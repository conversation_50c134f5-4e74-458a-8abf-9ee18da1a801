(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5055],{6560:(e,t,s)=>{"use strict";s.d(t,{r:()=>c});var r=s(95155),a=s(12115),l=s(30285),i=s(50172),n=s(59434);let c=a.forwardRef((e,t)=>{let{actionType:s="primary",icon:a,isLoading:c=!1,loadingText:d,className:o,children:m,disabled:x,asChild:u=!1,...h}=e,{variant:g,className:p}={primary:{variant:"default",className:"shadow-md"},secondary:{variant:"secondary",className:""},tertiary:{variant:"outline",className:""},danger:{variant:"destructive",className:"shadow-md"}}[s];return(0,r.jsx)(l.$,{ref:t,variant:g,className:(0,n.cn)(p,o),disabled:c||x,asChild:u,...h,children:c?(0,r.jsxs)("span",{className:"inline-flex items-center",children:[" ",(0,r.jsx)(i.A,{className:"mr-2 h-4 w-4 animate-spin"}),d||m]}):(0,r.jsxs)("span",{className:"inline-flex items-center",children:[" ",a&&(0,r.jsx)("span",{className:"mr-2",children:a}),m]})})});c.displayName="ActionButton"},6654:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useMergedRef",{enumerable:!0,get:function(){return a}});let r=s(12115);function a(e,t){let s=(0,r.useRef)(null),a=(0,r.useRef)(null);return(0,r.useCallback)(r=>{if(null===r){let e=s.current;e&&(s.current=null,e());let t=a.current;t&&(a.current=null,t())}else e&&(s.current=l(e,r)),t&&(a.current=l(t,r))},[e,t])}function l(e,t){if("function"!=typeof e)return e.current=t,()=>{e.current=null};{let s=e(t);return"function"==typeof s?s:()=>e(null)}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},11133:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(40157).A)("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},16059:(e,t,s)=>{Promise.resolve().then(s.bind(s,59965))},26126:(e,t,s)=>{"use strict";s.d(t,{E:()=>n});var r=s(95155);s(12115);var a=s(74466),l=s(59434);let i=(0,a.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function n(e){let{className:t,variant:s,...a}=e;return(0,r.jsx)("div",{className:(0,l.cn)(i({variant:s}),t),...a})}},35695:(e,t,s)=>{"use strict";var r=s(18999);s.o(r,"useParams")&&s.d(t,{useParams:function(){return r.useParams}}),s.o(r,"usePathname")&&s.d(t,{usePathname:function(){return r.usePathname}}),s.o(r,"useRouter")&&s.d(t,{useRouter:function(){return r.useRouter}}),s.o(r,"useSearchParams")&&s.d(t,{useSearchParams:function(){return r.useSearchParams}})},37648:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(40157).A)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},50172:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(40157).A)("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},55365:(e,t,s)=>{"use strict";s.d(t,{Fc:()=>c,TN:()=>o,XL:()=>d});var r=s(95155),a=s(12115),l=s(74466),i=s(59434);let n=(0,l.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),c=a.forwardRef((e,t)=>{let{className:s,variant:a,...l}=e;return(0,r.jsx)("div",{ref:t,role:"alert",className:(0,i.cn)(n({variant:a}),s),...l})});c.displayName="Alert";let d=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("h5",{ref:t,className:(0,i.cn)("mb-1 font-medium leading-none tracking-tight",s),...a})});d.displayName="AlertTitle";let o=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("div",{ref:t,className:(0,i.cn)("text-sm [&_p]:leading-relaxed",s),...a})});o.displayName="AlertDescription"},58260:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(40157).A)("Plane",[["path",{d:"M17.8 19.2 16 11l3.5-3.5C21 6 21.5 4 21 3c-1-.5-3 0-4.5 1.5L13 8 4.8 6.2c-.5-.1-.9.1-1.1.5l-.3.5c-.2.5-.1 1 .3 1.3L9 12l-2 3H4l-1 1 3 2 2 3 1-1v-3l3-2 3.5 5.3c.******* 1.3.3l.5-.2c.4-.3.6-.7.5-1.2z",key:"1v9wt8"}]])},59965:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>y});var r=s(95155),a=s(12115),l=s(35695),i=s(2730),n=s(66766),c=s(58260),d=s(37648),o=s(83662),m=s(39798),x=s(73168),u=s(83343),h=s(26126),g=s(59434),p=s(99673),f=s(77023);let j=function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(!e)return"N/A";try{return(0,x.GP)((0,u.H)(e),t?"MMM d, yyyy, HH:mm":"MMM d, yyyy")}catch(e){return"Invalid Date"}},N=e=>{switch(e){case"Planned":return"bg-blue-100 text-blue-800 border-blue-300";case"Confirmed":return"bg-green-100 text-green-800 border-green-300";case"In_Progress":return"bg-yellow-100 text-yellow-800 border-yellow-300";case"Completed":return"bg-purple-100 text-purple-800 border-purple-300";case"Cancelled":return"bg-red-100 text-red-800 border-red-300";default:return"bg-gray-100 text-gray-800 border-gray-300"}};function y(){let e=(0,l.useParams)(),[t,s]=(0,a.useState)(null),[x,u]=(0,a.useState)(!0),y=e.id,b=async()=>{u(!0);try{if(y){let e=await (0,i.getDelegationById)(y);e&&(s(e),document.title="".concat(e.eventName," - Delegation Report"))}}catch(e){console.error("Error fetching delegation for report:",e)}finally{u(!1)}};return((0,a.useEffect)(()=>{b()},[y]),x)?(0,r.jsxs)("div",{className:"max-w-4xl mx-auto p-4",children:[(0,r.jsx)(f.jt,{variant:"card",count:1}),(0,r.jsx)(f.jt,{variant:"table",count:3,className:"mt-6"}),(0,r.jsx)(f.jt,{variant:"table",count:2,className:"mt-6"})]}):t?(0,r.jsxs)("div",{className:"max-w-4xl mx-auto bg-white p-2 sm:p-4 text-gray-800",children:[(0,r.jsx)("div",{className:"text-right mb-4 no-print",children:(0,r.jsx)(m.k,{reportContentId:"#delegation-report-content",tableId:"#delegates-table",fileName:"delegation-report-".concat(t.eventName.replace(/\s+/g,"-")),enableCsv:t.delegates.length>0||t.statusHistory&&t.statusHistory.length>0})}),(0,r.jsxs)("div",{id:"delegation-report-content",className:"report-content",children:[(0,r.jsxs)("header",{className:"text-center mb-8 pb-4 border-b-2 border-gray-300",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-800",children:"Delegation Report"}),(0,r.jsx)("p",{className:"text-xl text-gray-600",children:t.eventName}),(0,r.jsxs)(h.E,{className:(0,g.cn)("mt-2 text-sm py-1 px-3 font-semibold",N(t.status)),children:["Status: ",(0,p.fZ)(t.status)]})]}),(0,r.jsxs)("section",{className:"mb-6 card-print p-4 border border-gray-200 rounded",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold text-gray-700 mb-3 pb-2 border-b border-gray-200",children:"Delegation Summary"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-x-4 gap-y-2 text-sm",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("strong",{children:"Event Name:"})," ",t.eventName]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("strong",{children:"Location:"})," ",t.location]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("strong",{children:"Duration:"})," ",j(t.durationFrom)," ","to ",j(t.durationTo)]}),t.invitationFrom&&(0,r.jsxs)("div",{children:[(0,r.jsx)("strong",{children:"Invitation From:"})," ",t.invitationFrom]}),t.invitationTo&&(0,r.jsxs)("div",{children:[(0,r.jsx)("strong",{children:"Invitation To:"})," ",t.invitationTo]})]}),t.imageUrl&&(0,r.jsx)("div",{className:"mt-4 relative aspect-[16/9] w-full max-w-md mx-auto overflow-hidden rounded no-print",children:(0,r.jsx)(n.default,{src:t.imageUrl,alt:t.eventName,layout:"fill",objectFit:"contain","data-ai-hint":"event placeholder"})}),t.notes&&(0,r.jsxs)("div",{className:"mt-3 text-sm",children:[(0,r.jsx)("strong",{children:"Notes:"})," ",(0,r.jsx)("span",{className:"italic",children:t.notes})]})]}),(0,r.jsxs)("section",{className:"mb-6 card-print p-4 border border-gray-200 rounded",children:[(0,r.jsxs)("h2",{className:"text-xl font-semibold text-gray-700 mb-3 pb-2 border-b border-gray-200",children:["Delegates (",t.delegates.length,")"]}),t.delegates.length>0?(0,r.jsxs)("table",{id:"delegates-table",className:"w-full text-sm text-left text-gray-600",children:[(0,r.jsx)("thead",{className:"text-xs text-gray-700 uppercase bg-gray-50",children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{scope:"col",className:"px-3 py-2",children:"Name"}),(0,r.jsx)("th",{scope:"col",className:"px-3 py-2",children:"Title"}),(0,r.jsx)("th",{scope:"col",className:"px-3 py-2",children:"Notes"})]})}),(0,r.jsx)("tbody",{children:t.delegates.map(e=>(0,r.jsxs)("tr",{className:"bg-white border-b hover:bg-gray-50",children:[(0,r.jsx)("td",{className:"px-3 py-2 font-medium",children:e.name}),(0,r.jsx)("td",{className:"px-3 py-2",children:e.title}),(0,r.jsx)("td",{className:"px-3 py-2",children:e.notes||"-"})]},e.id))})]}):(0,r.jsx)("p",{className:"text-gray-500 text-sm",children:"No delegates listed."})]}),(t.flightArrivalDetails||t.flightDepartureDetails)&&(0,r.jsxs)("section",{className:"mb-6 card-print p-4 border border-gray-200 rounded",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold text-gray-700 mb-3 pb-2 border-b border-gray-200",children:"Flight Information"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 text-sm",children:[t.flightArrivalDetails&&(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-semibold text-md mb-1",children:"Arrival Details"}),(0,r.jsxs)("p",{children:[(0,r.jsx)(c.A,{className:"inline h-4 w-4 mr-1"}),(0,r.jsx)("strong",{children:"Flight:"})," ",t.flightArrivalDetails.flightNumber]}),(0,r.jsxs)("p",{children:[(0,r.jsx)(d.A,{className:"inline h-4 w-4 mr-1"}),(0,r.jsx)("strong",{children:"Time:"})," ",j(t.flightArrivalDetails.dateTime,!0)]}),(0,r.jsxs)("p",{children:[(0,r.jsx)(o.A,{className:"inline h-4 w-4 mr-1"}),(0,r.jsx)("strong",{children:"Airport:"})," ",t.flightArrivalDetails.airport," ",t.flightArrivalDetails.terminal&&"(Terminal ".concat(t.flightArrivalDetails.terminal,")")]}),t.flightArrivalDetails.notes&&(0,r.jsxs)("p",{className:"mt-1 text-xs italic",children:[(0,r.jsx)("strong",{children:"Notes:"})," ",t.flightArrivalDetails.notes]})]}),t.flightDepartureDetails&&(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-semibold text-md mb-1",children:"Departure Details"}),(0,r.jsxs)("p",{children:[(0,r.jsx)(c.A,{className:"inline h-4 w-4 mr-1"}),(0,r.jsx)("strong",{children:"Flight:"})," ",t.flightDepartureDetails.flightNumber]}),(0,r.jsxs)("p",{children:[(0,r.jsx)(d.A,{className:"inline h-4 w-4 mr-1"}),(0,r.jsx)("strong",{children:"Time:"})," ",j(t.flightDepartureDetails.dateTime,!0)]}),(0,r.jsxs)("p",{children:[(0,r.jsx)(o.A,{className:"inline h-4 w-4 mr-1"}),(0,r.jsx)("strong",{children:"Airport:"})," ",t.flightDepartureDetails.airport," ",t.flightDepartureDetails.terminal&&"(Terminal ".concat(t.flightDepartureDetails.terminal,")")]}),t.flightDepartureDetails.notes&&(0,r.jsxs)("p",{className:"mt-1 text-xs italic",children:[(0,r.jsx)("strong",{children:"Notes:"})," ",t.flightDepartureDetails.notes]})]})]}),!t.flightArrivalDetails&&!t.flightDepartureDetails&&(0,r.jsx)("p",{className:"text-gray-500 text-sm",children:"No flight details logged."})]}),(0,r.jsxs)("section",{className:"card-print p-4 border border-gray-200 rounded",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold text-gray-700 mb-3 pb-2 border-b border-gray-200",children:"Status History"}),t.statusHistory&&t.statusHistory.length>0?(0,r.jsxs)("table",{id:"status-history-table",className:"w-full text-sm text-left text-gray-600",children:[(0,r.jsx)("thead",{className:"text-xs text-gray-700 uppercase bg-gray-50",children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{scope:"col",className:"px-3 py-2",children:"Status"}),(0,r.jsx)("th",{scope:"col",className:"px-3 py-2",children:"Changed At"}),(0,r.jsx)("th",{scope:"col",className:"px-3 py-2",children:"Reason"})]})}),(0,r.jsx)("tbody",{children:t.statusHistory.slice().sort((e,t)=>new Date(t.changedAt).getTime()-new Date(e.changedAt).getTime()).map(e=>(0,r.jsxs)("tr",{className:"bg-white border-b hover:bg-gray-50",children:[(0,r.jsx)("td",{className:"px-3 py-2",children:(0,r.jsx)(h.E,{className:(0,g.cn)("text-xs py-0.5 px-1.5",N(e.status)),children:(0,p.fZ)(e.status)})}),(0,r.jsx)("td",{className:"px-3 py-2",children:j(e.changedAt,!0)}),(0,r.jsx)("td",{className:"px-3 py-2",children:e.reason||"-"})]},e.id))})]}):(0,r.jsx)("p",{className:"text-gray-500 text-sm",children:"No status history available."})]}),(0,r.jsxs)("footer",{className:"mt-10 pt-4 border-t-2 border-gray-300 text-center text-xs text-gray-500",children:[(0,r.jsxs)("p",{children:["Report generated on: ",new Date().toLocaleDateString()]}),(0,r.jsx)("p",{children:"WorkHub - Delegation Management"})]})]})]}):(0,r.jsx)("div",{className:"text-center py-10",children:"Delegation not found."})}},68856:(e,t,s)=>{"use strict";s.d(t,{E:()=>l});var r=s(95155),a=s(59434);function l(e){let{className:t,...s}=e;return(0,r.jsx)("div",{className:(0,a.cn)("animate-pulse rounded-md bg-muted",t),...s})}},77023:(e,t,s)=>{"use strict";s.d(t,{gO:()=>g,jt:()=>u});var r=s(95155);s(12115);var a=s(50172),l=s(11133),i=s(59434),n=s(68856),c=s(55365),d=s(6560);let o={sm:"h-4 w-4",md:"h-6 w-6",lg:"h-8 w-8",xl:"h-12 w-12"},m={sm:"text-xs",md:"text-sm",lg:"text-base",xl:"text-lg"};function x(e){let{size:t="md",className:s,text:l,fullPage:n=!1}=e;return(0,r.jsx)("div",{className:(0,i.cn)("flex items-center justify-center",n&&"fixed inset-0 bg-background/80 backdrop-blur-sm z-50",s),children:(0,r.jsxs)("div",{className:"flex flex-col items-center",children:[(0,r.jsx)(a.A,{className:(0,i.cn)("animate-spin text-primary",o[t])}),l&&(0,r.jsx)("span",{className:(0,i.cn)("mt-2 text-muted-foreground",m[t]),children:l})]})})}function u(e){let{variant:t="default",count:s=1,className:a,testId:l="loading-skeleton"}=e;return"card"===t?(0,r.jsx)("div",{className:(0,i.cn)("grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",a),"data-testid":l,children:Array(s).fill(0).map((e,t)=>(0,r.jsxs)("div",{className:"overflow-hidden shadow-md rounded-lg border bg-card",children:[(0,r.jsx)(n.E,{className:"aspect-[16/10] w-full"}),(0,r.jsxs)("div",{className:"p-5",children:[(0,r.jsx)(n.E,{className:"h-7 w-3/4 mb-1"}),(0,r.jsx)(n.E,{className:"h-4 w-1/2 mb-3"}),(0,r.jsx)(n.E,{className:"h-px w-full my-3"}),(0,r.jsx)("div",{className:"space-y-2.5",children:[,,,].fill(0).map((e,t)=>(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(n.E,{className:"mr-2.5 h-5 w-5 rounded-full"}),(0,r.jsx)(n.E,{className:"h-5 w-2/3"})]},t))})]})]},t))}):"table"===t?(0,r.jsxs)("div",{className:(0,i.cn)("space-y-3",a),"data-testid":l,children:[(0,r.jsx)("div",{className:"flex gap-4",children:[,,,].fill(0).map((e,t)=>(0,r.jsx)(n.E,{className:"h-8 flex-1"},t))}),Array(s).fill(0).map((e,t)=>(0,r.jsx)("div",{className:"flex gap-4",children:[,,,].fill(0).map((e,t)=>(0,r.jsx)(n.E,{className:"h-6 flex-1"},t))},t))]}):"list"===t?(0,r.jsx)("div",{className:(0,i.cn)("space-y-3",a),"data-testid":l,children:Array(s).fill(0).map((e,t)=>(0,r.jsxs)("div",{className:"flex items-center gap-4",children:[(0,r.jsx)(n.E,{className:"h-12 w-12 rounded-full"}),(0,r.jsxs)("div",{className:"space-y-2 flex-1",children:[(0,r.jsx)(n.E,{className:"h-4 w-1/3"}),(0,r.jsx)(n.E,{className:"h-4 w-full"})]})]},t))}):"stats"===t?(0,r.jsx)("div",{className:(0,i.cn)("grid gap-4 md:grid-cols-2 lg:grid-cols-3",a),"data-testid":l,children:Array(s).fill(0).map((e,t)=>(0,r.jsxs)("div",{className:"rounded-lg border bg-card p-5 shadow-sm",children:[(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)(n.E,{className:"h-5 w-1/3"}),(0,r.jsx)(n.E,{className:"h-5 w-5 rounded-full"})]}),(0,r.jsx)(n.E,{className:"h-8 w-1/2 mt-3"}),(0,r.jsx)(n.E,{className:"h-4 w-2/3 mt-2"})]},t))}):(0,r.jsx)("div",{className:(0,i.cn)("space-y-2",a),"data-testid":l,children:Array(s).fill(0).map((e,t)=>(0,r.jsx)(n.E,{className:"w-full h-5"},t))})}function h(e){let{message:t,onRetry:s,className:n}=e;return(0,r.jsxs)(c.Fc,{variant:"destructive",className:(0,i.cn)("my-4",n),children:[(0,r.jsx)(l.A,{className:"h-4 w-4"}),(0,r.jsx)(c.XL,{children:"Error"}),(0,r.jsx)(c.TN,{children:(0,r.jsxs)("div",{className:"mt-2",children:[(0,r.jsx)("p",{className:"text-sm text-muted-foreground mb-4",children:t}),s&&(0,r.jsx)(d.r,{actionType:"tertiary",size:"sm",onClick:s,icon:(0,r.jsx)(a.A,{className:"h-4 w-4"}),children:"Try Again"})]})})]})}function g(e){let{isLoading:t,error:s,data:a,onRetry:l,children:n,loadingComponent:c,errorComponent:d,emptyComponent:o,className:m}=e;return t?c||(0,r.jsx)(x,{className:m,text:"Loading..."}):s?d||(0,r.jsx)(h,{message:s,onRetry:l,className:m}):!a||Array.isArray(a)&&0===a.length?o||(0,r.jsx)("div",{className:(0,i.cn)("text-center py-8",m),children:(0,r.jsx)("p",{className:"text-muted-foreground",children:"No data available"})}):(0,r.jsx)("div",{className:m,children:n(a)})}},83662:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(40157).A)("MapPin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},99673:(e,t,s)=>{"use strict";function r(e){switch(e){case"In_Progress":return"In Progress";case"No_details":return"No Details";default:return e}}s.d(t,{fZ:()=>r})}},e=>{var t=t=>e(e.s=t);e.O(0,[3796,6113,832,2688,2512,6766,8782,6045,8162,2730,9798,8441,1684,7358],()=>t(16059)),_N_E=e.O()}]);