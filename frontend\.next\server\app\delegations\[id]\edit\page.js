(()=>{var e={};e.id=9977,e.ids=[9977],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26067:(e,s,r)=>{Promise.resolve().then(r.bind(r,71321))},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},50269:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>d});var t=r(65239),a=r(48088),i=r(88170),n=r.n(i),l=r(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);r.d(s,o);let d={children:["",{children:["delegations",{children:["[id]",{children:["edit",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,79772)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\delegations\\[id]\\edit\\page.tsx"]}]},{}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\delegations\\[id]\\edit\\page.tsx"],m={require:r,loadChunk:()=>Promise.resolve()},u=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/delegations/[id]/edit/page",pathname:"/delegations/[id]/edit",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},52027:(e,s,r)=>{"use strict";r.d(s,{gO:()=>h,jt:()=>p});var t=r(60687);r(43210);var a=r(11516),i=r(72963),n=r(4780),l=r(85726),o=r(91821),d=r(68752);let c={sm:"h-4 w-4",md:"h-6 w-6",lg:"h-8 w-8",xl:"h-12 w-12"},m={sm:"text-xs",md:"text-sm",lg:"text-base",xl:"text-lg"};function u({size:e="md",className:s,text:r,fullPage:i=!1}){return(0,t.jsx)("div",{className:(0,n.cn)("flex items-center justify-center",i&&"fixed inset-0 bg-background/80 backdrop-blur-sm z-50",s),children:(0,t.jsxs)("div",{className:"flex flex-col items-center",children:[(0,t.jsx)(a.A,{className:(0,n.cn)("animate-spin text-primary",c[e])}),r&&(0,t.jsx)("span",{className:(0,n.cn)("mt-2 text-muted-foreground",m[e]),children:r})]})})}function p({variant:e="default",count:s=1,className:r,testId:a="loading-skeleton"}){return"card"===e?(0,t.jsx)("div",{className:(0,n.cn)("grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",r),"data-testid":a,children:Array(s).fill(0).map((e,s)=>(0,t.jsxs)("div",{className:"overflow-hidden shadow-md rounded-lg border bg-card",children:[(0,t.jsx)(l.E,{className:"aspect-[16/10] w-full"}),(0,t.jsxs)("div",{className:"p-5",children:[(0,t.jsx)(l.E,{className:"h-7 w-3/4 mb-1"}),(0,t.jsx)(l.E,{className:"h-4 w-1/2 mb-3"}),(0,t.jsx)(l.E,{className:"h-px w-full my-3"}),(0,t.jsx)("div",{className:"space-y-2.5",children:[,,,].fill(0).map((e,s)=>(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(l.E,{className:"mr-2.5 h-5 w-5 rounded-full"}),(0,t.jsx)(l.E,{className:"h-5 w-2/3"})]},s))})]})]},s))}):"table"===e?(0,t.jsxs)("div",{className:(0,n.cn)("space-y-3",r),"data-testid":a,children:[(0,t.jsx)("div",{className:"flex gap-4",children:[,,,].fill(0).map((e,s)=>(0,t.jsx)(l.E,{className:"h-8 flex-1"},s))}),Array(s).fill(0).map((e,s)=>(0,t.jsx)("div",{className:"flex gap-4",children:[,,,].fill(0).map((e,s)=>(0,t.jsx)(l.E,{className:"h-6 flex-1"},s))},s))]}):"list"===e?(0,t.jsx)("div",{className:(0,n.cn)("space-y-3",r),"data-testid":a,children:Array(s).fill(0).map((e,s)=>(0,t.jsxs)("div",{className:"flex items-center gap-4",children:[(0,t.jsx)(l.E,{className:"h-12 w-12 rounded-full"}),(0,t.jsxs)("div",{className:"space-y-2 flex-1",children:[(0,t.jsx)(l.E,{className:"h-4 w-1/3"}),(0,t.jsx)(l.E,{className:"h-4 w-full"})]})]},s))}):"stats"===e?(0,t.jsx)("div",{className:(0,n.cn)("grid gap-4 md:grid-cols-2 lg:grid-cols-3",r),"data-testid":a,children:Array(s).fill(0).map((e,s)=>(0,t.jsxs)("div",{className:"rounded-lg border bg-card p-5 shadow-sm",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)(l.E,{className:"h-5 w-1/3"}),(0,t.jsx)(l.E,{className:"h-5 w-5 rounded-full"})]}),(0,t.jsx)(l.E,{className:"h-8 w-1/2 mt-3"}),(0,t.jsx)(l.E,{className:"h-4 w-2/3 mt-2"})]},s))}):(0,t.jsx)("div",{className:(0,n.cn)("space-y-2",r),"data-testid":a,children:Array(s).fill(0).map((e,s)=>(0,t.jsx)(l.E,{className:"w-full h-5"},s))})}function x({message:e,onRetry:s,className:r}){return(0,t.jsxs)(o.Fc,{variant:"destructive",className:(0,n.cn)("my-4",r),children:[(0,t.jsx)(i.A,{className:"h-4 w-4"}),(0,t.jsx)(o.XL,{children:"Error"}),(0,t.jsx)(o.TN,{children:(0,t.jsxs)("div",{className:"mt-2",children:[(0,t.jsx)("p",{className:"text-sm text-muted-foreground mb-4",children:e}),s&&(0,t.jsx)(d.r,{actionType:"tertiary",size:"sm",onClick:s,icon:(0,t.jsx)(a.A,{className:"h-4 w-4"}),children:"Try Again"})]})})]})}function h({isLoading:e,error:s,data:r,onRetry:a,children:i,loadingComponent:l,errorComponent:o,emptyComponent:d,className:c}){return e?l||(0,t.jsx)(u,{className:c,text:"Loading..."}):s?o||(0,t.jsx)(x,{message:s,onRetry:a,className:c}):!r||Array.isArray(r)&&0===r.length?d||(0,t.jsx)("div",{className:(0,n.cn)("text-center py-8",c),children:(0,t.jsx)("p",{className:"text-muted-foreground",children:"No data available"})}):(0,t.jsx)("div",{className:c,children:i(r)})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},71321:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>g});var t=r(60687),a=r(43210),i=r(16189),n=r(97521),l=r(28840),o=r(48041),d=r(33886),c=r(14975),m=r(55817),u=r(29867),p=r(52027),x=r(95758),h=r(68752);function g(){let e=(0,i.useRouter)(),s=(0,i.useParams)(),{toast:r}=(0,u.dj)(),[g,f]=(0,a.useState)(null),[j,v]=(0,a.useState)(!0),[y,N]=(0,a.useState)(null),[b,w]=(0,a.useState)(!1),E=s.id,k=(0,a.useCallback)(async()=>{v(!0),N(null);try{if(E){let e=await (0,l.getDelegationById)(E);e?f(e):(N("Delegation not found"),r({title:"Error",description:"Delegation not found.",variant:"destructive"}))}}catch(s){console.error("Error fetching delegation:",s);let e=s instanceof Error?s.message:"Failed to load delegation";N(e),r({title:"Error",description:e,variant:"destructive"})}finally{v(!1)}},[E,r]),A=async s=>{if(E){w(!0),N(null);try{await (0,l.updateDelegation)(E,s),r({title:"Delegation Updated",description:`The delegation "${s.eventName}" has been successfully updated.`,variant:"default"}),e.push(`/delegations/${E}`)}catch(s){console.error("Error updating delegation:",s);let e="Failed to update delegation";if(s.validationErrors&&Array.isArray(s.validationErrors)){let r=s.validationErrors.map(e=>`${e.path}: ${e.message}`).join(", ");e=`Validation failed: ${r}`}else s.message&&(e=s.message);N(e),r({title:"Error",description:e,variant:"destructive"})}finally{w(!1)}}};return(0,t.jsx)(x.A,{children:(0,t.jsx)(p.gO,{isLoading:j,error:y,data:g,onRetry:k,loadingComponent:(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)(o.z,{title:"Loading Delegation...",icon:d.A}),(0,t.jsx)(p.jt,{variant:"card",count:1})," "]}),emptyComponent:(0,t.jsxs)("div",{className:"space-y-6 text-center",children:[(0,t.jsx)(o.z,{title:"Delegation Not Found",icon:c.A}),(0,t.jsx)("p",{children:"The requested delegation could not be found."}),(0,t.jsx)(h.r,{actionType:"primary",onClick:()=>e.push("/delegations"),icon:(0,t.jsx)(m.A,{className:"h-4 w-4"}),children:"Back to Delegations"})]}),children:e=>(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)(o.z,{title:`Edit Delegation: ${e.eventName}`,description:"Modify the details for this delegation or event.",icon:d.A}),(0,t.jsx)(n.A,{onSubmit:A,initialData:e,isEditing:!0,isSubmitting:b})]})})})}},72923:(e,s,r)=>{Promise.resolve().then(r.bind(r,79772))},74075:e=>{"use strict";e.exports=require("zlib")},77368:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(82614).A)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79772:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\projects\\\\CarServiceTrackingSystem\\\\TrackerSystem\\\\frontend\\\\src\\\\app\\\\delegations\\\\[id]\\\\edit\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\delegations\\[id]\\edit\\page.tsx","default")},81630:e=>{"use strict";e.exports=require("http")},85726:(e,s,r)=>{"use strict";r.d(s,{E:()=>i});var t=r(60687),a=r(4780);function i({className:e,...s}){return(0,t.jsx)("div",{className:(0,a.cn)("animate-pulse rounded-md bg-muted",e),...s})}},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},95758:(e,s,r)=>{"use strict";r.d(s,{A:()=>d});var t=r(60687),a=r(43210),i=r(91821),n=r(29523),l=r(77368);class o extends a.Component{constructor(e){super(e),this.handleRetry=()=>{this.setState({hasError:!1,error:null,errorInfo:null})},this.state={hasError:!1,error:null,errorInfo:null}}static getDerivedStateFromError(e){return{hasError:!0,error:e,errorInfo:null}}componentDidCatch(e,s){console.error("Error caught by ErrorBoundary:",e),console.error("Component stack:",s.componentStack),this.setState({errorInfo:s})}render(){return this.state.hasError?this.props.fallback?this.props.fallback:(0,t.jsxs)(i.Fc,{variant:"destructive",className:"my-4",children:[(0,t.jsx)(i.XL,{className:"text-lg font-semibold",children:"Something went wrong"}),(0,t.jsxs)(i.TN,{className:"mt-2",children:[(0,t.jsx)("p",{className:"mb-2",children:this.state.error?.message||"An unexpected error occurred"}),this.state.error?.stack&&(0,t.jsxs)("details",{className:"mt-2 text-xs",children:[(0,t.jsx)("summary",{children:"Error details"}),(0,t.jsx)("pre",{className:"mt-2 whitespace-pre-wrap overflow-auto max-h-[200px] p-2 bg-slate-100 dark:bg-slate-900 rounded",children:this.state.error.stack})]}),(0,t.jsxs)(n.$,{variant:"outline",size:"sm",onClick:this.handleRetry,className:"mt-4",children:[(0,t.jsx)(l.A,{className:"mr-2 h-4 w-4"}),"Try Again"]})]})]}):this.props.children}}let d=o}};var s=require("../../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[4447,3622,1658,5880,2729,3442,8054,4418,8141,3983,7497],()=>r(50269));module.exports=t})();