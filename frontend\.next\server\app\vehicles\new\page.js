(()=>{var e={};e.id=3667,e.ids=[3667],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26373:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>m});var s=t(60687),i=t(43210),a=t(16189),n=t(95950),l=t(28840),o=t(48041),d=t(24920),c=t(29867);let m=()=>{let e=(0,a.useRouter)(),{toast:r}=(0,c.dj)(),[t,m]=(0,i.useState)(!1),[p,x]=(0,i.useState)(null),h=async t=>{m(!0),x(null);try{let s={make:t.make,model:t.model,year:t.year,vin:t.vin,licensePlate:t.licensePlate,ownerName:t.ownerName,ownerContact:t.ownerContact,initialOdometer:void 0===t.initialOdometer?0:t.initialOdometer,color:t.color,imageUrl:t.imageUrl},i=await (0,l.addVehicle)(s);r({title:"Vehicle Added",description:`${i.make} ${i.model} has been successfully added.`,variant:"default"}),e.push("/vehicles")}catch(e){console.error("Failed to add vehicle:",e),x(e.message||"An unexpected error occurred. Please try again."),r({title:"Error Adding Vehicle",description:e.message||"Could not add the vehicle. Please check the details and try again.",variant:"destructive"})}finally{m(!1)}};return(0,s.jsxs)("div",{className:"container mx-auto py-8 space-y-8",children:[(0,s.jsx)(o.z,{title:"Add New Vehicle",description:"Enter the details for your new vehicle.",icon:d.A}),p&&(0,s.jsxs)("p",{className:"text-red-500 bg-red-100 p-3 rounded-md",children:["Error: ",p]}),(0,s.jsx)(n.A,{onSubmit:h,isEditing:!1,isLoading:t})]})}},27472:(e,r,t)=>{Promise.resolve().then(t.bind(t,26373))},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},48041:(e,r,t)=>{"use strict";t.d(r,{z:()=>i});var s=t(60687);function i({title:e,description:r,icon:t,children:i}){return(0,s.jsxs)("div",{className:"mb-6 pb-4 border-b border-border/50 flex justify-between items-center",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[t&&(0,s.jsx)(t,{className:"h-8 w-8 text-primary"}),(0,s.jsx)("h1",{className:"text-3xl font-bold tracking-tight text-foreground",children:e})]}),r&&(0,s.jsx)("p",{className:"text-muted-foreground mt-1",children:r})]}),i&&(0,s.jsx)("div",{className:"flex items-center gap-2",children:i})]})}t(43210)},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},55817:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(82614).A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},56729:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>p,tree:()=>d});var s=t(65239),i=t(48088),a=t(88170),n=t.n(a),l=t(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);t.d(r,o);let d={children:["",{children:["vehicles",{children:["new",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,81043)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\vehicles\\new\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\vehicles\\new\\page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/vehicles/new/page",pathname:"/vehicles/new",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81043:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\projects\\\\CarServiceTrackingSystem\\\\TrackerSystem\\\\frontend\\\\src\\\\app\\\\vehicles\\\\new\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\vehicles\\new\\page.tsx","default")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},92624:(e,r,t)=>{Promise.resolve().then(t.bind(t,81043))},94735:e=>{"use strict";e.exports=require("events")},95950:(e,r,t)=>{"use strict";t.d(r,{A:()=>g});var s=t(60687),i=t(43210),a=t(16189),n=t(27605),l=t(63442),o=t(45880);let d=o.Ik({make:o.Yj().min(1,"Make is required"),model:o.Yj().min(1,"Model is required"),year:o.au.number().min(1900,"Year must be 1900 or later").max(new Date().getFullYear()+1,`Year cannot be more than ${new Date().getFullYear()+1}`),vin:o.Yj().min(1,"VIN is required").regex(/^[A-HJ-NPR-Z0-9]{17}$/,"VIN must be a valid 17-character format (only capital letters A-H, J-N, P-R, Z and numbers 0-9)"),licensePlate:o.Yj().min(1,"License plate is required"),ownerName:o.Yj().min(1,"Owner name is required"),ownerContact:o.Yj().min(1,"Owner contact is required"),color:o.Yj().optional(),initialOdometer:o.au.number().min(0,"Odometer reading cannot be negative").optional(),imageUrl:o.Yj().url("Invalid image URL").optional().or(o.eu(""))});var c=t(29523),m=t(89667),p=t(80013),x=t(44493),h=t(29867),u=t(55817);let g=({onSubmit:e,initialData:r={},isEditing:t=!1,submitButtonText:o=t?"Save Changes":"Create Vehicle",isLoading:g=!1})=>{let j=(0,a.useRouter)(),{toast:v}=(0,h.dj)(),{register:y,handleSubmit:w,formState:{errors:f,isSubmitting:N},reset:b,setValue:k}=(0,n.mN)({resolver:(0,l.u)(d),defaultValues:{make:r?.make||"",model:r?.model||"",year:r?.year||new Date().getFullYear(),vin:r?.vin||"",licensePlate:r?.licensePlate||"",ownerName:r?.ownerName||"",ownerContact:r?.ownerContact||"",color:r?.color||"",initialOdometer:r?.initialOdometer??0,imageUrl:r?.imageUrl||""}});(0,i.useEffect)(()=>{r&&(k("make",r.make||""),k("model",r.model||""),k("year",r.year||new Date().getFullYear()),k("vin",r.vin||""),k("licensePlate",r.licensePlate||""),k("ownerName",r.ownerName||""),k("ownerContact",r.ownerContact||""),k("color",r.color||""),k("initialOdometer",r.initialOdometer??0),k("imageUrl",r.imageUrl||""))},[r,k]);let C=async r=>{await e(r),v({title:t?"Vehicle Updated":"Vehicle Added",description:`${r.make} ${r.model} has been successfully ${t?"updated":"added"}.`,variant:"default"})};return(0,s.jsxs)(x.Zp,{className:"max-w-2xl mx-auto",children:[(0,s.jsxs)(x.aR,{children:[(0,s.jsx)(x.ZB,{children:t?"Edit Vehicle":"Add New Vehicle"}),(0,s.jsx)(x.BT,{children:t?"Update the details of the vehicle.":"Enter the details for the new vehicle."})]}),(0,s.jsxs)("form",{onSubmit:w(C),children:[(0,s.jsxs)(x.Wu,{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(p.J,{htmlFor:"make",children:"Make"}),(0,s.jsx)(m.p,{id:"make",...y("make"),placeholder:"e.g., Toyota"}),f.make&&(0,s.jsx)("p",{className:"text-sm text-red-500 mt-1",children:f.make.message})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(p.J,{htmlFor:"model",children:"Model"}),(0,s.jsx)(m.p,{id:"model",...y("model"),placeholder:"e.g., Camry"}),f.model&&(0,s.jsx)("p",{className:"text-sm text-red-500 mt-1",children:f.model.message})]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(p.J,{htmlFor:"year",children:"Year"}),(0,s.jsx)(m.p,{id:"year",type:"number",...y("year"),placeholder:"e.g., 2023"}),f.year&&(0,s.jsx)("p",{className:"text-sm text-red-500 mt-1",children:f.year.message})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(p.J,{htmlFor:"vin",children:"VIN"}),(0,s.jsx)(m.p,{id:"vin",...y("vin"),placeholder:"Vehicle Identification Number"}),f.vin&&(0,s.jsx)("p",{className:"text-sm text-red-500 mt-1",children:f.vin.message}),(0,s.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"VIN must be exactly 17 characters, using capital letters A-H, J-N, P-R, Z and numbers 0-9."})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(p.J,{htmlFor:"licensePlate",children:"License Plate"}),(0,s.jsx)(m.p,{id:"licensePlate",...y("licensePlate"),placeholder:"e.g., ABC-123"}),f.licensePlate&&(0,s.jsx)("p",{className:"text-sm text-red-500 mt-1",children:f.licensePlate.message})]}),(0,s.jsx)("hr",{className:"my-6"}),(0,s.jsxs)("div",{children:[(0,s.jsx)(p.J,{htmlFor:"ownerName",children:"Owner Name"}),(0,s.jsx)(m.p,{id:"ownerName",...y("ownerName"),placeholder:"e.g., John Doe"}),f.ownerName&&(0,s.jsx)("p",{className:"text-sm text-red-500 mt-1",children:f.ownerName.message})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(p.J,{htmlFor:"ownerContact",children:"Owner Contact (Email/Phone)"}),(0,s.jsx)(m.p,{id:"ownerContact",...y("ownerContact"),placeholder:"e.g., <EMAIL> or 555-1234"}),f.ownerContact&&(0,s.jsx)("p",{className:"text-sm text-red-500 mt-1",children:f.ownerContact.message})]}),(0,s.jsx)("hr",{className:"my-6"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(p.J,{htmlFor:"color",children:"Color (Optional)"}),(0,s.jsx)(m.p,{id:"color",...y("color"),placeholder:"e.g., Blue"}),f.color&&(0,s.jsx)("p",{className:"text-sm text-red-500 mt-1",children:f.color.message})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(p.J,{htmlFor:"initialOdometer",children:"Initial Odometer (Optional)"}),(0,s.jsx)(m.p,{id:"initialOdometer",type:"number",...y("initialOdometer"),placeholder:"e.g., 100"}),f.initialOdometer&&(0,s.jsx)("p",{className:"text-sm text-red-500 mt-1",children:f.initialOdometer.message})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(p.J,{htmlFor:"imageUrl",children:"Image URL (Optional)"}),(0,s.jsx)(m.p,{id:"imageUrl",...y("imageUrl"),placeholder:"https://example.com/image.png"}),f.imageUrl&&(0,s.jsx)("p",{className:"text-sm text-red-500 mt-1",children:f.imageUrl.message})]})]}),(0,s.jsxs)(x.wL,{className:"flex justify-end space-x-3 pt-6",children:[(0,s.jsxs)(c.$,{type:"button",variant:"outline",onClick:()=>j.back(),disabled:N||g,children:[(0,s.jsx)(u.A,{className:"mr-2 h-4 w-4"}),"Cancel"]}),(0,s.jsx)(c.$,{type:"submit",disabled:N||g,children:N||g?"Processing...":o})]})]})]})}}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,3622,1658,5880,3442,8141,3983],()=>t(56729));module.exports=s})();