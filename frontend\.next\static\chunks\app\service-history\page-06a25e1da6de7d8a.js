(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2003],{3235:(e,r,s)=>{"use strict";s.d(r,{A:()=>a});let a=(0,s(40157).A)("Wrench",[["path",{d:"M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z",key:"cbrjhi"}]])},3638:(e,r,s)=>{"use strict";s.d(r,{A:()=>a});let a=(0,s(40157).A)("History",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}],["path",{d:"M12 7v5l4 2",key:"1fdv2h"}]])},25318:(e,r,s)=>{"use strict";s.d(r,{A:()=>a});let a=(0,s(40157).A)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},28328:(e,r,s)=>{"use strict";s.d(r,{A:()=>a});let a=(0,s(40157).A)("Car",[["path",{d:"M19 17h2c.6 0 1-.4 1-1v-3c0-.9-.7-1.7-1.5-1.9C18.7 10.6 16 10 16 10s-1.3-1.4-2.2-2.3c-.5-.4-1.1-.7-1.8-.7H5c-.6 0-1.1.4-1.4.9l-1.4 2.9A3.7 3.7 0 0 0 2 12v4c0 .6.4 1 1 1h2",key:"5owen"}],["circle",{cx:"7",cy:"17",r:"2",key:"u2ysq9"}],["path",{d:"M9 17h6",key:"r8uit2"}],["circle",{cx:"17",cy:"17",r:"2",key:"axvx0g"}]])},31949:(e,r,s)=>{"use strict";s.d(r,{A:()=>a});let a=(0,s(40157).A)("TriangleAlert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},34301:(e,r,s)=>{"use strict";s.d(r,{A:()=>a});let a=(0,s(40157).A)("CirclePlus",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M8 12h8",key:"1wcyev"}],["path",{d:"M12 8v8",key:"napkw2"}]])},40968:(e,r,s)=>{"use strict";s.d(r,{b:()=>c});var a=s(12115),t=s(63655),l=s(95155),i=a.forwardRef((e,r)=>(0,l.jsx)(t.sG.label,{...e,ref:r,onMouseDown:r=>{var s;r.target.closest("button, input, select, textarea")||(null==(s=e.onMouseDown)||s.call(e,r),!r.defaultPrevented&&r.detail>1&&r.preventDefault())}}));i.displayName="Label";var c=i},50594:(e,r,s)=>{"use strict";s.d(r,{A:()=>a});let a=(0,s(40157).A)("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]])},59142:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>V});var a=s(95155),t=s(11518),l=s.n(t),i=s(12115),c=s(2730),d=s(5611),n=s(95647),o=s(30285),h=s(62523),m=s(59409),p=s(66695),x=s(85057),u=s(26126),f=s(3638),v=s(34301),y=s(75074),j=s(25318),b=s(28328),N=s(3235),g=s(6874),w=s.n(g),k=s(68856),A=s(58824),S=s(55365),C=s(31949),E=s(67554);class R extends i.Component{static getDerivedStateFromError(e){return{hasError:!0,error:e}}componentDidCatch(e,r){this.setState({errorInfo:r}),console.error("Error caught by ErrorBoundary:",e),console.error("Component stack:",r.componentStack),this.props.onError&&this.props.onError(e,r)}render(){let{title:e="Something went wrong",description:r="An unexpected error occurred.",resetLabel:s="Try Again"}=this.props;if(this.state.hasError){var t;return this.props.fallback?this.props.fallback:(0,a.jsxs)(S.Fc,{variant:"destructive",className:"my-4",children:[(0,a.jsx)(C.A,{className:"h-4 w-4 mr-2"}),(0,a.jsx)(S.XL,{className:"text-lg font-semibold",children:e}),(0,a.jsxs)(S.TN,{className:"mt-2",children:[(0,a.jsx)("p",{className:"mb-2",children:(null==(t=this.state.error)?void 0:t.message)||r}),!1,(0,a.jsxs)(o.$,{variant:"outline",size:"sm",onClick:this.handleRetry,className:"mt-4",children:[(0,a.jsx)(E.A,{className:"mr-2 h-4 w-4"}),s]})]})]})}return this.props.children}constructor(e){super(e),this.handleRetry=()=>{this.setState({hasError:!1,error:null,errorInfo:null})},this.state={hasError:!1,error:null,errorInfo:null}}}let M=e=>{let{children:r,fallback:s}=e;return(0,a.jsx)(R,{title:"Error Loading Service Records",description:"An unexpected error occurred while loading service records.",resetLabel:"Try Again",fallback:s,onError:(e,r)=>{console.error("ServiceRecords component error:",e),console.error("Component stack:",r.componentStack)},children:r})};var F=s(6560),L=s(15080),T=s(39798);let P=["Oil Change","Tire Rotation","Brake Service","Battery Replacement","Air Filter Replacement","Cabin Air Filter Replacement","Wiper Blade Replacement","Fluid Check/Top-up","Spark Plug Replacement","Coolant Flush","Transmission Service","Wheel Alignment","State Inspection","Other"];function V(){var e,r,s,t;let[g,S]=(0,i.useState)([]),[C,E]=(0,i.useState)([]),[R,V]=(0,i.useState)([]),[q,z]=(0,i.useState)(!0),[I,D]=(0,i.useState)(!0),[H,J]=(0,i.useState)(null),[W,B]=(0,i.useState)("all"),[O,_]=(0,i.useState)("all"),[U,Z]=(0,i.useState)(""),[$,X]=(0,i.useState)(""),[G,Y]=(0,i.useState)([]),[K,Q]=(0,i.useState)(0);(0,i.useEffect)(()=>{let e=setTimeout(()=>{X(U)},300);return()=>clearTimeout(e)},[U]);let ee=(0,i.useCallback)(async()=>{z(!0);try{let e=await (0,c.getVehicles)();S(Array.isArray(e)?e:[])}catch(e){console.error("Failed to fetch vehicles:",e),S([])}finally{z(!1)}},[]),er=(0,i.useCallback)(async()=>{D(!0),J(null);try{let e=await (0,d.Lv)();E(e)}catch(e){console.error("Failed to fetch service records:",e),J("Failed to load service records. Please try again.")}finally{D(!1)}},[]);(0,i.useEffect)(()=>{ee(),er()},[ee,er,K]),(0,i.useEffect)(()=>{let e=new Set;C.forEach(r=>{r.servicePerformed.forEach(r=>e.add(r))}),P.forEach(r=>e.add(r)),Y(Array.from(e).sort())},[C]),(0,i.useEffect)(()=>{let e=[...C];if("all"!==W&&(e=e.filter(e=>e.vehicleId===W)),"all"!==O&&(e=e.filter(e=>e.servicePerformed.includes(O))),$){let r=$.toLowerCase();e=e.filter(e=>"".concat(e.vehicleMake," ").concat(e.vehicleModel).toLowerCase().includes(r)||e.servicePerformed.join(" ").toLowerCase().includes(r)||e.notes&&e.notes.toLowerCase().includes(r)||e.vehiclePlateNumber&&e.vehiclePlateNumber.toLowerCase().includes(r)||e.odometer.toString().includes(r))}V(e)},[C,W,O,$]);let es=(0,i.useCallback)(()=>{Q(e=>e+1)},[]);return q?(0,a.jsx)(L.A,{children:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)(n.z,{title:"Service History",icon:f.A,children:(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)(k.E,{className:"h-10 w-32"}),(0,a.jsx)(k.E,{className:"h-10 w-32"})]})}),(0,a.jsx)(p.Zp,{children:(0,a.jsx)(p.Wu,{className:"pt-6",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-6",children:[(0,a.jsx)(k.E,{className:"h-10 w-full"}),(0,a.jsx)(k.E,{className:"h-10 w-full"}),(0,a.jsx)(k.E,{className:"h-10 w-full"})]})})}),(0,a.jsx)("div",{className:"space-y-4",children:[void 0,void 0,void 0,void 0,void 0].map((e,r)=>(0,a.jsxs)("div",{className:"flex items-center space-x-4 p-4 border-b",children:[(0,a.jsx)(k.E,{className:"h-6 w-1/6"}),(0,a.jsx)(k.E,{className:"h-6 w-1/6"}),(0,a.jsx)(k.E,{className:"h-6 w-2/6"}),(0,a.jsx)(k.E,{className:"h-6 w-1/6"}),(0,a.jsx)(k.E,{className:"h-6 w-1/6"})]},r))})]})}):(0,a.jsx)(L.A,{children:(0,a.jsxs)("div",{className:"jsx-5e2e520e6dad691 space-y-6 print-container",children:[(0,a.jsx)(n.z,{title:"Service History Report",description:"View and manage all service records for your vehicles.",icon:f.A,children:(0,a.jsxs)("div",{className:"jsx-5e2e520e6dad691 flex gap-2 no-print",children:[(0,a.jsx)(F.r,{actionType:"tertiary",asChild:!0,icon:(0,a.jsx)(v.A,{className:"h-4 w-4"}),children:(0,a.jsx)(w(),{href:"/vehicles",children:"Log New Service"})}),(0,a.jsx)(T.k,{reportContentId:"#service-history-report-content",reportType:"service-history",tableId:"#service-history-table",fileName:"service-history-report-".concat(new Date().toISOString().split("T")[0]),enableCsv:R.length>0})]})}),(0,a.jsxs)(p.Zp,{className:"shadow-sm no-print",children:[(0,a.jsxs)(p.aR,{className:"pb-2",children:[(0,a.jsx)(p.ZB,{className:"text-lg",children:"Filter Options"}),(0,a.jsx)(p.BT,{children:"Filter service records by vehicle, service type, or search terms"})]}),(0,a.jsxs)(p.Wu,{children:[(0,a.jsxs)("div",{className:"jsx-5e2e520e6dad691 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 items-end filter-grid",children:[(0,a.jsxs)("div",{className:"jsx-5e2e520e6dad691",children:[(0,a.jsx)(x.J,{htmlFor:"vehicle-filter",children:"Filter by Vehicle"}),(0,a.jsxs)(m.l6,{value:W,onValueChange:B,"aria-label":"Filter by vehicle",children:[(0,a.jsx)(m.bq,{id:"vehicle-filter",className:"w-full mt-1.5",children:(0,a.jsx)(m.yv,{placeholder:"All Vehicles"})}),(0,a.jsxs)(m.gC,{children:[(0,a.jsx)(m.eb,{value:"all",children:"All Vehicles"}),g.map(e=>(0,a.jsxs)(m.eb,{value:String(e.id),children:[e.make," ",e.model," (",e.year,")"]},e.id))]})]})]}),(0,a.jsxs)("div",{className:"jsx-5e2e520e6dad691",children:[(0,a.jsx)(x.J,{htmlFor:"service-filter",children:"Filter by Service Type"}),(0,a.jsxs)(m.l6,{value:O,onValueChange:_,"aria-label":"Filter by service type",children:[(0,a.jsx)(m.bq,{id:"service-filter",className:"w-full mt-1.5",children:(0,a.jsx)(m.yv,{placeholder:"All Services"})}),(0,a.jsxs)(m.gC,{children:[(0,a.jsx)(m.eb,{value:"all",children:"All Services"}),G.map(e=>(0,a.jsx)(m.eb,{value:e,children:e},e))]})]})]}),(0,a.jsxs)("div",{className:"jsx-5e2e520e6dad691",children:[(0,a.jsx)(x.J,{htmlFor:"search-records",children:"Search Records"}),(0,a.jsxs)("div",{className:"jsx-5e2e520e6dad691 relative mt-1.5",children:[(0,a.jsx)(h.p,{id:"search-records",type:"text",placeholder:"Search by keyword, notes, plate...",value:U,onChange:e=>Z(e.target.value),className:"pl-10 pr-10","aria-label":"Search service records"}),(0,a.jsx)(y.A,{className:"absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground","aria-hidden":"true"}),U&&(0,a.jsxs)(o.$,{variant:"ghost",size:"icon",className:"absolute right-1 top-1/2 -translate-y-1/2 h-7 w-7",onClick:()=>Z(""),"aria-label":"Clear search",children:[(0,a.jsx)(j.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"jsx-5e2e520e6dad691 sr-only",children:"Clear search"})]})]})]})]}),("all"!==W||"all"!==O||U)&&(0,a.jsxs)("div",{className:"jsx-5e2e520e6dad691 mt-6 flex flex-wrap items-center justify-between border border-border p-3 rounded-md",children:[(0,a.jsxs)("div",{className:"jsx-5e2e520e6dad691 flex flex-wrap gap-2 items-center",children:[(0,a.jsx)("span",{className:"jsx-5e2e520e6dad691 font-medium text-sm",children:"Active Filters:"}),"all"!==W&&(0,a.jsxs)(u.E,{variant:"outline",className:"flex items-center gap-1",children:[(0,a.jsx)(b.A,{className:"h-3 w-3"}),null==(e=g.find(e=>e.id===Number(W)))?void 0:e.make," ",null==(r=g.find(e=>e.id===Number(W)))?void 0:r.model]}),"all"!==O&&(0,a.jsxs)(u.E,{variant:"outline",className:"flex items-center gap-1",children:[(0,a.jsx)(N.A,{className:"h-3 w-3"}),O]}),U&&(0,a.jsxs)(u.E,{variant:"outline",className:"flex items-center gap-1",children:[(0,a.jsx)(y.A,{className:"h-3 w-3"}),'"',U,'"']})]}),(0,a.jsxs)(o.$,{variant:"outline",size:"sm",onClick:()=>{B("all"),_("all"),Z("")},className:"mt-2 sm:mt-0","aria-label":"Reset all filters",children:[(0,a.jsx)(j.A,{className:"h-3 w-3 mr-1"}),"Reset Filters"]})]})]})]}),(0,a.jsxs)("div",{id:"service-history-report-content",className:"jsx-5e2e520e6dad691 report-content",children:[(0,a.jsxs)("header",{className:"jsx-5e2e520e6dad691 text-center mb-8 pb-4 border-b-2 border-gray-300 print-only",children:[(0,a.jsx)("h1",{className:"jsx-5e2e520e6dad691 text-3xl font-bold text-gray-800",children:"Service History Report"}),(0,a.jsx)("p",{className:"jsx-5e2e520e6dad691 text-md text-gray-600",children:"all"!==W?"Vehicle: ".concat(null==(s=g.find(e=>e.id===Number(W)))?void 0:s.make," ").concat(null==(t=g.find(e=>e.id===Number(W)))?void 0:t.model):"All Vehicles"})]}),(0,a.jsx)(M,{children:(0,a.jsx)(A.R,{records:R,isLoading:I,error:H,onRetry:es,showVehicleInfo:!0,vehicleSpecific:!1})}),(0,a.jsxs)("footer",{className:"jsx-5e2e520e6dad691 mt-10 pt-4 border-t-2 border-gray-300 text-center text-xs text-gray-500",children:[(0,a.jsxs)("p",{className:"jsx-5e2e520e6dad691",children:["Report generated on: ",new Date().toLocaleDateString()]}),(0,a.jsx)("p",{className:"jsx-5e2e520e6dad691",children:"WorkHub - Vehicle Service Management"})]})]}),(0,a.jsx)(l(),{id:"5e2e520e6dad691",children:".print-only{display:none}@media print{.no-print{display:none!important}.print-only{display:block}.print-container{padding:1rem}.card-print{-webkit-box-shadow:none!important;-moz-box-shadow:none!important;box-shadow:none!important;border:none!important}.print-service-col{max-width:200px;white-space:normal!important}.print-notes-col{max-width:200px;white-space:normal!important}.print-text-wrap{word-break:break-word;white-space:normal!important}}@media(max-width:640px){.delegations-table-container,.overflow-x-auto{overflow-x:auto}.filter-grid{grid-template-columns:1fr!important}.summary-grid{grid-template-columns:1fr 1fr!important}}"})]})})}},59409:(e,r,s)=>{"use strict";s.d(r,{bq:()=>m,eb:()=>f,gC:()=>u,l6:()=>o,yv:()=>h});var a=s(95155),t=s(12115),l=s(31992),i=s(79556),c=s(77381),d=s(10518),n=s(59434);let o=l.bL;l.YJ;let h=l.WT,m=t.forwardRef((e,r)=>{let{className:s,children:t,...c}=e;return(0,a.jsxs)(l.l9,{ref:r,className:(0,n.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",s),...c,children:[t,(0,a.jsx)(l.In,{asChild:!0,children:(0,a.jsx)(i.A,{className:"h-4 w-4 opacity-50"})})]})});m.displayName=l.l9.displayName;let p=t.forwardRef((e,r)=>{let{className:s,...t}=e;return(0,a.jsx)(l.PP,{ref:r,className:(0,n.cn)("flex cursor-default items-center justify-center py-1",s),...t,children:(0,a.jsx)(c.A,{className:"h-4 w-4"})})});p.displayName=l.PP.displayName;let x=t.forwardRef((e,r)=>{let{className:s,...t}=e;return(0,a.jsx)(l.wn,{ref:r,className:(0,n.cn)("flex cursor-default items-center justify-center py-1",s),...t,children:(0,a.jsx)(i.A,{className:"h-4 w-4"})})});x.displayName=l.wn.displayName;let u=t.forwardRef((e,r)=>{let{className:s,children:t,position:i="popper",...c}=e;return(0,a.jsx)(l.ZL,{children:(0,a.jsxs)(l.UC,{ref:r,className:(0,n.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===i&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",s),position:i,...c,children:[(0,a.jsx)(p,{}),(0,a.jsx)(l.LM,{className:(0,n.cn)("p-1","popper"===i&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:t}),(0,a.jsx)(x,{})]})})});u.displayName=l.UC.displayName,t.forwardRef((e,r)=>{let{className:s,...t}=e;return(0,a.jsx)(l.JU,{ref:r,className:(0,n.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",s),...t})}).displayName=l.JU.displayName;let f=t.forwardRef((e,r)=>{let{className:s,children:t,...i}=e;return(0,a.jsxs)(l.q7,{ref:r,className:(0,n.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",s),...i,children:[(0,a.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,a.jsx)(l.VF,{children:(0,a.jsx)(d.A,{className:"h-4 w-4"})})}),(0,a.jsx)(l.p4,{children:t})]})});f.displayName=l.q7.displayName,t.forwardRef((e,r)=>{let{className:s,...t}=e;return(0,a.jsx)(l.wv,{ref:r,className:(0,n.cn)("-mx-1 my-1 h-px bg-muted",s),...t})}).displayName=l.wv.displayName},62523:(e,r,s)=>{"use strict";s.d(r,{p:()=>i});var a=s(95155),t=s(12115),l=s(59434);let i=t.forwardRef((e,r)=>{let{className:s,type:t,...i}=e;return(0,a.jsx)("input",{type:t,className:(0,l.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",s),ref:r,...i})});i.displayName="Input"},67554:(e,r,s)=>{"use strict";s.d(r,{A:()=>a});let a=(0,s(40157).A)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},75074:(e,r,s)=>{"use strict";s.d(r,{A:()=>a});let a=(0,s(40157).A)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},85057:(e,r,s)=>{"use strict";s.d(r,{J:()=>n});var a=s(95155),t=s(12115),l=s(40968),i=s(74466),c=s(59434);let d=(0,i.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),n=t.forwardRef((e,r)=>{let{className:s,...t}=e;return(0,a.jsx)(l.b,{ref:r,className:(0,c.cn)(d(),s),...t})});n.displayName=l.b.displayName},95449:(e,r,s)=>{Promise.resolve().then(s.bind(s,59142))}},e=>{var r=r=>e(e.s=r);e.O(0,[3796,6113,832,2688,2512,1859,7529,8782,6045,8738,8162,2730,536,9798,8610,8441,1684,7358],()=>r(95449)),_N_E=e.O()}]);