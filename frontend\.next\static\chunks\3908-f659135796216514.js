"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3908],{17759:(e,t,r)=>{r.d(t,{C5:()=>g,MJ:()=>p,eI:()=>h,lR:()=>f,lV:()=>d,zB:()=>m});var a=r(95155),s=r(12115),l=r(99708),n=r(62177),i=r(59434),o=r(85057);let d=n.Op,c=s.createContext({}),m=e=>{let{...t}=e;return(0,a.jsx)(c.Provider,{value:{name:t.name},children:(0,a.jsx)(n.xI,{...t})})},u=()=>{let e=s.useContext(c),t=s.useContext(x),{getFieldState:r,formState:a}=(0,n.xW)(),l=r(e.name,a);if(!e)throw Error("useFormField should be used within <FormField>");let{id:i}=t;return{id:i,name:e.name,formItemId:"".concat(i,"-form-item"),formDescriptionId:"".concat(i,"-form-item-description"),formMessageId:"".concat(i,"-form-item-message"),...l}},x=s.createContext({}),h=s.forwardRef((e,t)=>{let{className:r,...l}=e,n=s.useId();return(0,a.jsx)(x.Provider,{value:{id:n},children:(0,a.jsx)("div",{ref:t,className:(0,i.cn)("space-y-2",r),...l})})});h.displayName="FormItem";let f=s.forwardRef((e,t)=>{let{className:r,...s}=e,{error:l,formItemId:n}=u();return(0,a.jsx)(o.J,{ref:t,className:(0,i.cn)(l&&"text-destructive",r),htmlFor:n,...s})});f.displayName="FormLabel";let p=s.forwardRef((e,t)=>{let{...r}=e,{error:s,formItemId:n,formDescriptionId:i,formMessageId:o}=u();return(0,a.jsx)(l.DX,{ref:t,id:n,"aria-describedby":s?"".concat(i," ").concat(o):"".concat(i),"aria-invalid":!!s,...r})});p.displayName="FormControl",s.forwardRef((e,t)=>{let{className:r,...s}=e,{formDescriptionId:l}=u();return(0,a.jsx)("p",{ref:t,id:l,className:(0,i.cn)("text-sm text-muted-foreground",r),...s})}).displayName="FormDescription";let g=s.forwardRef((e,t)=>{var r;let{className:s,children:l,...n}=e,{error:o,formMessageId:d}=u(),c=o?String(null!=(r=null==o?void 0:o.message)?r:""):l;return c?(0,a.jsx)("p",{ref:t,id:d,className:(0,i.cn)("text-sm font-medium text-destructive",s),...n,children:c}):null});g.displayName="FormMessage"},33908:(e,t,r)=>{r.d(t,{A:()=>er});var a=r(95155),s=r(62177),l=r(90221),n=r(92122),i=r(6560),o=r(62523),d=r(88539),c=r(66695),m=r(17759),u=r(59409),x=r(50594),h=r(50286),f=r(77223),p=r(34301),g=r(83082),j=r(75074),y=r(74465),v=r(12543),N=r(59119),b=r(35695),w=r(83343),T=r(73168),D=r(12115),A=r(87481),C=r(51920),I=r(50172),R=r(58260),S=r(15452),F=r(25318),M=r(59434);let k=S.bL;S.l9;let z=S.ZL;S.bm;let O=D.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(S.hJ,{ref:t,className:(0,M.cn)("fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",r),...s})});O.displayName=S.hJ.displayName;let P=D.forwardRef((e,t)=>{let{className:r,children:s,...l}=e;return(0,a.jsxs)(z,{children:[(0,a.jsx)(O,{}),(0,a.jsxs)(S.UC,{ref:t,className:(0,M.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",r),...l,children:[s,(0,a.jsxs)(S.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,a.jsx)(F.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})});P.displayName=S.UC.displayName;let _=e=>{let{className:t,...r}=e;return(0,a.jsx)("div",{className:(0,M.cn)("flex flex-col space-y-1.5 text-center sm:text-left",t),...r})};_.displayName="DialogHeader";let J=e=>{let{className:t,...r}=e;return(0,a.jsx)("div",{className:(0,M.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",t),...r})};J.displayName="DialogFooter";let E=D.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(S.hE,{ref:t,className:(0,M.cn)("text-lg font-semibold leading-none tracking-tight",r),...s})});E.displayName=S.hE.displayName;let B=D.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(S.VY,{ref:t,className:(0,M.cn)("text-sm text-muted-foreground",r),...s})});B.displayName=S.VY.displayName;var U=r(66424),L=r(52582);let V=async(e,t)=>{if(!t)throw console.error("Search date is required for historical flight search."),Error("Search date is required");if(new Date("".concat(t,"T00:00:00.000Z"))>new Date)throw console.warn("Search for future date rejected: ".concat(t)),Error("OpenSky API does not provide data for future dates. The date ".concat(t," is in the future."));let r=await (0,L.Fd)("/flights/search?callsign=".concat(encodeURIComponent(e),"&date=").concat(t));if(Array.isArray(r))return r;if(r.flights)return r.flights;if(r.message){let e=Error(r.message);throw e.details=r.details,e}return[]};var G=r(20547);let H=G.bL,Z=G.l9,q=D.forwardRef((e,t)=>{let{className:r,align:s="center",sideOffset:l=4,...n}=e;return(0,a.jsx)(G.ZL,{children:(0,a.jsx)(G.UC,{ref:t,align:s,sideOffset:l,className:(0,M.cn)("z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-popover-content-transform-origin]",r),...n})})});q.displayName=G.UC.displayName;var Y=r(965),W=r(73158),K=r(43900),X=r(30285);function Q(e){let{className:t,classNames:r,showOutsideDays:s=!0,...l}=e;return(0,a.jsx)(K.hv,{showOutsideDays:s,className:(0,M.cn)("p-3",t),classNames:{months:"flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0",month:"space-y-4",caption:"flex justify-center pt-1 relative items-center",caption_label:"text-sm font-medium",nav:"space-x-1 flex items-center",nav_button:(0,M.cn)((0,X.r)({variant:"outline"}),"h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100"),nav_button_previous:"absolute left-1",nav_button_next:"absolute right-1",table:"w-full border-collapse space-y-1",head_row:"flex",head_cell:"text-muted-foreground rounded-md w-9 font-normal text-[0.8rem]",row:"flex w-full mt-2",cell:"h-9 w-9 text-center text-sm p-0 relative [&:has([aria-selected].day-range-end)]:rounded-r-md [&:has([aria-selected].day-outside)]:bg-accent/50 [&:has([aria-selected])]:bg-accent first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md focus-within:relative focus-within:z-20",day:(0,M.cn)((0,X.r)({variant:"ghost"}),"h-9 w-9 p-0 font-normal aria-selected:opacity-100"),day_range_end:"day-range-end",day_selected:"bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground",day_today:"bg-accent text-accent-foreground",day_outside:"day-outside text-muted-foreground aria-selected:bg-accent/50 aria-selected:text-muted-foreground",day_disabled:"text-muted-foreground opacity-50",day_range_middle:"aria-selected:bg-accent aria-selected:text-accent-foreground",day_hidden:"invisible",...r},components:{IconLeft:e=>{let{className:t,...r}=e;return(0,a.jsx)(Y.A,{className:(0,M.cn)("h-4 w-4",t),...r})},IconRight:e=>{let{className:t,...r}=e;return(0,a.jsx)(W.A,{className:(0,M.cn)("h-4 w-4",t),...r})}},...l})}function $(e){let{isOpen:t,onClose:r,onSelectFlight:s,type:l}=e,[n,d]=(0,D.useState)(""),[c,m]=(0,D.useState)(new Date),[u,x]=(0,D.useState)(!1),[h,f]=(0,D.useState)([]),[p,v]=(0,D.useState)(null),{toast:N}=(0,A.dj)(),b=(0,D.useCallback)(function(e,t){let r=null,a=function(){for(var a=arguments.length,s=Array(a),l=0;l<a;l++)s[l]=arguments[l];let n=this;r&&clearTimeout(r),r=setTimeout(()=>{r=null,e.apply(n,s)},t)};return a.cancel=function(){r&&(clearTimeout(r),r=null)},a}(async(e,t)=>{if(e.length<2)return void f([]);if(!t){v("Please select a date to search."),N({title:"Date Required",description:"Please select a date before searching for flights.",variant:"destructive"}),f([]);return}x(!0),v(null);let r=(0,T.GP)(t,"yyyy-MM-dd");try{let t=await V(e,r);f(t),0===t.length&&N({title:"No Flights Found",description:'No flights found matching "'.concat(e,'" on ').concat(r,"."),variant:"default"})}catch(e){v(e.message||"Failed to search flights"),N({title:"Error Searching Flights",description:"Failed to search flights: ".concat(e.message,". Please try again."),variant:"destructive"})}finally{x(!1)}},500),[N]);(0,D.useEffect)(()=>(n&&c?b(n,c):f([]),()=>{b.cancel()}),[n,c,b]);let w=e=>{s(e),r()},S=e=>e?new Date(1e3*e).toLocaleString():"Unknown";return(0,a.jsx)(k,{open:t,onOpenChange:e=>!e&&r(),children:(0,a.jsxs)(P,{className:"sm:max-w-[600px] max-h-[80vh] flex flex-col",children:[(0,a.jsxs)(_,{children:[(0,a.jsxs)(E,{className:"flex items-center",children:["arrival"===l?(0,a.jsx)(g.A,{className:"mr-2 h-5 w-5 text-accent"}):(0,a.jsx)(y.A,{className:"mr-2 h-5 w-5 text-accent"}),"Search ","arrival"===l?"Arrival":"Departure"," Flights"]}),(0,a.jsx)(B,{children:"Enter a flight callsign (e.g., BA123) and select a date to search for flights."})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4",children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(j.A,{className:"absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground"}),(0,a.jsx)(o.p,{placeholder:"Callsign (e.g., BA123)",className:"pl-10",value:n,onChange:e=>d(e.target.value)})]}),(0,a.jsx)("div",{children:(0,a.jsxs)(H,{children:[(0,a.jsx)(Z,{asChild:!0,children:(0,a.jsx)(i.r,{actionType:"tertiary",className:(0,M.cn)("w-full justify-start text-left font-normal",!c&&"text-muted-foreground"),icon:(0,a.jsx)(C.A,{className:"h-4 w-4"}),children:c?(0,T.GP)(c,"PPP"):(0,a.jsx)("span",{children:"Pick a date"})})}),(0,a.jsx)(q,{className:"w-auto p-0",children:(0,a.jsx)(Q,{mode:"single",selected:c,onSelect:m,initialFocus:!0})})]})})]}),u?(0,a.jsxs)("div",{className:"flex justify-center items-center py-8",children:[(0,a.jsx)(I.A,{className:"h-8 w-8 animate-spin text-primary"}),(0,a.jsx)("span",{className:"ml-2 text-muted-foreground",children:"Searching flights..."})]}):p?(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsxs)("div",{className:"bg-destructive/15 text-destructive p-4 rounded-md mb-4 text-sm",children:[(0,a.jsx)("h4",{className:"font-bold mb-2",children:"Error Details:"}),(0,a.jsx)("p",{className:"mb-2",children:p}),p&&p.details&&(0,a.jsxs)("div",{className:"mt-3 border-t border-destructive/20 pt-3",children:[p.details.possibleReasons&&(0,a.jsxs)("div",{className:"mt-2",children:[(0,a.jsx)("h5",{className:"font-semibold text-xs mb-1",children:"Possible Reasons:"}),(0,a.jsx)("ul",{className:"list-disc list-inside text-xs",children:p.details.possibleReasons.map((e,t)=>(0,a.jsx)("li",{className:"mb-1",children:e},t))})]}),p.details.apiInfo&&(0,a.jsxs)("div",{className:"mt-2 text-xs",children:[(0,a.jsx)("h5",{className:"font-semibold mb-1",children:"API Information:"}),(0,a.jsx)("p",{children:p.details.apiInfo})]})]}),(0,a.jsxs)("p",{className:"text-xs text-muted-foreground mt-3",children:["API URL:"," ","http://localhost:3001/api"]})]}),(0,a.jsx)("div",{className:"flex justify-center gap-2",children:p.toString().includes("future date")?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(i.r,{actionType:"tertiary",onClick:()=>{m(new Date),setTimeout(()=>b(n,new Date),100)},children:"Try with Today's Date"}),(0,a.jsx)(i.r,{actionType:"tertiary",onClick:()=>{let e=new Date;e.setDate(e.getDate()-1),m(e),setTimeout(()=>b(n,e),100)},children:"Try with Yesterday"})]}):(0,a.jsx)(i.r,{actionType:"tertiary",onClick:()=>b(n,c),children:"Try Again"})})]}):0===h.length?(0,a.jsx)("div",{className:"text-center py-8 text-muted-foreground",children:n.length>0?(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-amber-500 font-medium mb-2",children:['No flights found matching "',n,'" on'," ",c?(0,T.GP)(c,"PPP"):"selected date"]}),(0,a.jsxs)("div",{className:"bg-muted p-4 rounded-md mb-4 text-sm",children:[(0,a.jsx)("h4",{className:"font-semibold mb-2",children:"Suggestions:"}),(0,a.jsxs)("ul",{className:"list-disc list-inside text-muted-foreground text-sm",children:[(0,a.jsx)("li",{className:"mb-1",children:'Check if the callsign format is correct (e.g., "RYR441J" for Ryanair flight 441J)'}),(0,a.jsx)("li",{className:"mb-1",children:"Try searching for a different date - OpenSky may not have data for all dates"}),(0,a.jsx)("li",{className:"mb-1",children:"OpenSky Network API primarily provides historical data, not future schedules"}),(0,a.jsx)("li",{className:"mb-1",children:"Some flights may not be tracked by OpenSky Network"})]})]}),(0,a.jsxs)("div",{className:"text-xs text-muted-foreground p-2 bg-muted rounded-md inline-block",children:[(0,a.jsxs)("p",{children:["API URL:"," ","http://localhost:3001/api","/flights/search"]}),(0,a.jsxs)("p",{children:["Search Term: ",n]}),(0,a.jsxs)("p",{children:["Date:"," ",c?(0,T.GP)(c,"yyyy-MM-dd"):"Not selected"]})]})]}):"Enter a flight callsign to search."}):(0,a.jsx)(U.F,{className:"flex-1 max-h-[400px] pr-4",children:(0,a.jsx)("div",{className:"space-y-2",children:h.map(e=>(0,a.jsxs)("div",{className:"p-3 border rounded-md hover:bg-accent hover:text-accent-foreground cursor-pointer transition-colors",onClick:()=>w(e),children:[(0,a.jsxs)("div",{className:"flex justify-between items-start",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-semibold",children:e.callsign}),(0,a.jsxs)("p",{className:"text-sm text-muted-foreground",children:[e.departureAirport||"Unknown"," →"," ",e.arrivalAirport||"Unknown"]})]}),(0,a.jsxs)("div",{className:"text-right text-sm",children:[(0,a.jsxs)("p",{className:"flex items-center",children:[(0,a.jsx)(R.A,{className:"inline-block h-3 w-3 mr-1 text-muted-foreground"}),e.icao24]}),void 0!==e.onGround&&(0,a.jsx)("p",{className:e.onGround?"text-amber-500":"text-green-500",children:e.onGround?"On Ground":"In Air"})]})]}),(e.departureTime||e.arrivalTime)&&(0,a.jsxs)("div",{className:"mt-2 text-xs text-muted-foreground",children:[e.departureTime&&(0,a.jsxs)("p",{children:["Departure: ",S(e.departureTime)]}),e.arrivalTime&&(0,a.jsxs)("p",{children:["Arrival: ",S(e.arrivalTime)]})]})]},"".concat(e.icao24,"-").concat(e.callsign)))})}),(0,a.jsx)(J,{className:"mt-4",children:(0,a.jsx)(i.r,{actionType:"tertiary",onClick:r,children:"Cancel"})})]})})}Q.displayName="Calendar";var ee=r(99673);let et=(e,t)=>{if(!e)return"";try{let r=(0,w.H)(e);if("date"===t)return(0,T.GP)(r,"yyyy-MM-dd");return(0,T.GP)(r,"yyyy-MM-dd'T'HH:mm")}catch(t){return console.warn("Invalid date string for input formatting:",e),""}};function er(e){var t;let{onSubmit:C,initialData:I,isEditing:R=!1,isSubmitting:S=!1}=e,F=(0,b.useRouter)(),{toast:M}=(0,A.dj)(),[k,z]=(0,D.useState)(!1),[O,P]=(0,D.useState)(!1),_=(0,s.mN)({resolver:(0,l.u)(n.eL),defaultValues:{eventName:(null==I?void 0:I.eventName)||"",location:(null==I?void 0:I.location)||"",durationFrom:I?et(I.durationFrom,"date"):"",durationTo:I?et(I.durationTo,"date"):"",invitationFrom:(null==I?void 0:I.invitationFrom)||"",invitationTo:(null==I?void 0:I.invitationTo)||"",delegates:(null==I||null==(t=I.delegates)?void 0:t.length)?I.delegates:[{name:"",title:"",notes:""}],flightArrivalDetails:(null==I?void 0:I.flightArrivalDetails)?{...I.flightArrivalDetails,dateTime:et(I.flightArrivalDetails.dateTime,"datetime-local")}:null,flightDepartureDetails:(null==I?void 0:I.flightDepartureDetails)?{...I.flightDepartureDetails,dateTime:et(I.flightDepartureDetails.dateTime,"datetime-local")}:null,status:(null==I?void 0:I.status)||"Planned",notes:(null==I?void 0:I.notes)||"",imageUrl:(null==I?void 0:I.imageUrl)||""}}),{fields:J,append:E,remove:B}=(0,s.jz)({control:_.control,name:"delegates"}),U=e=>{if(!e||(!e.flightNumber||""===e.flightNumber.trim())&&(!e.dateTime||""===e.dateTime.trim())&&(!e.airport||""===e.airport.trim()))return null;if(!(e.flightNumber&&""!==e.flightNumber.trim()&&e.dateTime&&""!==e.dateTime.trim()&&e.airport&&""!==e.airport.trim()))return console.warn("Incomplete flight details detected:",e),null;try{let{formatDateForApi:t,isValidDateString:a}=r(21876),s={...e};if(s.dateTime&&(s.dateTime=t(s.dateTime),!s.dateTime))return console.error("Invalid date format after processing:",e.dateTime),null;return s}catch(e){return console.error("Error processing flight details:",e),null}},L=(e,t)=>{try{let r="arrival"===t?e.arrivalTime:e.departureTime,a="";if(r){let e=new Date(1e3*r);a=(0,T.GP)(e,"yyyy-MM-dd'T'HH:mm")}else{let e=new Date;if("departure"===t&&_.getValues("durationFrom"))try{let t=(0,w.H)(_.getValues("durationFrom"));e.setDate(t.getDate()-1)}catch(e){console.warn("Could not parse durationFrom date, using current date")}a=(0,T.GP)(e,"yyyy-MM-dd'T'HH:mm")}"arrival"===t?(_.setValue("flightArrivalDetails.flightNumber",e.callsign),_.setValue("flightArrivalDetails.dateTime",a),_.setValue("flightArrivalDetails.airport",e.arrivalAirport||"")):(_.setValue("flightDepartureDetails.flightNumber",e.callsign),_.setValue("flightDepartureDetails.dateTime",a),_.setValue("flightDepartureDetails.airport",e.departureAirport||"")),M({title:"Flight Details Updated",description:"".concat("arrival"===t?"Arrival":"Departure"," flight details have been populated."),variant:"default"})}catch(e){console.error("Error setting flight details:",e),M({title:"Error",description:"Failed to set flight details. Please try again or enter manually.",variant:"destructive"})}};return(0,a.jsx)(m.lV,{..._,children:(0,a.jsx)("form",{onSubmit:_.handleSubmit(e=>{C({...e,flightArrivalDetails:U(e.flightArrivalDetails),flightDepartureDetails:U(e.flightDepartureDetails)})}),children:(0,a.jsxs)(c.Zp,{className:"shadow-lg",children:[(0,a.jsxs)(c.aR,{children:[(0,a.jsx)(c.ZB,{className:"text-2xl text-primary",children:R?"Edit Delegation":"Add New Delegation"}),(0,a.jsx)(c.BT,{children:"Fill in the details for the delegation event or trip."})]}),(0,a.jsxs)(c.Wu,{className:"space-y-6",children:[(0,a.jsxs)("section",{className:"space-y-4 p-6 border rounded-lg bg-card",children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-foreground flex items-center",children:[(0,a.jsx)(x.A,{className:"mr-2 h-5 w-5 text-accent"}),"Basic Information"]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsx)(m.zB,{control:_.control,name:"eventName",render:e=>{let{field:t}=e;return(0,a.jsxs)(m.eI,{children:[(0,a.jsx)(m.lR,{children:"Event Name"}),(0,a.jsx)(m.MJ,{children:(0,a.jsx)(o.p,{placeholder:"e.g., Annual Summit 2024",...t})}),(0,a.jsx)(m.C5,{})]})}}),(0,a.jsx)(m.zB,{control:_.control,name:"location",render:e=>{let{field:t}=e;return(0,a.jsxs)(m.eI,{children:[(0,a.jsx)(m.lR,{children:"Location"}),(0,a.jsx)(m.MJ,{children:(0,a.jsx)(o.p,{placeholder:"e.g., New York, USA",...t})}),(0,a.jsx)(m.C5,{})]})}})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsx)(m.zB,{control:_.control,name:"durationFrom",render:e=>{let{field:t}=e;return(0,a.jsxs)(m.eI,{children:[(0,a.jsx)(m.lR,{children:"Duration From"}),(0,a.jsx)(m.MJ,{children:(0,a.jsx)(o.p,{type:"date",...t})}),(0,a.jsx)(m.C5,{})]})}}),(0,a.jsx)(m.zB,{control:_.control,name:"durationTo",render:e=>{let{field:t}=e;return(0,a.jsxs)(m.eI,{children:[(0,a.jsx)(m.lR,{children:"Duration To"}),(0,a.jsx)(m.MJ,{children:(0,a.jsx)(o.p,{type:"date",...t})}),(0,a.jsx)(m.C5,{})]})}})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsx)(m.zB,{control:_.control,name:"invitationFrom",render:e=>{let{field:t}=e;return(0,a.jsxs)(m.eI,{children:[(0,a.jsx)(m.lR,{children:"Invitation From (Optional)"}),(0,a.jsx)(m.MJ,{children:(0,a.jsx)(o.p,{placeholder:"e.g., Global Corp",...t})}),(0,a.jsx)(m.C5,{})]})}}),(0,a.jsx)(m.zB,{control:_.control,name:"invitationTo",render:e=>{let{field:t}=e;return(0,a.jsxs)(m.eI,{children:[(0,a.jsx)(m.lR,{children:"Invitation To (Optional)"}),(0,a.jsx)(m.MJ,{children:(0,a.jsx)(o.p,{placeholder:"e.g., Our CEO",...t})}),(0,a.jsx)(m.C5,{})]})}})]}),(0,a.jsx)(m.zB,{control:_.control,name:"status",render:e=>{let{field:t}=e;return(0,a.jsxs)(m.eI,{children:[(0,a.jsx)(m.lR,{children:"Status"}),(0,a.jsxs)(u.l6,{onValueChange:t.onChange,defaultValue:t.value,children:[(0,a.jsx)(m.MJ,{children:(0,a.jsx)(u.bq,{children:(0,a.jsx)(u.yv,{placeholder:"Select status"})})}),(0,a.jsx)(u.gC,{children:n.Qw.options.map(e=>(0,a.jsx)(u.eb,{value:e,children:(0,ee.fZ)(e)},e))})]}),(0,a.jsx)(m.C5,{})]})}}),(0,a.jsx)(m.zB,{control:_.control,name:"imageUrl",render:e=>{let{field:t}=e;return(0,a.jsxs)(m.eI,{children:[(0,a.jsx)(m.lR,{children:"Image URL (Optional)"}),(0,a.jsx)(m.MJ,{children:(0,a.jsx)(o.p,{placeholder:"https://example.com/image.jpg",...t})}),(0,a.jsx)(m.C5,{})]})}})]}),(0,a.jsxs)("section",{className:"space-y-4 p-6 border rounded-lg bg-card",children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-foreground flex items-center",children:[(0,a.jsx)(h.A,{className:"mr-2 h-5 w-5 text-accent"}),"Delegates"]}),J.map((e,t)=>(0,a.jsxs)("div",{className:"p-3 border rounded-md space-y-3 bg-background relative",children:[(0,a.jsx)(m.zB,{control:_.control,name:"delegates.".concat(t,".name"),render:e=>{let{field:t}=e;return(0,a.jsxs)(m.eI,{children:[(0,a.jsx)(m.lR,{children:"Delegate Name"}),(0,a.jsx)(m.MJ,{children:(0,a.jsx)(o.p,{placeholder:"Full Name",...t})}),(0,a.jsx)(m.C5,{})]})}}),(0,a.jsx)(m.zB,{control:_.control,name:"delegates.".concat(t,".title"),render:e=>{let{field:t}=e;return(0,a.jsxs)(m.eI,{children:[(0,a.jsx)(m.lR,{children:"Title/Role"}),(0,a.jsx)(m.MJ,{children:(0,a.jsx)(o.p,{placeholder:"e.g., CEO, Head of Department",...t})}),(0,a.jsx)(m.C5,{})]})}}),(0,a.jsx)(m.zB,{control:_.control,name:"delegates.".concat(t,".notes"),render:e=>{let{field:t}=e;return(0,a.jsxs)(m.eI,{children:[(0,a.jsx)(m.lR,{children:"Notes (Optional)"}),(0,a.jsx)(m.MJ,{children:(0,a.jsx)(d.T,{placeholder:"Any specific notes for this delegate",...t})}),(0,a.jsx)(m.C5,{})]})}}),J.length>1&&(0,a.jsx)(i.r,{type:"button",actionType:"danger",size:"sm",onClick:()=>B(t),className:"absolute top-2 right-2",icon:(0,a.jsx)(f.A,{className:"h-4 w-4"})})]},e.id)),(0,a.jsx)(i.r,{type:"button",actionType:"secondary",onClick:()=>E({name:"",title:"",notes:""}),icon:(0,a.jsx)(p.A,{className:"h-4 w-4"}),children:"Add Delegate"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,a.jsxs)("section",{className:"space-y-4 p-6 border rounded-lg bg-card",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-foreground flex items-center",children:[(0,a.jsx)(g.A,{className:"mr-2 h-5 w-5 text-accent"}),"Arrival Flight (Optional)"]}),(0,a.jsx)(i.r,{type:"button",actionType:"tertiary",size:"sm",onClick:()=>z(!0),icon:(0,a.jsx)(j.A,{className:"h-4 w-4"}),children:"Search Flights"})]}),(0,a.jsx)(m.zB,{control:_.control,name:"flightArrivalDetails.flightNumber",render:e=>{let{field:t}=e;return(0,a.jsxs)(m.eI,{children:[(0,a.jsx)(m.lR,{children:"Flight Number"}),(0,a.jsx)(m.MJ,{children:(0,a.jsx)(o.p,{placeholder:"e.g., BA245",...t,value:t.value||""})}),(0,a.jsx)(m.C5,{})]})}}),(0,a.jsx)(m.zB,{control:_.control,name:"flightArrivalDetails.dateTime",render:e=>{let{field:t}=e;return(0,a.jsxs)(m.eI,{children:[(0,a.jsx)(m.lR,{children:"Date & Time"}),(0,a.jsx)(m.MJ,{children:(0,a.jsx)(o.p,{type:"datetime-local",...t,value:t.value||""})}),(0,a.jsx)(m.C5,{})]})}}),(0,a.jsx)(m.zB,{control:_.control,name:"flightArrivalDetails.airport",render:e=>{let{field:t}=e;return(0,a.jsxs)(m.eI,{children:[(0,a.jsx)(m.lR,{children:"Airport"}),(0,a.jsx)(m.MJ,{children:(0,a.jsx)(o.p,{placeholder:"e.g., JFK",...t,value:t.value||""})}),(0,a.jsx)(m.C5,{})]})}}),(0,a.jsx)(m.zB,{control:_.control,name:"flightArrivalDetails.terminal",render:e=>{let{field:t}=e;return(0,a.jsxs)(m.eI,{children:[(0,a.jsx)(m.lR,{children:"Terminal (Optional)"}),(0,a.jsx)(m.MJ,{children:(0,a.jsx)(o.p,{placeholder:"e.g., T4",...t,value:t.value||""})}),(0,a.jsx)(m.C5,{})]})}}),(0,a.jsx)(m.zB,{control:_.control,name:"flightArrivalDetails.notes",render:e=>{let{field:t}=e;return(0,a.jsxs)(m.eI,{children:[(0,a.jsx)(m.lR,{children:"Notes (Optional)"}),(0,a.jsx)(m.MJ,{children:(0,a.jsx)(d.T,{placeholder:"Arrival notes",...t,value:t.value||""})}),(0,a.jsx)(m.C5,{})]})}})]}),(0,a.jsxs)("section",{className:"space-y-4 p-6 border rounded-lg bg-card",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-foreground flex items-center",children:[(0,a.jsx)(y.A,{className:"mr-2 h-5 w-5 text-accent"}),"Departure Flight (Optional)"]}),(0,a.jsx)(i.r,{type:"button",actionType:"tertiary",size:"sm",onClick:()=>P(!0),icon:(0,a.jsx)(j.A,{className:"h-4 w-4"}),children:"Search Flights"})]}),(0,a.jsx)(m.zB,{control:_.control,name:"flightDepartureDetails.flightNumber",render:e=>{let{field:t}=e;return(0,a.jsxs)(m.eI,{children:[(0,a.jsx)(m.lR,{children:"Flight Number"}),(0,a.jsx)(m.MJ,{children:(0,a.jsx)(o.p,{placeholder:"e.g., AF123",...t,value:t.value||""})}),(0,a.jsx)(m.C5,{})]})}}),(0,a.jsx)(m.zB,{control:_.control,name:"flightDepartureDetails.dateTime",render:e=>{let{field:t}=e;return(0,a.jsxs)(m.eI,{children:[(0,a.jsx)(m.lR,{children:"Date & Time"}),(0,a.jsx)(m.MJ,{children:(0,a.jsx)(o.p,{type:"datetime-local",...t,value:t.value||""})}),(0,a.jsx)(m.C5,{})]})}}),(0,a.jsx)(m.zB,{control:_.control,name:"flightDepartureDetails.airport",render:e=>{let{field:t}=e;return(0,a.jsxs)(m.eI,{children:[(0,a.jsx)(m.lR,{children:"Airport"}),(0,a.jsx)(m.MJ,{children:(0,a.jsx)(o.p,{placeholder:"e.g., LHR",...t,value:t.value||""})}),(0,a.jsx)(m.C5,{})]})}}),(0,a.jsx)(m.zB,{control:_.control,name:"flightDepartureDetails.terminal",render:e=>{let{field:t}=e;return(0,a.jsxs)(m.eI,{children:[(0,a.jsx)(m.lR,{children:"Terminal (Optional)"}),(0,a.jsx)(m.MJ,{children:(0,a.jsx)(o.p,{placeholder:"e.g., T5",...t,value:t.value||""})}),(0,a.jsx)(m.C5,{})]})}}),(0,a.jsx)(m.zB,{control:_.control,name:"flightDepartureDetails.notes",render:e=>{let{field:t}=e;return(0,a.jsxs)(m.eI,{children:[(0,a.jsx)(m.lR,{children:"Notes (Optional)"}),(0,a.jsx)(m.MJ,{children:(0,a.jsx)(d.T,{placeholder:"Departure notes",...t,value:t.value||""})}),(0,a.jsx)(m.C5,{})]})}})]})]}),(0,a.jsx)($,{isOpen:k,onClose:()=>z(!1),onSelectFlight:e=>L(e,"arrival"),type:"arrival"}),(0,a.jsx)($,{isOpen:O,onClose:()=>P(!1),onSelectFlight:e=>L(e,"departure"),type:"departure"}),(0,a.jsxs)("section",{className:"space-y-4 p-6 border rounded-lg bg-card",children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-foreground flex items-center",children:[(0,a.jsx)(x.A,{className:"mr-2 h-5 w-5 text-accent"}),"General Notes (Optional)"]}),(0,a.jsx)(m.zB,{control:_.control,name:"notes",render:e=>{let{field:t}=e;return(0,a.jsxs)(m.eI,{children:[(0,a.jsx)(m.lR,{children:"Additional Notes for the Delegation"}),(0,a.jsx)(m.MJ,{children:(0,a.jsx)(d.T,{placeholder:"Any other relevant information...",...t,value:t.value||""})}),(0,a.jsx)(m.C5,{})]})}})]})]}),(0,a.jsxs)(c.wL,{className:"flex justify-between gap-2 border-t pt-6",children:[(0,a.jsx)(i.r,{type:"button",actionType:"tertiary",onClick:()=>F.back(),icon:(0,a.jsx)(v.A,{className:"h-4 w-4"}),disabled:S,children:"Cancel"}),(0,a.jsx)(i.r,{type:"submit",actionType:"primary",isLoading:S,loadingText:R?"Saving...":"Creating...",icon:(0,a.jsx)(N.A,{className:"h-4 w-4"}),children:R?"Save Changes":"Create Delegation"})]})]})})})}},59409:(e,t,r)=>{r.d(t,{bq:()=>u,eb:()=>p,gC:()=>f,l6:()=>c,yv:()=>m});var a=r(95155),s=r(12115),l=r(31992),n=r(79556),i=r(77381),o=r(10518),d=r(59434);let c=l.bL;l.YJ;let m=l.WT,u=s.forwardRef((e,t)=>{let{className:r,children:s,...i}=e;return(0,a.jsxs)(l.l9,{ref:t,className:(0,d.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",r),...i,children:[s,(0,a.jsx)(l.In,{asChild:!0,children:(0,a.jsx)(n.A,{className:"h-4 w-4 opacity-50"})})]})});u.displayName=l.l9.displayName;let x=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(l.PP,{ref:t,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",r),...s,children:(0,a.jsx)(i.A,{className:"h-4 w-4"})})});x.displayName=l.PP.displayName;let h=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(l.wn,{ref:t,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",r),...s,children:(0,a.jsx)(n.A,{className:"h-4 w-4"})})});h.displayName=l.wn.displayName;let f=s.forwardRef((e,t)=>{let{className:r,children:s,position:n="popper",...i}=e;return(0,a.jsx)(l.ZL,{children:(0,a.jsxs)(l.UC,{ref:t,className:(0,d.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===n&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",r),position:n,...i,children:[(0,a.jsx)(x,{}),(0,a.jsx)(l.LM,{className:(0,d.cn)("p-1","popper"===n&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:s}),(0,a.jsx)(h,{})]})})});f.displayName=l.UC.displayName,s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(l.JU,{ref:t,className:(0,d.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",r),...s})}).displayName=l.JU.displayName;let p=s.forwardRef((e,t)=>{let{className:r,children:s,...n}=e;return(0,a.jsxs)(l.q7,{ref:t,className:(0,d.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",r),...n,children:[(0,a.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,a.jsx)(l.VF,{children:(0,a.jsx)(o.A,{className:"h-4 w-4"})})}),(0,a.jsx)(l.p4,{children:s})]})});p.displayName=l.q7.displayName,s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(l.wv,{ref:t,className:(0,d.cn)("-mx-1 my-1 h-px bg-muted",r),...s})}).displayName=l.wv.displayName},62523:(e,t,r)=>{r.d(t,{p:()=>n});var a=r(95155),s=r(12115),l=r(59434);let n=s.forwardRef((e,t)=>{let{className:r,type:s,...n}=e;return(0,a.jsx)("input",{type:s,className:(0,l.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",r),ref:t,...n})});n.displayName="Input"},66424:(e,t,r)=>{r.d(t,{F:()=>i});var a=r(95155),s=r(12115),l=r(47655),n=r(59434);let i=s.forwardRef((e,t)=>{let{className:r,children:s,...i}=e;return(0,a.jsxs)(l.bL,{ref:t,className:(0,n.cn)("relative overflow-hidden",r),...i,children:[(0,a.jsx)(l.LM,{className:"h-full w-full rounded-[inherit]",children:s}),(0,a.jsx)(o,{}),(0,a.jsx)(l.OK,{})]})});i.displayName=l.bL.displayName;let o=s.forwardRef((e,t)=>{let{className:r,orientation:s="vertical",...i}=e;return(0,a.jsx)(l.VM,{ref:t,orientation:s,className:(0,n.cn)("flex touch-none select-none transition-colors","vertical"===s&&"h-full w-2.5 border-l border-l-transparent p-[1px]","horizontal"===s&&"h-2.5 flex-col border-t border-t-transparent p-[1px]",r),...i,children:(0,a.jsx)(l.lr,{className:"relative flex-1 rounded-full bg-border"})})});o.displayName=l.VM.displayName},85057:(e,t,r)=>{r.d(t,{J:()=>d});var a=r(95155),s=r(12115),l=r(40968),n=r(74466),i=r(59434);let o=(0,n.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(l.b,{ref:t,className:(0,i.cn)(o(),r),...s})});d.displayName=l.b.displayName},87481:(e,t,r)=>{r.d(t,{dj:()=>u});var a=r(12115);let s=0,l=new Map,n=e=>{if(l.has(e))return;let t=setTimeout(()=>{l.delete(e),c({type:"REMOVE_TOAST",toastId:e})},1e6);l.set(e,t)},i=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:r}=t;return r?n(r):e.toasts.forEach(e=>{n(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===r||void 0===r?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},o=[],d={toasts:[]};function c(e){d=i(d,e),o.forEach(e=>{e(d)})}function m(e){let{...t}=e,r=(s=(s+1)%Number.MAX_SAFE_INTEGER).toString(),a=()=>c({type:"DISMISS_TOAST",toastId:r});return c({type:"ADD_TOAST",toast:{...t,id:r,open:!0,onOpenChange:e=>{e||a()}}}),{id:r,dismiss:a,update:e=>c({type:"UPDATE_TOAST",toast:{...e,id:r}})}}function u(){let[e,t]=a.useState(d);return a.useEffect(()=>(o.push(t),()=>{let e=o.indexOf(t);e>-1&&o.splice(e,1)}),[e]),{...e,toast:m,dismiss:e=>c({type:"DISMISS_TOAST",toastId:e})}}},88539:(e,t,r)=>{r.d(t,{T:()=>n});var a=r(95155),s=r(12115),l=r(59434);let n=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("textarea",{className:(0,l.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",r),ref:t,...s})});n.displayName="Textarea"},99673:(e,t,r)=>{function a(e){switch(e){case"In_Progress":return"In Progress";case"No_details":return"No Details";default:return e}}r.d(t,{fZ:()=>a})}}]);