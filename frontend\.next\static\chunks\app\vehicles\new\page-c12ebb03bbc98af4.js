(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3667],{1441:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>u});var i=a(95155),r=a(12115),l=a(35695),d=a(36521),n=a(2730),s=a(95647),c=a(28328),o=a(87481);let u=()=>{let e=(0,l.useRouter)(),{toast:t}=(0,o.dj)(),[a,u]=(0,r.useState)(!1),[h,m]=(0,r.useState)(null),v=async a=>{u(!0),m(null);try{let i={make:a.make,model:a.model,year:a.year,vin:a.vin,licensePlate:a.licensePlate,ownerName:a.ownerName,ownerContact:a.ownerContact,initialOdometer:void 0===a.initialOdometer?0:a.initialOdometer,color:a.color,imageUrl:a.imageUrl},r=await (0,n.addVehicle)(i);t({title:"Vehicle Added",description:"".concat(r.make," ").concat(r.model," has been successfully added."),variant:"default"}),e.push("/vehicles")}catch(e){console.error("Failed to add vehicle:",e),m(e.message||"An unexpected error occurred. Please try again."),t({title:"Error Adding Vehicle",description:e.message||"Could not add the vehicle. Please check the details and try again.",variant:"destructive"})}finally{u(!1)}};return(0,i.jsxs)("div",{className:"container mx-auto py-8 space-y-8",children:[(0,i.jsx)(s.z,{title:"Add New Vehicle",description:"Enter the details for your new vehicle.",icon:c.A}),h&&(0,i.jsxs)("p",{className:"text-red-500 bg-red-100 p-3 rounded-md",children:["Error: ",h]}),(0,i.jsx)(d.A,{onSubmit:v,isEditing:!1,isLoading:a})]})}},45256:(e,t,a)=>{Promise.resolve().then(a.bind(a,1441))}},e=>{var t=t=>e(e.s=t);e.O(0,[3796,6113,832,4066,8162,2730,9634,8441,1684,7358],()=>t(45256)),_N_E=e.O()}]);