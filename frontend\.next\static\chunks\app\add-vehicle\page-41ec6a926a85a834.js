(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9527],{57541:(e,s,i)=>{"use strict";i.r(s),i.d(s,{default:()=>d});var t=i(95155),c=i(36521),l=i(2730),n=i(35695),r=i(95647),u=i(28328);function d(){let e=(0,n.useRouter)();return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)(r.z,{title:"Add New Vehicle",description:"Enter the details of your new vehicle.",icon:u.A}),(0,t.jsx)(c.A,{onSubmit:s=>{(0,l.addVehicle)(s),e.push("/vehicles")}})]})}},67569:(e,s,i)=>{Promise.resolve().then(i.bind(i,57541))}},e=>{var s=s=>e(e.s=s);e.O(0,[3796,6113,832,4066,8162,2730,9634,8441,1684,7358],()=>s(67569)),_N_E=e.O()}]);