(()=>{var e={};e.id=393,e.ids=[393],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},34937:(e,t,r)=>{Promise.resolve().then(r.bind(r,80351))},35137:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(82614).A)("SquarePen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},50337:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>x});var s=r(60687),a=r(43210),i=r(16189),o=r(37716),n=r(28840),d=r(48041),l=r(35137),p=r(28399),c=r(29867),u=r(85726),m=r(29523);let x=()=>{let e=(0,i.useRouter)(),t=(0,i.useParams)(),{toast:r}=(0,c.dj)(),x=t.id,y=parseInt(x,10),[h,v]=(0,a.useState)(null),[f,g]=(0,a.useState)(!1),[b,j]=(0,a.useState)(!0),[E,k]=(0,a.useState)(null),S=(0,a.useCallback)(async()=>{if(isNaN(y)){k("Invalid employee ID provided."),j(!1);return}j(!0);try{let e=await (0,n.getEmployeeById)(y);e?v(e):k("Employee not found.")}catch(e){console.error("Failed to fetch employee:",e),k(e.message||"Failed to load employee data.")}finally{j(!1)}},[y]);(0,a.useEffect)(()=>{x&&S()},[x,S]);let q=async t=>{if(isNaN(y))return void k("Cannot update employee without a valid ID.");g(!0),k(null);try{await (0,n.updateEmployee)(y,t),r({title:"Employee Updated",description:`${t.name} has been successfully updated.`,variant:"default"}),e.push("/employees")}catch(t){console.error("Failed to update employee:",t);let e=t.response?.data?.error||t.message||"An unexpected error occurred.";k(e),r({title:"Error Updating Employee",description:e,variant:"destructive"})}finally{g(!1)}};return b?(0,s.jsxs)("div",{className:"container mx-auto py-8 space-y-8",children:[(0,s.jsx)(d.z,{title:"Edit Employee",description:"Loading employee details...",icon:l.A}),(0,s.jsxs)("div",{className:"max-w-3xl mx-auto space-y-6",children:[(0,s.jsx)(u.E,{className:"h-10 w-1/3"}),[...Array(6)].map((e,t)=>(0,s.jsx)(u.E,{className:"h-12 w-full"},t)),(0,s.jsxs)("div",{className:"flex justify-end space-x-3 pt-6",children:[(0,s.jsx)(u.E,{className:"h-10 w-24"}),(0,s.jsx)(u.E,{className:"h-10 w-24"})]})]})]}):E&&!h?(0,s.jsxs)("div",{className:"container mx-auto py-8 space-y-8 text-center",children:[(0,s.jsx)(d.z,{title:"Error",description:E,icon:p.A}),(0,s.jsx)(m.$,{onClick:()=>e.push("/employees"),children:"Back to Employees"})]}):(0,s.jsxs)("div",{className:"container mx-auto py-8 space-y-8",children:[(0,s.jsx)(d.z,{title:"Edit Employee",description:`Update details for ${h?.name||h?.fullName||"employee"}`,icon:l.A}),E&&h&&(0,s.jsxs)("div",{className:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4",role:"alert",children:[(0,s.jsx)("strong",{className:"font-bold",children:"Error: "}),(0,s.jsx)("span",{className:"block sm:inline",children:E})]}),h&&(0,s.jsx)(o.A,{onSubmit:q,initialData:h,isEditing:!0,isLoading:f})]})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68317:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>c,pages:()=>p,routeModule:()=>u,tree:()=>l});var s=r(65239),a=r(48088),i=r(88170),o=r.n(i),n=r(30893),d={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);r.d(t,d);let l={children:["",{children:["employees",{children:["edit",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,80351)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\employees\\edit\\[id]\\page.tsx"]}]},{}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,p=["C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\employees\\edit\\[id]\\page.tsx"],c={require:r,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/employees/edit/[id]/page",pathname:"/employees/edit/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},71385:(e,t,r)=>{Promise.resolve().then(r.bind(r,50337))},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},80351:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\projects\\\\CarServiceTrackingSystem\\\\TrackerSystem\\\\frontend\\\\src\\\\app\\\\employees\\\\edit\\\\[id]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\employees\\edit\\[id]\\page.tsx","default")},81630:e=>{"use strict";e.exports=require("http")},85726:(e,t,r)=>{"use strict";r.d(t,{E:()=>i});var s=r(60687),a=r(4780);function i({className:e,...t}){return(0,s.jsx)("div",{className:(0,a.cn)("animate-pulse rounded-md bg-muted",e),...t})}},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,3622,1658,5880,2729,3442,8141,3983,3860],()=>r(68317));module.exports=s})();