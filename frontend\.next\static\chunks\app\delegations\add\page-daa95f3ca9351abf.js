(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5534],{6560:(e,a,r)=>{"use strict";r.d(a,{r:()=>l});var t=r(95155),s=r(12115),d=r(30285),i=r(50172),n=r(59434);let l=s.forwardRef((e,a)=>{let{actionType:r="primary",icon:s,isLoading:l=!1,loadingText:c,className:o,children:m,disabled:f,asChild:u=!1,...x}=e,{variant:N,className:p}={primary:{variant:"default",className:"shadow-md"},secondary:{variant:"secondary",className:""},tertiary:{variant:"outline",className:""},danger:{variant:"destructive",className:"shadow-md"}}[r];return(0,t.jsx)(d.$,{ref:a,variant:N,className:(0,n.cn)(p,o),disabled:l||f,asChild:u,...x,children:l?(0,t.jsxs)("span",{className:"inline-flex items-center",children:[" ",(0,t.jsx)(i.A,{className:"mr-2 h-4 w-4 animate-spin"}),c||m]}):(0,t.jsxs)("span",{className:"inline-flex items-center",children:[" ",s&&(0,t.jsx)("span",{className:"mr-2",children:s}),m]})})});l.displayName="ActionButton"},18115:(e,a,r)=>{Promise.resolve().then(r.bind(r,71616))},66695:(e,a,r)=>{"use strict";r.d(a,{BT:()=>c,Wu:()=>o,ZB:()=>l,Zp:()=>i,aR:()=>n,wL:()=>m});var t=r(95155),s=r(12115),d=r(59434);let i=s.forwardRef((e,a)=>{let{className:r,...s}=e;return(0,t.jsx)("div",{ref:a,className:(0,d.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",r),...s})});i.displayName="Card";let n=s.forwardRef((e,a)=>{let{className:r,...s}=e;return(0,t.jsx)("div",{ref:a,className:(0,d.cn)("flex flex-col space-y-1.5 p-6",r),...s})});n.displayName="CardHeader";let l=s.forwardRef((e,a)=>{let{className:r,...s}=e;return(0,t.jsx)("div",{ref:a,className:(0,d.cn)("text-2xl font-semibold leading-none tracking-tight",r),...s})});l.displayName="CardTitle";let c=s.forwardRef((e,a)=>{let{className:r,...s}=e;return(0,t.jsx)("div",{ref:a,className:(0,d.cn)("text-sm text-muted-foreground",r),...s})});c.displayName="CardDescription";let o=s.forwardRef((e,a)=>{let{className:r,...s}=e;return(0,t.jsx)("div",{ref:a,className:(0,d.cn)("p-6 pt-0",r),...s})});o.displayName="CardContent";let m=s.forwardRef((e,a)=>{let{className:r,...s}=e;return(0,t.jsx)("div",{ref:a,className:(0,d.cn)("flex items-center p-6 pt-0",r),...s})});m.displayName="CardFooter"},71616:(e,a,r)=>{"use strict";r.r(a),r.d(a,{default:()=>o});var t=r(95155),s=r(33908),d=r(2730),i=r(35695),n=r(95647),l=r(57082),c=r(87481);function o(){let e=(0,i.useRouter)(),{toast:a}=(0,c.dj)(),r=async r=>{try{await (0,d.addDelegation)(r),a({title:"Delegation Added",description:'The delegation "'.concat(r.eventName,'" has been successfully created.'),variant:"default"}),e.push("/delegations")}catch(e){console.error("Error adding delegation:",e),a({title:"Error",description:"Failed to add delegation. Please try again.",variant:"destructive"})}};return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)(n.z,{title:"Add New Delegation",description:"Enter the details for the new delegation or event.",icon:l.A}),(0,t.jsx)(s.A,{onSubmit:r,isEditing:!1})]})}},95647:(e,a,r)=>{"use strict";r.d(a,{z:()=>s});var t=r(95155);function s(e){let{title:a,description:r,icon:s,children:d}=e;return(0,t.jsxs)("div",{className:"mb-6 pb-4 border-b border-border/50 flex justify-between items-center",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[s&&(0,t.jsx)(s,{className:"h-8 w-8 text-primary"}),(0,t.jsx)("h1",{className:"text-3xl font-bold tracking-tight text-foreground",children:a})]}),r&&(0,t.jsx)("p",{className:"text-muted-foreground mt-1",children:r})]}),d&&(0,t.jsx)("div",{className:"flex items-center gap-2",children:d})]})}r(12115)}},e=>{var a=a=>e(e.s=a);e.O(0,[3796,6113,832,2688,2512,1859,4066,8241,3229,3801,8162,2730,3908,8441,1684,7358],()=>a(18115)),_N_E=e.O()}]);