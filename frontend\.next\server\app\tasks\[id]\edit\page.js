(()=>{var e={};e.id=4344,e.ids=[4344],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29909:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>l,tree:()=>p});var s=r(65239),i=r(48088),a=r(88170),n=r.n(a),o=r(30893),d={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>o[e]);r.d(t,d);let p={children:["",{children:["tasks",{children:["[id]",{children:["edit",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,88658)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\tasks\\[id]\\edit\\page.tsx"]}]},{}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\tasks\\[id]\\edit\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},l=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/tasks/[id]/edit/page",pathname:"/tasks/[id]/edit",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:p}})},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},38832:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l});var s=r(60687),i=r(43210),a=r(16189),n=r(91188),o=r(28840),d=r(48041);let p=(0,r(82614).A)("FilePen",[["path",{d:"M12.5 22H18a2 2 0 0 0 2-2V7l-5-5H6a2 2 0 0 0-2 2v9.5",key:"1couwa"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M13.378 15.626a1 1 0 1 0-3.004-3.004l-5.01 5.012a2 2 0 0 0-.506.854l-.837 2.87a.5.5 0 0 0 .62.62l2.87-.837a2 2 0 0 0 .854-.506z",key:"1y4qbx"}]]);var c=r(29867),u=r(85726);function l(){let e=(0,a.useRouter)(),t=(0,a.useParams)(),{toast:r}=(0,c.dj)(),[l,x]=(0,i.useState)(null),[m,h]=(0,i.useState)(!0),v=t.id,f=async t=>{if(v&&l)try{let s=await (0,o.updateTask)(v,t);s?(r({title:"Task Updated",description:`The task "${s.description.substring(0,30)}..." has been successfully updated.`,variant:"default"}),e.push(`/tasks/${v}`)):r({title:"Error",description:"Failed to update task.",variant:"destructive"})}catch(e){console.error("Error updating task:",e),r({title:"Error",description:"Failed to update task. Please try again.",variant:"destructive"})}};return m?(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsx)(d.z,{title:"Loading...",icon:p}),(0,s.jsx)(u.E,{className:"h-[600px] w-full rounded-lg bg-card"})]}):l?(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsx)(d.z,{title:`Edit Task: ${l.description.substring(0,50)}${l.description.length>50?"...":""}`,description:"Modify the details for this task.",icon:p}),(0,s.jsx)(n.A,{onSubmit:f,initialData:l,isEditing:!0})]}):(0,s.jsx)("p",{children:"Task not found."})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},59468:(e,t,r)=>{Promise.resolve().then(r.bind(r,38832))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},72620:(e,t,r)=>{Promise.resolve().then(r.bind(r,88658))},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},85726:(e,t,r)=>{"use strict";r.d(t,{E:()=>a});var s=r(60687),i=r(4780);function a({className:e,...t}){return(0,s.jsx)("div",{className:(0,i.cn)("animate-pulse rounded-md bg-muted",e),...t})}},88658:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\projects\\\\CarServiceTrackingSystem\\\\TrackerSystem\\\\frontend\\\\src\\\\app\\\\tasks\\\\[id]\\\\edit\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\tasks\\[id]\\edit\\page.tsx","default")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,3622,1658,5880,2729,3442,8141,3983,3348],()=>r(29909));module.exports=s})();