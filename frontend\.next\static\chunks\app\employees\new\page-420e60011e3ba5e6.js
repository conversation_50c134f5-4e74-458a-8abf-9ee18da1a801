(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6841],{46143:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>u});var t=s(95155),l=s(12115),a=s(35695),d=s(11612),n=s(2730),o=s(95647),i=s(79239),c=s(87481);let u=()=>{let e=(0,a.useRouter)(),{toast:r}=(0,c.dj)(),[s,u]=(0,l.useState)(!1),[p,m]=(0,l.useState)(null),y=async s=>{u(!0),m(null);try{let t=await (0,n.addEmployee)(s);r({title:"Employee Added",description:"".concat(t.name||t.fullName," has been successfully added."),variant:"default"}),e.push("/employees")}catch(s){var t,l;console.error("Failed to add employee:",s);let e=(null==(l=s.response)||null==(t=l.data)?void 0:t.error)||s.message||"An unexpected error occurred.";m(e),r({title:"Error Adding Employee",description:e,variant:"destructive"})}finally{u(!1)}};return(0,t.jsxs)("div",{className:"container mx-auto py-8 space-y-8",children:[(0,t.jsx)(o.z,{title:"Add New Employee",description:"Enter the details for the new employee.",icon:i.A}),p&&(0,t.jsxs)("div",{className:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4",role:"alert",children:[(0,t.jsx)("strong",{className:"font-bold",children:"Error: "}),(0,t.jsx)("span",{className:"block sm:inline",children:p})]}),(0,t.jsx)(d.A,{onSubmit:y,isEditing:!1,isLoading:s})]})}},53096:(e,r,s)=>{Promise.resolve().then(s.bind(s,46143))},79239:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(40157).A)("UsersRound",[["path",{d:"M18 21a8 8 0 0 0-16 0",key:"3ypg7q"}],["circle",{cx:"10",cy:"8",r:"5",key:"o932ke"}],["path",{d:"M22 20c0-3.37-2-6.5-4-8a5 5 0 0 0-.45-8.3",key:"10s06x"}]])}},e=>{var r=r=>e(e.s=r);e.O(0,[3796,6113,832,2688,2512,1859,4066,8162,2730,3222,8441,1684,7358],()=>r(53096)),_N_E=e.O()}]);