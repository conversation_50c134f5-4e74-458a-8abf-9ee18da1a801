exports.id=8141,exports.ids=[8141],exports.modules={4780:(e,r,t)=>{"use strict";t.d(r,{cn:()=>n});var s=t(49384),a=t(82348);function n(...e){return(0,a.QP)((0,s.$)(e))}},10379:(e,r,t)=>{"use strict";t.d(r,{A:()=>l});var s=t(43210),a=t(94025);let n="https://abylqjnpaegeqwktcukn.supabase.co",i="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFieWxxam5wYWVnZXF3a3RjdWtuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDcyMTM0NTMsImV4cCI6MjA2Mjc4OTQ1M30.WCzj8fDu7vdxhvbOUuoQHVamy9-XYBr4vtTox52ap2o";if(!n||!i)throw Error("Missing Supabase environment variables. Please check NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY in your .env.local file");let o=(0,a.UU)(n,i);function l(){let[e,r]=(0,s.useState)({user:null,session:null,loading:!0,error:null}),t=(0,s.useCallback)(()=>{r(e=>({...e,error:null}))},[]),a=(0,s.useCallback)(async(e,t)=>{r(e=>({...e,loading:!0,error:null}));try{let{data:s,error:a}=await o.auth.signInWithPassword({email:e,password:t});if(a)return r(e=>({...e,loading:!1,error:a.message})),{error:a};return r(e=>({...e,loading:!1})),{error:null}}catch(t){let e=t.message||"An unexpected error occurred during sign in";return r(r=>({...r,loading:!1,error:e})),{error:{message:e}}}},[]),n=(0,s.useCallback)(async(e,t,s)=>{r(e=>({...e,loading:!0,error:null}));try{let{data:a,error:n}=await o.auth.signUp({email:e,password:t,options:{data:s||{}}});if(n)return r(e=>({...e,loading:!1,error:n.message})),{error:n};return r(e=>({...e,loading:!1})),{error:null}}catch(t){let e=t.message||"An unexpected error occurred during sign up";return r(r=>({...r,loading:!1,error:e})),{error:{message:e}}}},[]),i=(0,s.useCallback)(async()=>{r(e=>({...e,loading:!0,error:null}));try{let{error:e}=await o.auth.signOut();if(e)return r(r=>({...r,loading:!1,error:e.message})),{error:e};return{error:null}}catch(t){let e=t.message||"An unexpected error occurred during sign out";return r(r=>({...r,loading:!1,error:e})),{error:{message:e}}}},[]),l=(0,s.useCallback)(async e=>{r(e=>({...e,loading:!0,error:null}));try{let{error:t}=await o.auth.resetPasswordForEmail(e,{redirectTo:`${window.location.origin}/reset-password`});if(t)return r(e=>({...e,loading:!1,error:t.message})),{error:t};return r(e=>({...e,loading:!1})),{error:null}}catch(t){let e=t.message||"An unexpected error occurred during password reset";return r(r=>({...r,loading:!1,error:e})),{error:{message:e}}}},[]);return{...e,signIn:a,signUp:n,signOut:i,resetPassword:l,clearError:t}}},21342:(e,r,t)=>{"use strict";t.d(r,{SQ:()=>m,_2:()=>f,lp:()=>h,mB:()=>p,rI:()=>c,ty:()=>u});var s=t(60687),a=t(43210),n=t(26312),i=t(74158),o=t(58450),l=t(73256),d=t(4780);let c=n.bL,u=n.l9;n.YJ,n.ZL,n.Pb,n.z6,a.forwardRef(({className:e,inset:r,children:t,...a},o)=>(0,s.jsxs)(n.ZP,{ref:o,className:(0,d.cn)("flex cursor-default gap-2 select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",r&&"pl-8",e),...a,children:[t,(0,s.jsx)(i.A,{className:"ml-auto"})]})).displayName=n.ZP.displayName,a.forwardRef(({className:e,...r},t)=>(0,s.jsx)(n.G5,{ref:t,className:(0,d.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...r})).displayName=n.G5.displayName;let m=a.forwardRef(({className:e,sideOffset:r=4,...t},a)=>(0,s.jsx)(n.ZL,{children:(0,s.jsx)(n.UC,{ref:a,sideOffset:r,className:(0,d.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...t})}));m.displayName=n.UC.displayName;let f=a.forwardRef(({className:e,inset:r,...t},a)=>(0,s.jsx)(n.q7,{ref:a,className:(0,d.cn)("relative flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",r&&"pl-8",e),...t}));f.displayName=n.q7.displayName,a.forwardRef(({className:e,children:r,checked:t,...a},i)=>(0,s.jsxs)(n.H_,{ref:i,className:(0,d.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),checked:t,...a,children:[(0,s.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,s.jsx)(n.VF,{children:(0,s.jsx)(o.A,{className:"h-4 w-4"})})}),r]})).displayName=n.H_.displayName,a.forwardRef(({className:e,children:r,...t},a)=>(0,s.jsxs)(n.hN,{ref:a,className:(0,d.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...t,children:[(0,s.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,s.jsx)(n.VF,{children:(0,s.jsx)(l.A,{className:"h-2 w-2 fill-current"})})}),r]})).displayName=n.hN.displayName;let h=a.forwardRef(({className:e,inset:r,...t},a)=>(0,s.jsx)(n.JU,{ref:a,className:(0,d.cn)("px-2 py-1.5 text-sm font-semibold",r&&"pl-8",e),...t}));h.displayName=n.JU.displayName;let p=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)(n.wv,{ref:t,className:(0,d.cn)("-mx-1 my-1 h-px bg-muted",e),...r}));p.displayName=n.wv.displayName},21408:(e,r,t)=>{Promise.resolve().then(t.bind(t,94431))},27965:(e,r,t)=>{"use strict";t.d(r,{OJ:()=>j.OJ,OV:()=>w,Fv:()=>R});var s=t(60687),a=t(43210),n=t(72963),i=t(27247),o=t(85535),l=t(11003),d=t(76311),c=t(11516),u=t(29523),m=t(89667),f=t(80013),h=t(44493),p=t(91821),x=t(10379);function g({onSuccess:e,onForgotPassword:r,onSignUp:t}){let{signIn:g,loading:y,error:v,clearError:j}=(0,x.A)(),[w,b]=(0,a.useState)({email:"",password:""}),[N,E]=(0,a.useState)(!1),[T,A]=(0,a.useState)({}),R=()=>{let e={};return w.email?/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(w.email)||(e.email="Please enter a valid email address"):e.email="Email is required",w.password?w.password.length<6&&(e.password="Password must be at least 6 characters"):e.password="Password is required",A(e),0===Object.keys(e).length},S=async r=>{if(r.preventDefault(),j(),A({}),R())try{let{error:r}=await g(w.email,w.password);r||e?.()}catch(e){console.error("Login error:",e)}},_=(e,r)=>{b(t=>({...t,[e]:r})),T[e]&&A(r=>({...r,[e]:""})),v&&j()};return(0,s.jsxs)(h.Zp,{className:"w-full max-w-md mx-auto",children:[(0,s.jsxs)(h.aR,{className:"space-y-1",children:[(0,s.jsx)(h.ZB,{className:"text-2xl font-bold text-center",children:"\uD83D\uDEA8 WorkHub Login"}),(0,s.jsx)(h.BT,{className:"text-center",children:"Emergency Security Access - Enter your credentials to continue"})]}),(0,s.jsxs)(h.Wu,{children:[(0,s.jsxs)("form",{onSubmit:S,className:"space-y-4",children:[v&&(0,s.jsxs)(p.Fc,{variant:"destructive",children:[(0,s.jsx)(n.A,{className:"h-4 w-4"}),(0,s.jsx)(p.TN,{children:v})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(f.J,{htmlFor:"email",children:"Email Address"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(i.A,{className:"absolute left-3 top-3 h-4 w-4 text-muted-foreground"}),(0,s.jsx)(m.p,{id:"email",type:"email",placeholder:"Enter your email",value:w.email,onChange:e=>_("email",e.target.value),className:`pl-10 ${T.email?"border-red-500":""}`,disabled:y,autoComplete:"email"})]}),T.email&&(0,s.jsx)("p",{className:"text-sm text-red-500",children:T.email})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(f.J,{htmlFor:"password",children:"Password"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(o.A,{className:"absolute left-3 top-3 h-4 w-4 text-muted-foreground"}),(0,s.jsx)(m.p,{id:"password",type:N?"text":"password",placeholder:"Enter your password",value:w.password,onChange:e=>_("password",e.target.value),className:`pl-10 pr-10 ${T.password?"border-red-500":""}`,disabled:y,autoComplete:"current-password"}),(0,s.jsx)("button",{type:"button",onClick:()=>E(!N),className:"absolute right-3 top-3 h-4 w-4 text-muted-foreground hover:text-foreground",disabled:y,children:N?(0,s.jsx)(l.A,{}):(0,s.jsx)(d.A,{})})]}),T.password&&(0,s.jsx)("p",{className:"text-sm text-red-500",children:T.password})]}),(0,s.jsx)(u.$,{type:"submit",className:"w-full",disabled:y,children:y?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(c.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Signing In..."]}):"Sign In"}),(0,s.jsxs)("div",{className:"space-y-2 text-center",children:[r&&(0,s.jsx)("button",{type:"button",onClick:r,className:"text-sm text-muted-foreground hover:text-foreground underline",disabled:y,children:"Forgot your password?"}),t&&(0,s.jsxs)("div",{className:"text-sm text-muted-foreground",children:["Don't have an account?"," ",(0,s.jsx)("button",{type:"button",onClick:t,className:"text-foreground hover:underline font-medium",disabled:y,children:"Sign up here"})]})]})]}),(0,s.jsx)("div",{className:"mt-6 p-3 bg-yellow-50 border border-yellow-200 rounded-md",children:(0,s.jsxs)("div",{className:"flex items-start",children:[(0,s.jsx)(n.A,{className:"h-4 w-4 text-yellow-600 mt-0.5 mr-2 flex-shrink-0"}),(0,s.jsxs)("div",{className:"text-xs text-yellow-800",children:[(0,s.jsx)("strong",{children:"Emergency Security Mode:"})," This system is operating under enhanced security protocols. All access attempts are logged and monitored."]})]})})]})]})}var y=t(14975),v=t(53597),j=t(63213);function w({children:e,fallback:r,requireEmailVerification:t=!0,allowedRoles:a=[]}){let{user:n,session:i,loading:o,error:l}=(0,j.Z2)();if(o)return(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,s.jsx)(h.Zp,{className:"w-full max-w-md mx-auto",children:(0,s.jsxs)(h.Wu,{className:"flex flex-col items-center justify-center py-8",children:[(0,s.jsx)(c.A,{className:"h-8 w-8 animate-spin text-blue-600 mb-4"}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"\uD83D\uDEA8 Verifying security credentials..."})]})})});if(l&&!n)return(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 p-4",children:(0,s.jsxs)(h.Zp,{className:"w-full max-w-md mx-auto",children:[(0,s.jsxs)(h.aR,{children:[(0,s.jsxs)(h.ZB,{className:"flex items-center text-red-600",children:[(0,s.jsx)(y.A,{className:"h-5 w-5 mr-2"}),"Authentication Error"]}),(0,s.jsx)(h.BT,{children:"There was a problem with the security system"})]}),(0,s.jsxs)(h.Wu,{children:[(0,s.jsx)(p.Fc,{variant:"destructive",children:(0,s.jsx)(p.TN,{children:l})}),(0,s.jsx)("div",{className:"mt-4",children:(0,s.jsx)(g,{})})]})]})});if(!n||!i)return r?(0,s.jsx)(s.Fragment,{children:r}):(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 p-4",children:(0,s.jsx)("div",{className:"w-full max-w-md",children:(0,s.jsx)(g,{onSuccess:()=>{console.log("✅ User authenticated successfully")}})})});if(t&&!n.email_confirmed_at)return(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 p-4",children:(0,s.jsxs)(h.Zp,{className:"w-full max-w-md mx-auto",children:[(0,s.jsxs)(h.aR,{children:[(0,s.jsxs)(h.ZB,{className:"flex items-center text-yellow-600",children:[(0,s.jsx)(v.A,{className:"h-5 w-5 mr-2"}),"Email Verification Required"]}),(0,s.jsx)(h.BT,{children:"Please verify your email address to continue"})]}),(0,s.jsxs)(h.Wu,{children:[(0,s.jsxs)(p.Fc,{children:[(0,s.jsx)(y.A,{className:"h-4 w-4"}),(0,s.jsxs)(p.TN,{children:["We've sent a verification email to ",(0,s.jsx)("strong",{children:n.email}),". Please check your inbox and click the verification link to access the system."]})]}),(0,s.jsx)("div",{className:"mt-4 text-center",children:(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"Didn't receive the email? Check your spam folder or contact your administrator."})})]})]})});if(a.length>0){let e=n.user_metadata?.role||"USER";if(!a.includes(e))return(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 p-4",children:(0,s.jsxs)(h.Zp,{className:"w-full max-w-md mx-auto",children:[(0,s.jsxs)(h.aR,{children:[(0,s.jsxs)(h.ZB,{className:"flex items-center text-red-600",children:[(0,s.jsx)(v.A,{className:"h-5 w-5 mr-2"}),"Access Denied"]}),(0,s.jsx)(h.BT,{children:"Insufficient permissions to access this resource"})]}),(0,s.jsxs)(h.Wu,{children:[(0,s.jsx)(p.Fc,{variant:"destructive",children:(0,s.jsxs)(p.TN,{children:["Your account (",e,") does not have permission to access this area. Required roles: ",a.join(", ")]})}),(0,s.jsx)("div",{className:"mt-4 text-center",children:(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"Contact your administrator if you believe this is an error."})})]})]})})}return(0,s.jsx)(s.Fragment,{children:e})}var b=t(49497),N=t(58595),E=t(96834),T=t(21342),A=t(32584);function R({variant:e="dropdown",showSignOut:r=!0}){let{user:t,signOut:n,loading:o}=(0,j.Z2)(),[l,d]=(0,a.useState)(!1);if(!t)return null;let m=async()=>{d(!0);try{await n()}catch(e){console.error("Sign out error:",e)}finally{d(!1)}},f=e=>e.substring(0,2).toUpperCase(),x=e=>e?new Date(e).toLocaleDateString():"Never",g=()=>t.user_metadata?.role||"USER",y=e=>{switch(e.toUpperCase()){case"ADMIN":return"destructive";case"MANAGER":return"default";default:return"secondary"}};return"dropdown"===e?(0,s.jsxs)(T.rI,{children:[(0,s.jsx)(T.ty,{asChild:!0,children:(0,s.jsx)(u.$,{variant:"ghost",className:"relative h-8 w-8 rounded-full",children:(0,s.jsxs)(A.eu,{className:"h-8 w-8",children:[(0,s.jsx)(A.BK,{src:t.user_metadata?.avatar_url,alt:t.email||""}),(0,s.jsx)(A.q5,{children:f(t.email||"U")})]})})}),(0,s.jsxs)(T.SQ,{className:"w-56",align:"end",forceMount:!0,children:[(0,s.jsx)(T.lp,{className:"font-normal",children:(0,s.jsxs)("div",{className:"flex flex-col space-y-1",children:[(0,s.jsx)("p",{className:"text-sm font-medium leading-none",children:t.user_metadata?.full_name||t.email}),(0,s.jsx)("p",{className:"text-xs leading-none text-muted-foreground",children:t.email}),(0,s.jsxs)("div",{className:"flex items-center gap-2 mt-1",children:[(0,s.jsx)(E.E,{variant:y(g()),className:"text-xs",children:g()}),t.email_confirmed_at&&(0,s.jsxs)(E.E,{variant:"outline",className:"text-xs",children:[(0,s.jsx)(v.A,{className:"h-3 w-3 mr-1"}),"Verified"]})]})]})}),(0,s.jsx)(T.mB,{}),r&&(0,s.jsx)(T._2,{onClick:m,disabled:l,className:"text-red-600 focus:text-red-600",children:l?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(c.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Signing out..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(b.A,{className:"mr-2 h-4 w-4"}),"Sign out"]})})]})]}):(0,s.jsxs)(h.Zp,{className:"w-full max-w-md",children:[(0,s.jsxs)(h.aR,{children:[(0,s.jsxs)(h.ZB,{className:"flex items-center gap-2",children:[(0,s.jsx)(N.A,{className:"h-5 w-5"}),"User Profile"]}),(0,s.jsx)(h.BT,{children:"\uD83D\uDEA8 Emergency Security - Current session information"})]}),(0,s.jsxs)(h.Wu,{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsxs)(A.eu,{className:"h-16 w-16",children:[(0,s.jsx)(A.BK,{src:t.user_metadata?.avatar_url,alt:t.email||""}),(0,s.jsx)(A.q5,{className:"text-lg",children:f(t.email||"U")})]}),(0,s.jsxs)("div",{className:"space-y-1",children:[(0,s.jsx)("h3",{className:"font-medium",children:t.user_metadata?.full_name||"User"}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(i.A,{className:"h-4 w-4 text-muted-foreground"}),(0,s.jsx)("span",{className:"text-sm text-muted-foreground",children:t.email})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(E.E,{variant:y(g()),children:g()}),t.email_confirmed_at&&(0,s.jsxs)(E.E,{variant:"outline",children:[(0,s.jsx)(v.A,{className:"h-3 w-3 mr-1"}),"Verified"]})]})]})]}),(0,s.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("span",{className:"text-muted-foreground",children:"User ID:"}),(0,s.jsxs)("span",{className:"font-mono text-xs",children:[t.id.substring(0,8),"..."]})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("span",{className:"text-muted-foreground",children:"Email Verified:"}),(0,s.jsx)("span",{children:t.email_confirmed_at?"✅ Yes":"❌ No"})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("span",{className:"text-muted-foreground",children:"Last Sign In:"}),(0,s.jsx)("span",{children:x(t.last_sign_in_at)})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("span",{className:"text-muted-foreground",children:"Created:"}),(0,s.jsx)("span",{children:x(t.created_at)})]})]}),(0,s.jsxs)(p.Fc,{children:[(0,s.jsx)(v.A,{className:"h-4 w-4"}),(0,s.jsx)(p.TN,{className:"text-xs",children:"This session is protected by emergency security protocols. All activities are monitored and logged."})]}),r&&(0,s.jsx)(u.$,{onClick:m,disabled:l,variant:"outline",className:"w-full",children:l?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(c.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Signing out..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(b.A,{className:"mr-2 h-4 w-4"}),"Sign out"]})})]})]})}},29523:(e,r,t)=>{"use strict";t.d(r,{$:()=>d,r:()=>l});var s=t(60687),a=t(43210),n=t(8730),i=t(24224),o=t(4780);let l=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=a.forwardRef(({className:e,variant:r,size:t,asChild:a=!1,...i},d)=>{let c=a?n.DX:"button";return(0,s.jsx)(c,{className:(0,o.cn)(l({variant:r,size:t,className:e})),ref:d,...i})});d.displayName="Button"},29867:(e,r,t)=>{"use strict";t.d(r,{dj:()=>m});var s=t(43210);let a=0,n=new Map,i=e=>{if(n.has(e))return;let r=setTimeout(()=>{n.delete(e),c({type:"REMOVE_TOAST",toastId:e})},1e6);n.set(e,r)},o=(e,r)=>{switch(r.type){case"ADD_TOAST":return{...e,toasts:[r.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===r.toast.id?{...e,...r.toast}:e)};case"DISMISS_TOAST":{let{toastId:t}=r;return t?i(t):e.toasts.forEach(e=>{i(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===t||void 0===t?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===r.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==r.toastId)}}},l=[],d={toasts:[]};function c(e){d=o(d,e),l.forEach(e=>{e(d)})}function u({...e}){let r=(a=(a+1)%Number.MAX_SAFE_INTEGER).toString(),t=()=>c({type:"DISMISS_TOAST",toastId:r});return c({type:"ADD_TOAST",toast:{...e,id:r,open:!0,onOpenChange:e=>{e||t()}}}),{id:r,dismiss:t,update:e=>c({type:"UPDATE_TOAST",toast:{...e,id:r}})}}function m(){let[e,r]=s.useState(d);return s.useEffect(()=>(l.push(r),()=>{let e=l.indexOf(r);e>-1&&l.splice(e,1)}),[e]),{...e,toast:u,dismiss:e=>c({type:"DISMISS_TOAST",toastId:e})}}},32584:(e,r,t)=>{"use strict";t.d(r,{BK:()=>l,eu:()=>o,q5:()=>d});var s=t(60687),a=t(43210),n=t(11096),i=t(4780);let o=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)(n.bL,{ref:t,className:(0,i.cn)("relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full",e),...r}));o.displayName=n.bL.displayName;let l=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)(n._V,{ref:t,className:(0,i.cn)("aspect-square h-full w-full",e),...r}));l.displayName=n._V.displayName;let d=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)(n.H4,{ref:t,className:(0,i.cn)("flex h-full w-full items-center justify-center rounded-full bg-muted",e),...r}));d.displayName=n.H4.displayName},44263:()=>{},44493:(e,r,t)=>{"use strict";t.d(r,{BT:()=>d,Wu:()=>c,ZB:()=>l,Zp:()=>i,aR:()=>o,wL:()=>u});var s=t(60687),a=t(43210),n=t(4780);let i=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...r}));i.displayName="Card";let o=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",e),...r}));o.displayName="CardHeader";let l=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",e),...r}));l.displayName="CardTitle";let d=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,n.cn)("text-sm text-muted-foreground",e),...r}));d.displayName="CardDescription";let c=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,n.cn)("p-6 pt-0",e),...r}));c.displayName="CardContent";let u=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,n.cn)("flex items-center p-6 pt-0",e),...r}));u.displayName="CardFooter"},48653:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,86346,23)),Promise.resolve().then(t.t.bind(t,27924,23)),Promise.resolve().then(t.t.bind(t,35656,23)),Promise.resolve().then(t.t.bind(t,40099,23)),Promise.resolve().then(t.t.bind(t,38243,23)),Promise.resolve().then(t.t.bind(t,28827,23)),Promise.resolve().then(t.t.bind(t,62763,23)),Promise.resolve().then(t.t.bind(t,97173,23))},49254:(e,r,t)=>{"use strict";t.d(r,{Fd:()=>o,Fv:()=>y,Lv:()=>O,O5:()=>n,Oc:()=>g,Q3:()=>u,Qu:()=>w,UT:()=>E,VH:()=>_,Y$:()=>j,Y3:()=>k,Yv:()=>x,a9:()=>N,bV:()=>v,bt:()=>S,dC:()=>f,ih:()=>m,kw:()=>l,lC:()=>T,mh:()=>I,nz:()=>d,sI:()=>R,vq:()=>A,x1:()=>b,xY:()=>p,xn:()=>c,y_:()=>h});var s=t(65594);let a=null;function n(e){a=e}let i="http://localhost:3001/api";async function o(e,r){let t=`${i}${e}`,s={"Content-Type":"application/json",...r?.headers};a&&(s.Authorization=`Bearer ${a}`);let n={...r,headers:s};try{if(console.debug(`API Request: ${n?.method||"GET"} ${t}`),n?.body)try{console.debug("Request payload:",JSON.parse(n.body))}catch(e){console.debug("Request payload (non-JSON):",n.body)}let e=await fetch(t,n);if(!e.ok){let r;try{r=await e.json()}catch(t){r={message:e.statusText||`HTTP error ${e.status}`}}if(console.error(`API Error ${e.status} at ${t}:`,r),r?.status==="error"&&r?.message==="Validation failed"){console.error("Validation errors:",r.errors);let s="Validation failed";if(r.errors&&Array.isArray(r.errors)){let e=r.errors.map(e=>`${e.path}: ${e.message}`).join("; ");e&&(s+=`: ${e}`)}let a=Error(`API request to ${t} failed with status ${e.status}: ${s}`);throw a.validationErrors=r.errors,a.receivedData=r.receivedData,a.status=e.status,a}if(r?.errors&&Array.isArray(r.errors)){console.error("Validation errors:",r.errors);let s=Error(`API request to ${t} failed with status ${e.status}: ${r.message||"Validation failed"}`);throw s.validationErrors=r.errors,s}if(r?.details){let e=Error(r?.error?.message||r?.message||"Unknown server error");throw e.details=r.details,e}let s=r?.error?.message||r?.message||"Unknown server error";throw console.error(`API Error details: ${s}`),Error(`API request to ${t} failed with status ${e.status}: ${s}`)}if(204===e.status)return null;let r=await e.json();return console.debug(`API Response from ${t}:`,r),r}catch(s){if(s.message.startsWith("API request to"))throw console.error("API Error Propagated:",s.message),s;let e=`Network error when trying to fetch ${t}. The server might be down, unreachable, or there could be a CORS issue. Original error: ${s.message||"Unknown fetch error"}`;throw console.error("Fetch setup/network error details:",e,"Options:",r,"Original error object:",s),Error(e)}}let l=async()=>o("/vehicles"),d=async e=>o(`/vehicles/${e}`),c=async e=>o("/vehicles",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)}),u=async(e,r)=>o(`/vehicles/${e}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(r)}),m=async e=>o(`/vehicles/${e}`,{method:"DELETE"}),f=async(e,r)=>o(`/vehicles/${e}/servicerecords`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(r)}),h=async e=>o(`/vehicles/${e}/servicerecords`),p=async(e={})=>{try{let r=new URLSearchParams;e.vehicleId&&r.append("vehicleId",e.vehicleId),e.startDate&&r.append("startDate",e.startDate),e.endDate&&r.append("endDate",e.endDate),e.limit&&r.append("limit",e.limit.toString()),e.offset&&r.append("offset",e.offset.toString()),e.sortBy&&r.append("sortBy",e.sortBy),e.sortOrder&&r.append("sortOrder",e.sortOrder);let t=`/servicerecords${r.toString()?`?${r.toString()}`:""}`;console.info("Fetching service records",{url:t,params:e});let s=0;for(;s<=3;){let e=new AbortController,r=setTimeout(()=>e.abort(),1e4);try{let a=await fetch(`${i}${t}`,{signal:e.signal});if(clearTimeout(r),!a.ok&&400===a.status){let e=await a.json();throw console.warn("400 Bad Request error (not retrying)",{url:t,status:a.status,errorData:e}),Error(e.message||"Bad Request")}if(!a.ok&&a.status>=500&&s<3){console.warn(`Server error (${a.status}), retrying...`,{url:t,attempt:s+1,status:a.status}),await new Promise(e=>setTimeout(e,1e3*(s+1))),s++;continue}if(!a.ok){let e=await a.json().catch(()=>({}));throw Error(e.message||`HTTP error ${a.status}`)}let n=await a.json();if(!Array.isArray(n))throw console.error("Expected array but received:",n),Error("Invalid response format: expected an array of service records");return console.debug("Service records fetched successfully",{count:n.length}),n}catch(e){if(clearTimeout(r),e instanceof Error&&"AbortError"===e.name){if(console.warn("Request timed out after 10000ms, retrying...",{url:t,attempt:s+1}),s<3){s++;continue}throw Error("Request timed out after 3 attempts")}if(e instanceof Error&&e.message.includes("Bad Request"))throw e;if(s<3)console.warn(`Fetch attempt ${s+1} failed, retrying...`,{url:t,error:e instanceof Error?e.message:"Unknown error"}),await new Promise(e=>setTimeout(e,1e3*(s+1))),s++;else throw console.error("All 3 retry attempts failed",{url:t,error:e instanceof Error?e.message:"Unknown error"}),e}}throw Error("Failed to fetch service records after retries")}catch(r){return console.error("Service records fetch exception",{error:r instanceof Error?r.message:"Unknown error",params:e}),[]}},x=async(e,r)=>o(`/vehicles/${e}/servicerecords/${r}`,{method:"DELETE"}),g=async()=>o("/employees"),y=async e=>o(`/employees/${e}`),v=async e=>{let r={...e};return r.hireDate&&(r.hireDate=(0,s.formatDateForApi)(r.hireDate)),""===r.contactEmail&&(r.contactEmail=null),""===r.contactPhone&&(r.contactPhone=null),""===r.contactMobile&&(r.contactMobile=null),o("/employees",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(r)})},j=async(e,r)=>{let t={...r};return t.hireDate&&(t.hireDate=(0,s.formatDateForApi)(t.hireDate)),""===t.contactEmail&&(t.contactEmail=null),""===t.contactPhone&&(t.contactPhone=null),""===t.contactMobile&&(t.contactMobile=null),o(`/employees/${e}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(t)})},w=async e=>o(`/employees/${e}`,{method:"DELETE"}),b=async()=>o("/tasks"),N=async e=>o(`/tasks/${e}`),E=async e=>o("/tasks",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)}),T=async(e,r)=>o(`/tasks/${e}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(r)}),A=async e=>o(`/tasks/${e}`,{method:"DELETE"}),R=async()=>o("/delegations"),S=async e=>o(`/delegations/${e}`),_=async e=>o("/delegations",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)}),I=async(e,r)=>o(`/delegations/${e}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(r)}),k=async e=>o(`/delegations/${e}`,{method:"DELETE"}),O=async()=>{try{console.info("Starting to fetch enriched service records");let e=[],r=[];try{let t=await Promise.allSettled([l(),p()]);"fulfilled"===t[0].status?(e=Array.isArray(t[0].value)?t[0].value:[],console.debug(`Successfully fetched ${e.length} vehicles`)):console.error("Failed to fetch vehicles:",t[0].reason),"fulfilled"===t[1].status?(r=Array.isArray(t[1].value)?t[1].value:[],console.debug(`Successfully fetched ${r.length} service records`)):console.error("Failed to fetch service records:",t[1].reason)}catch(e){console.error("Error in parallel fetch operations:",e)}if(0===e.length&&0===r.length)throw Error("Failed to fetch both vehicles and service records");let t=new Map;e.forEach(e=>{e&&"number"==typeof e.id&&t.set(e.id,e)});let s=r.map(e=>{if(!e||void 0===e.vehicleId)return console.warn("Invalid service record found:",e),{id:e?.id||"unknown",vehicleId:"unknown",date:e?.date||new Date().toISOString(),odometer:e?.odometer||0,servicePerformed:e?.servicePerformed||["Unknown"],vehicleMake:"Unknown",vehicleModel:"Unknown",vehicleYear:0,vehiclePlateNumber:"Unknown"};let r=Number(e.vehicleId),s=t.get(r);return s?{...e,vehicleId:String(s.id),vehicleMake:s.make||"Unknown",vehicleModel:s.model||"Unknown",vehicleYear:s.year||0,vehiclePlateNumber:s.licensePlate||"Unknown"}:(console.warn(`Vehicle with ID ${e.vehicleId} not found for service record ${e.id}`),{...e,vehicleId:String(e.vehicleId),vehicleMake:"Unknown",vehicleModel:"Unknown",vehicleYear:0,vehiclePlateNumber:"Unknown"})}).sort((e,r)=>{try{let t=new Date(e.date).getTime(),s=new Date(r.date).getTime();if(isNaN(t)||isNaN(s))return 0;let a=s-t;if(0!==a)return a;let n="number"==typeof e.odometer?e.odometer:0;return("number"==typeof r.odometer?r.odometer:0)-n}catch(e){return console.error("Error sorting records:",e),0}});return console.info(`Returning ${s.length} enriched service records`),s}catch(e){return console.error("Error fetching enriched service records:",e),[]}}},50784:(e,r,t)=>{Promise.resolve().then(t.bind(t,67407))},50861:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,16444,23)),Promise.resolve().then(t.t.bind(t,16042,23)),Promise.resolve().then(t.t.bind(t,88170,23)),Promise.resolve().then(t.t.bind(t,49477,23)),Promise.resolve().then(t.t.bind(t,29345,23)),Promise.resolve().then(t.t.bind(t,12089,23)),Promise.resolve().then(t.t.bind(t,46577,23)),Promise.resolve().then(t.t.bind(t,31307,23))},63213:(e,r,t)=>{"use strict";t.d(r,{OJ:()=>o,Z2:()=>l});var s=t(60687),a=t(43210),n=t(10379);t(82046),t(49254);let i=(0,a.createContext)(void 0);function o({children:e}){let r=(0,n.A)();return(0,s.jsx)(i.Provider,{value:r,children:e})}function l(){let e=(0,a.useContext)(i);if(void 0===e)throw Error("useAuthContext must be used within an AuthProvider");return e}},65594:(e,r,t)=>{"use strict";t.r(r),t.d(r,{formatDateForApi:()=>d,formatDateForDisplay:()=>l,formatDateForInput:()=>o,isDateAfter:()=>f,isValidDateString:()=>m,isValidIsoDateString:()=>u,parseDateFromApi:()=>c});var s=t(58261),a=t(47141),n=t(76869),i=t(32637);let o=(e,r="datetime-local")=>{if(!e)return"";try{let t="string"==typeof e?(0,s.H)(e):e;if(!(0,a.f)(t))return console.warn("Invalid date for input formatting:",e),"";if("date"===r)return(0,n.GP)(t,"yyyy-MM-dd");return(0,n.GP)(t,"yyyy-MM-dd'T'HH:mm")}catch(e){return console.warn("Error formatting date for input:",e),""}},l=(e,r=!1)=>{if(!e)return"N/A";try{let t="string"==typeof e?(0,s.H)(e):e;if(!(0,a.f)(t))return"Invalid Date";return(0,n.GP)(t,r?"MMM d, yyyy, HH:mm":"MMM d, yyyy")}catch(e){return console.warn("Error formatting date for display:",e),"Invalid Date"}},d=e=>{if(!e)return"";try{if((0,i.$)(e))return e.toISOString();let r=new Date(e);if(!(0,a.f)(r))return console.warn("Invalid date for API formatting:",e),"";return r.toISOString()}catch(e){return console.warn("Error formatting date for API:",e),""}},c=e=>{if(!e)return null;try{let r=(0,s.H)(e);return(0,a.f)(r)?r:null}catch(e){return console.warn("Error parsing date from API:",e),null}},u=e=>{if(!e)return!1;try{let r=(0,s.H)(e);return(0,a.f)(r)}catch(e){return!1}},m=e=>{if(!e)return!1;try{let r=new Date(e);return(0,a.f)(r)}catch(e){return!1}},f=(e,r)=>{if(!e||!r)return!1;try{let t="string"==typeof e?(0,s.H)(e):e,n="string"==typeof r?(0,s.H)(r):r;if(!(0,a.f)(t)||!(0,a.f)(n))return!1;return t>n}catch(e){return console.warn("Error comparing dates:",e),!1}}},67407:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>V});var s=t(60687);t(44263);var a=t(85814),n=t.n(a),i=t(59059),o=t(24920),l=t(44610),d=t(33886),c=t(80489),u=t(28399),m=t(58369),f=t(22423),h=t(29523),p=t(16189),x=t(4780),g=t(43210),y=t(29848),v=t(10453),j=t(10218),w=t(21342);function b(){let{setTheme:e}=(0,j.D)();return(0,s.jsxs)(w.rI,{children:[(0,s.jsx)(w.ty,{asChild:!0,children:(0,s.jsxs)(h.$,{variant:"ghost",size:"icon",className:"text-foreground hover:bg-accent hover:text-accent-foreground",children:[(0,s.jsx)(y.A,{className:"h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0"}),(0,s.jsx)(v.A,{className:"absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100"}),(0,s.jsx)("span",{className:"sr-only",children:"Toggle theme"})]})}),(0,s.jsxs)(w.SQ,{align:"end",children:[(0,s.jsx)(w._2,{onClick:()=>e("light"),children:"Light"}),(0,s.jsx)(w._2,{onClick:()=>e("dark"),children:"Dark"}),(0,s.jsx)(w._2,{onClick:()=>e("system"),children:"System"})]})]})}function N(){let e=(0,p.usePathname)(),r=[{href:"/",label:"Dashboard",icon:i.A},{href:"/vehicles",label:"Assets",icon:o.A},{href:"/service-history",label:"Maintenance",icon:l.A},{href:"/delegations",label:"Projects",icon:d.A},{href:"/tasks",label:"Tasks",icon:c.A},{href:"/employees",label:"Team",icon:u.A},{href:"/admin",label:"Admin",icon:m.A}];return(0,s.jsx)("header",{className:"bg-card text-card-foreground shadow-md no-print border-b border-border",children:(0,s.jsx)("nav",{className:"container mx-auto px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"flex items-center justify-between h-16",children:[(0,s.jsx)("div",{className:"flex items-center",children:(0,s.jsxs)(n(),{href:"/",className:"flex items-center space-x-2 text-xl font-semibold hover:opacity-80 transition-opacity",children:[(0,s.jsx)(f.A,{className:"h-7 w-7 text-primary"}),(0,s.jsx)("span",{children:"WorkHub"})]})}),(0,s.jsxs)("div",{className:"flex items-center space-x-1 md:space-x-2",children:[r.map(r=>(0,s.jsx)(h.$,{variant:"ghost",asChild:!0,className:(0,x.cn)("hover:bg-accent hover:text-accent-foreground px-2 md:px-3",e===r.href||"/"!==r.href&&e.startsWith(r.href)?"bg-accent text-accent-foreground":"text-foreground"),children:(0,s.jsxs)(n(),{href:r.href,className:"flex items-center",children:[(0,s.jsx)(r.icon,{className:"mr-0 h-4 w-4 md:mr-2"}),(0,s.jsx)("span",{className:"hidden md:inline",children:r.label})]})},r.href)),(0,s.jsx)(b,{})]})]})})})}var E=t(29867),T=t(47313),A=t(24224),R=t(78726);let S=T.Kq,_=g.forwardRef(({className:e,...r},t)=>(0,s.jsx)(T.LM,{ref:t,className:(0,x.cn)("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",e),...r}));_.displayName=T.LM.displayName;let I=(0,A.F)("group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background text-foreground",destructive:"destructive group border-destructive bg-destructive text-destructive-foreground"}},defaultVariants:{variant:"default"}}),k=g.forwardRef(({className:e,variant:r,...t},a)=>(0,s.jsx)(T.bL,{ref:a,className:(0,x.cn)(I({variant:r}),e),...t}));k.displayName=T.bL.displayName,g.forwardRef(({className:e,...r},t)=>(0,s.jsx)(T.rc,{ref:t,className:(0,x.cn)("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",e),...r})).displayName=T.rc.displayName;let O=g.forwardRef(({className:e,...r},t)=>(0,s.jsx)(T.bm,{ref:t,className:(0,x.cn)("absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",e),"toast-close":"",...r,children:(0,s.jsx)(R.A,{className:"h-4 w-4"})}));O.displayName=T.bm.displayName;let P=g.forwardRef(({className:e,...r},t)=>(0,s.jsx)(T.hE,{ref:t,className:(0,x.cn)("text-sm font-semibold",e),...r}));P.displayName=T.hE.displayName;let D=g.forwardRef(({className:e,...r},t)=>(0,s.jsx)(T.VY,{ref:t,className:(0,x.cn)("text-sm opacity-90",e),...r}));function $(){let{toasts:e}=(0,E.dj)();return(0,s.jsxs)(S,{children:[e.map(function({id:e,title:r,description:t,action:a,...n}){return(0,s.jsxs)(k,{...n,children:[(0,s.jsxs)("div",{className:"grid gap-1",children:[r&&(0,s.jsx)(P,{children:r}),t&&(0,s.jsx)(D,{children:t})]}),a,(0,s.jsx)(O,{})]},e)}),(0,s.jsx)(_,{})]})}function C({children:e,...r}){return(0,s.jsx)(j.N,{...r,children:e})}D.displayName=T.VY.displayName;var U=t(72600);t(63245);var F=t(27965);function M({children:e}){let r=(0,p.usePathname)();return["/auth-test","/supabase-diagnostics"].some(e=>r.startsWith(e))?(0,s.jsx)(s.Fragment,{children:e}):(0,s.jsx)(F.OV,{requireEmailVerification:!0,children:e})}function V({children:e}){return(0,s.jsxs)("html",{lang:"en",className:"h-full",suppressHydrationWarning:!0,children:[(0,s.jsx)("head",{children:(0,s.jsx)(U.default,{src:"/fix-findindex-error.js",strategy:"beforeInteractive"})}),(0,s.jsx)("body",{className:"font-sans antialiased flex flex-col min-h-screen",children:(0,s.jsx)(C,{attribute:"class",defaultTheme:"system",enableSystem:!0,disableTransitionOnChange:!0,children:(0,s.jsx)(F.OJ,{children:(0,s.jsxs)(M,{children:[(0,s.jsx)(N,{}),(0,s.jsx)("main",{className:"flex-grow container mx-auto px-4 sm:px-6 lg:px-8 py-8",children:e}),(0,s.jsx)($,{}),(0,s.jsx)("footer",{className:"bg-card text-card-foreground py-4 text-center text-sm no-print border-t border-border",children:(0,s.jsxs)("p",{children:["\xa9 ",new Date().getFullYear()," WorkHub. All rights reserved."]})})]})})})})]})}},80013:(e,r,t)=>{"use strict";t.d(r,{J:()=>d});var s=t(60687),a=t(43210),n=t(78148),i=t(24224),o=t(4780);let l=(0,i.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)(n.b,{ref:t,className:(0,o.cn)(l(),e),...r}));d.displayName=n.b.displayName},82046:(e,r,t)=>{"use strict";t.d(r,{FH:()=>f,CZ:()=>m,O5:()=>i});var s=function(e){return e.NETWORK_ERROR="network_error",e.TIMEOUT="timeout",e.SERVER_ERROR="server_error",e.RATE_LIMIT="rate_limit",e.CLIENT_ERROR="client_error",e.VALIDATION_ERROR="validation_error",e.AUTHENTICATION_ERROR="authentication_error",e.AUTHORIZATION_ERROR="authorization_error",e.NOT_FOUND="not_found",e.PARSING_ERROR="parsing_error",e.UNKNOWN="unknown",e}({});class a extends Error{constructor(e,r){super(e),this.name="ApiError",this.status=r.status,this.endpoint=r.endpoint,this.validationErrors=r.validationErrors,this.receivedData=r.receivedData,this.details=r.details,this.errorType=r.errorType||this.determineErrorType(),this.retryable=this.isRetryable(),Object.setPrototypeOf(this,a.prototype)}static create(e,r,t){return new a(e,{status:r,validationErrors:t?.validationErrors,receivedData:t?.receivedData,details:t?.details,errorType:t?.errorType})}determineErrorType(){return this.status>=500?"server_error":429===this.status?"rate_limit":401===this.status?"authentication_error":403===this.status?"authorization_error":400===this.status&&this.isValidationError()?"validation_error":this.status>=400&&this.status<500?"client_error":"unknown"}isRetryable(){return"server_error"===this.errorType||"network_error"===this.errorType||"timeout"===this.errorType||"rate_limit"===this.errorType}isValidationError(){return 400===this.status&&Array.isArray(this.validationErrors)&&this.validationErrors.length>0}getFormattedMessage(){if(this.isValidationError()){let e=this.validationErrors.map(e=>`${e.path}: ${e.message}`).join("; ");return`Validation failed: ${e}`}switch(this.errorType){case"network_error":return"Network error: Unable to connect to the server. Please check your internet connection.";case"timeout":return"Request timed out. The server is taking too long to respond.";case"rate_limit":return"Too many requests. Please try again later.";case"authentication_error":return"Authentication required. Please log in and try again.";case"authorization_error":return"You do not have permission to perform this action.";case"not_found":return`Resource not found: ${this.endpoint||"The requested resource"} could not be found.`;case"parsing_error":return"Could not parse the server response. Please try again or contact support.";case"server_error":return`Server error (${this.status}): ${this.message}. Please try again later.`;default:return this.message}}getTechnicalDetails(){let e=[`Status: ${this.status}`,`Type: ${this.errorType}`,`Message: ${this.message}`];return this.details&&e.push(`Details: ${JSON.stringify(this.details)}`),this.validationErrors&&e.push(`Validation Errors: ${JSON.stringify(this.validationErrors)}`),e.join("\n")}}let n=null;function i(e){n=e}let o=e=>new Promise(r=>setTimeout(r,e)),l=(e,r)=>Math.min(r*Math.pow(2,e),3e4);async function d(e){let r,t;if(204===e.status)return null;if(e.ok)try{return await e.json()}catch(r){throw console.error("Failed to parse successful API response:",{status:e.status,statusText:e.statusText,url:e.url,error:r instanceof Error?r.message:"Unknown error"}),new a(`Failed to parse successful response: ${r instanceof Error?r.message:"Unknown error"}`,{status:e.status,endpoint:e.url,errorType:s.PARSING_ERROR})}let n=function(e){if(e>=500)return s.SERVER_ERROR;if(429===e)return s.RATE_LIMIT;if(401===e)return s.AUTHENTICATION_ERROR;if(403===e)return s.AUTHORIZATION_ERROR;if(404===e)return s.NOT_FOUND;else if(e>=400&&e<500)return s.CLIENT_ERROR;else return s.UNKNOWN}(e.status),i=`HTTP error ${e.status}`;try{if(r=await e.json(),i=r?.message||i,t=r?.details,400===e.status&&r?.status==="error"&&r?.message==="Validation failed"){n=s.VALIDATION_ERROR;let t=r.errors;throw console.error("API validation errors:",{endpoint:e.url,status:e.status,errors:t,receivedData:r.receivedData}),new a(r.message,{status:e.status,endpoint:e.url,errorType:n,validationErrors:t,receivedData:r.receivedData})}}catch(r){try{if(r instanceof a)throw r;{let r=await e.text();r&&(i=r)}}catch(r){if(r instanceof a)throw r;i=e.statusText||i}}throw console.error(`API error (${e.status}):`,{endpoint:e.url,status:e.status,message:i,details:t,errorType:n}),new a(i,{status:e.status,endpoint:e.url,details:t,errorType:n})}async function c(e,r={}){let t=function(){let e={"Content-Type":"application/json"};return n&&(e.Authorization=`Bearer ${n}`),{headers:e,retries:3,retryDelay:1e3,timeout:1e4}}(),i={...t,...r,headers:{...t.headers,...r.headers}},{retries:u=3,retryDelay:m=1e3,timeout:f=1e4,skipRetryLogging:h=!1}=i,p=null;for(let r=0;r<=u;r++)try{let r=new AbortController,t=setTimeout(()=>{r.abort()},f),s={...i,signal:r.signal},a=e.startsWith("http")?e:`http://localhost:3001/api${e}`,n=await fetch(a,s);return clearTimeout(t),await d(n)}catch(t){if(p=t,function(e){if(e instanceof a){if(e.status>=400&&e.status<500)return 429===e.status?s.RATE_LIMIT:s.CLIENT_ERROR;if(e.status>=500)return s.SERVER_ERROR}return e instanceof TypeError&&e.message.includes("network")?s.NETWORK_ERROR:e instanceof DOMException&&"AbortError"===e.name?s.TIMEOUT:s.UNKNOWN}(t)===s.CLIENT_ERROR||r===u)throw t;let e=l(r,m);h||console.warn(`API request failed (attempt ${r+1}/${u+1}), retrying in ${e/1e3}s:`,t),await o(e)}throw p||Error("Failed to fetch after retries")}async function u(e,r,t,s={}){let a={...s,method:e};return"GET"!==e&&t&&(a.body=JSON.stringify(t)),c(r,a)}function m(e,r){if(e&&"object"==typeof e){if("data"in e&&"status"in e)return e.data;Array.isArray(e)||"status"in e||"message"in e||!("errors"in e)}return e}let f={get:(e,r)=>u("GET",e,void 0,r),post:(e,r,t)=>u("POST",e,r,t),put:(e,r,t)=>u("PUT",e,r,t),patch:(e,r,t)=>u("PATCH",e,r,t),delete:(e,r)=>u("DELETE",e,void 0,r)}},89667:(e,r,t)=>{"use strict";t.d(r,{p:()=>i});var s=t(60687),a=t(43210),n=t(4780);let i=a.forwardRef(({className:e,type:r,...t},a)=>(0,s.jsx)("input",{type:r,className:(0,n.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:a,...t}));i.displayName="Input"},91821:(e,r,t)=>{"use strict";t.d(r,{Fc:()=>l,TN:()=>c,XL:()=>d});var s=t(60687),a=t(43210),n=t(24224),i=t(4780);let o=(0,n.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),l=a.forwardRef(({className:e,variant:r,...t},a)=>(0,s.jsx)("div",{ref:a,role:"alert",className:(0,i.cn)(o({variant:r}),e),...t}));l.displayName="Alert";let d=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("h5",{ref:t,className:(0,i.cn)("mb-1 font-medium leading-none tracking-tight",e),...r}));d.displayName="AlertTitle";let c=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,i.cn)("text-sm [&_p]:leading-relaxed",e),...r}));c.displayName="AlertDescription"},94431:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\projects\\\\CarServiceTrackingSystem\\\\TrackerSystem\\\\frontend\\\\src\\\\app\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\layout.tsx","default")},96834:(e,r,t)=>{"use strict";t.d(r,{E:()=>o});var s=t(60687);t(43210);var a=t(24224),n=t(4780);let i=(0,a.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function o({className:e,variant:r,...t}){return(0,s.jsx)("div",{className:(0,n.cn)(i({variant:r}),e),...t})}}};