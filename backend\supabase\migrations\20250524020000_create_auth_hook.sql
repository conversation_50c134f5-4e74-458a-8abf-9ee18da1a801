-- Create Custom Access Token Hook for RBAC
-- This function will be called by Supa<PERSON> Auth to inject custom claims into JWTs

CREATE OR REPLACE FUNCTION public.custom_access_token_hook(event jsonb)
RETURNS jsonb
LANGUAGE plpgsql
STABLE
AS $$
DECLAR<PERSON>
  claims jsonb;
  user_role text;
  user_active boolean;
BEGIN
  -- Get the user ID from the event
  SELECT (event->>'user_id')::uuid INTO claims;
  
  -- Fetch user role and active status from user_profiles table
  SELECT 
    up.role,
    up.is_active
  INTO 
    user_role,
    user_active
  FROM public.user_profiles up
  WHERE up.user_id = (event->>'user_id')::uuid;
  
  -- If no profile exists, assign default role
  IF user_role IS NULL THEN
    user_role := 'USER';
    user_active := true;
  END IF;
  
  -- Build the custom claims
  claims := jsonb_build_object(
    'app_metadata', jsonb_build_object(
      'custom_claims', jsonb_build_object(
        'user_role', user_role,
        'is_active', user_active
      )
    )
  );
  
  -- Return the claims to be merged into the JWT
  RETURN claims;
END;
$$;

-- Grant execute permission to the service role
GRANT EXECUTE ON FUNCTION public.custom_access_token_hook(jsonb) TO service_role;

-- Comment explaining the hook
COMMENT ON FUNCTION public.custom_access_token_hook(jsonb) IS 
'Custom Access Token Hook for Supabase Auth. Injects user role and active status from user_profiles table into JWT claims.'; 