"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9798],{39798:(e,t,a)=>{a.d(t,{k:()=>b});var s=a(95155),o=a(12115),r=a(6560),n=a(18018),d=a(50172),l=a(68718),i=a(15300),c=a(60679),p=a(44838),m=a(60408),f=a.n(m);let u=(e,t)=>{let a=URL.createObjectURL(e),s=document.createElement("a");s.href=a,s.download=t,document.body.appendChild(s),s.click(),document.body.removeChild(s),URL.revokeObjectURL(a)},h=async(e,t,a)=>{a.toLowerCase().endsWith(".csv")||(a+=".csv");let s=[t,...e];u(new Blob([f().unparse(s)],{type:"text/csv;charset=utf-8;"}),a)},x=e=>{let t=document.querySelector(e);if(!t)throw Error("Table element not found: ".concat(e));let a=t.querySelector("thead tr");if(!a)throw Error("Table header row not found");let s=[];a.querySelectorAll("th").forEach(e=>{if(!e.hasAttribute("data-skip-export")){var t;s.push((null==(t=e.textContent)?void 0:t.trim())||"")}});let o=[];return t.querySelectorAll("tbody tr").forEach(e=>{let t=[],s=0;e.querySelectorAll("td").forEach(e=>{let o=a.querySelectorAll("th")[s];if(s++,o&&!o.hasAttribute("data-skip-export")){var r;let a=e.getAttribute("data-export-value")||(null==(r=e.textContent)?void 0:r.trim())||"";t.push(a)}}),o.push(t)}),{headers:s,data:o}};var y=a(87481),w=a(59434);function b(e){let{reportContentId:t,reportType:a,entityId:m,tableId:f,fileName:u,enableCsv:b=!1,csvData:v,className:N}=e,[g,j]=(0,o.useState)(!1),[S,A]=(0,o.useState)(!1),{toast:_}=(0,y.dj)(),T=async()=>{j(!0);try{let e="/api/reports/".concat(a).concat(m?"/".concat(m):""),t=document.createElement("a");t.href=e,t.download="".concat(u,".pdf"),t.target="_blank",document.body.appendChild(t),t.click(),document.body.removeChild(t),_({title:"PDF Downloaded",description:"Your report is being downloaded as a PDF.",variant:"default"})}catch(e){console.error("Error generating PDF:",e),_({title:"Download Failed",description:"PDF download failed: ".concat(e.message||"Please try again."),variant:"destructive"})}finally{j(!1)}},E=async()=>{if(b){A(!0);try{if(v&&v.data&&v.headers)h(v.data,v.headers,"".concat(u,".csv"));else if(f){let e=x(f);h(e.data,e.headers,"".concat(u,".csv"))}else throw Error("CSV export requires either `csvData` or a `tableId` to be provided.");_({title:"CSV Downloaded",description:"Your report has been downloaded as a CSV file.",variant:"default"})}catch(e){console.error("Error generating CSV:",e),_({title:"Download Failed",description:"CSV generation failed: ".concat(e.message||"Please try again."),variant:"destructive"})}finally{A(!1)}}},D=g||S;return(0,s.jsxs)("div",{className:(0,w.cn)("flex items-center gap-2 no-print",N),children:[(0,s.jsx)(r.r,{actionType:"secondary",size:"icon",onClick:()=>{window.print()},"aria-label":"Print report",title:"Print Report",children:(0,s.jsx)(n.A,{className:"h-4 w-4"})}),(0,s.jsxs)(p.rI,{children:[(0,s.jsx)(p.ty,{asChild:!0,children:(0,s.jsx)(r.r,{actionType:"secondary",size:"icon","aria-label":"Download report",disabled:D,title:"Download Report",children:D?(0,s.jsx)(d.A,{className:"h-4 w-4 animate-spin"}):(0,s.jsx)(l.A,{className:"h-4 w-4"})})}),(0,s.jsxs)(p.SQ,{align:"end",children:[(0,s.jsxs)(p._2,{onClick:T,disabled:g,children:[g?(0,s.jsx)(d.A,{className:"mr-2 h-4 w-4 animate-spin"}):(0,s.jsx)(i.A,{className:"mr-2 h-4 w-4"}),(0,s.jsx)("span",{children:"Download PDF"})]}),b&&(0,s.jsxs)(p._2,{onClick:E,disabled:S,children:[S?(0,s.jsx)(d.A,{className:"mr-2 h-4 w-4 animate-spin"}):(0,s.jsx)(c.A,{className:"mr-2 h-4 w-4"}),(0,s.jsx)("span",{children:"Download CSV"})]})]})]})]})}},44838:(e,t,a)=>{a.d(t,{SQ:()=>m,_2:()=>f,lp:()=>u,mB:()=>h,rI:()=>c,ty:()=>p});var s=a(95155),o=a(12115),r=a(48698),n=a(73158),d=a(10518),l=a(70154),i=a(59434);let c=r.bL,p=r.l9;r.YJ,r.ZL,r.Pb,r.z6,o.forwardRef((e,t)=>{let{className:a,inset:o,children:d,...l}=e;return(0,s.jsxs)(r.ZP,{ref:t,className:(0,i.cn)("flex cursor-default gap-2 select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",o&&"pl-8",a),...l,children:[d,(0,s.jsx)(n.A,{className:"ml-auto"})]})}).displayName=r.ZP.displayName,o.forwardRef((e,t)=>{let{className:a,...o}=e;return(0,s.jsx)(r.G5,{ref:t,className:(0,i.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",a),...o})}).displayName=r.G5.displayName;let m=o.forwardRef((e,t)=>{let{className:a,sideOffset:o=4,...n}=e;return(0,s.jsx)(r.ZL,{children:(0,s.jsx)(r.UC,{ref:t,sideOffset:o,className:(0,i.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",a),...n})})});m.displayName=r.UC.displayName;let f=o.forwardRef((e,t)=>{let{className:a,inset:o,...n}=e;return(0,s.jsx)(r.q7,{ref:t,className:(0,i.cn)("relative flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",o&&"pl-8",a),...n})});f.displayName=r.q7.displayName,o.forwardRef((e,t)=>{let{className:a,children:o,checked:n,...l}=e;return(0,s.jsxs)(r.H_,{ref:t,className:(0,i.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",a),checked:n,...l,children:[(0,s.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,s.jsx)(r.VF,{children:(0,s.jsx)(d.A,{className:"h-4 w-4"})})}),o]})}).displayName=r.H_.displayName,o.forwardRef((e,t)=>{let{className:a,children:o,...n}=e;return(0,s.jsxs)(r.hN,{ref:t,className:(0,i.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",a),...n,children:[(0,s.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,s.jsx)(r.VF,{children:(0,s.jsx)(l.A,{className:"h-2 w-2 fill-current"})})}),o]})}).displayName=r.hN.displayName;let u=o.forwardRef((e,t)=>{let{className:a,inset:o,...n}=e;return(0,s.jsx)(r.JU,{ref:t,className:(0,i.cn)("px-2 py-1.5 text-sm font-semibold",o&&"pl-8",a),...n})});u.displayName=r.JU.displayName;let h=o.forwardRef((e,t)=>{let{className:a,...o}=e;return(0,s.jsx)(r.wv,{ref:t,className:(0,i.cn)("-mx-1 my-1 h-px bg-muted",a),...o})});h.displayName=r.wv.displayName},87481:(e,t,a)=>{a.d(t,{dj:()=>m});var s=a(12115);let o=0,r=new Map,n=e=>{if(r.has(e))return;let t=setTimeout(()=>{r.delete(e),c({type:"REMOVE_TOAST",toastId:e})},1e6);r.set(e,t)},d=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:a}=t;return a?n(a):e.toasts.forEach(e=>{n(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===a||void 0===a?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},l=[],i={toasts:[]};function c(e){i=d(i,e),l.forEach(e=>{e(i)})}function p(e){let{...t}=e,a=(o=(o+1)%Number.MAX_SAFE_INTEGER).toString(),s=()=>c({type:"DISMISS_TOAST",toastId:a});return c({type:"ADD_TOAST",toast:{...t,id:a,open:!0,onOpenChange:e=>{e||s()}}}),{id:a,dismiss:s,update:e=>c({type:"UPDATE_TOAST",toast:{...e,id:a}})}}function m(){let[e,t]=s.useState(i);return s.useEffect(()=>(l.push(t),()=>{let e=l.indexOf(t);e>-1&&l.splice(e,1)}),[e]),{...e,toast:p,dismiss:e=>c({type:"DISMISS_TOAST",toastId:e})}}}}]);