(()=>{var e={};e.id=8880,e.ids=[8880],e.modules={3018:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>Z});var t=a(60687),r=a(43210),l=a(16189),i=a(85814),n=a.n(i),d=a(30474),c=a(68752),o=a(44493),m=a(33886),x=a(55817),u=a(35137),p=a(57207),h=a(26398),g=a(92876),f=a(48206),j=a(99196),N=a(52856),v=a(93242),y=a(15036),b=a(48409),w=a(28840),A=a(48041),k=a(29867),D=a(96834),C=a(4780),T=a(76869),E=a(58261),P=a(15795),q=a(93500),R=a(52027),M=a(69981);let L=e=>{switch(e){case"Planned":return"bg-blue-500/20 text-blue-700 border-blue-500/30 dark:text-blue-400 dark:bg-blue-500/10 dark:border-blue-500/20";case"Confirmed":return"bg-green-500/20 text-green-700 border-green-500/30 dark:text-green-400 dark:bg-green-500/10 dark:border-green-500/20";case"In_Progress":return"bg-yellow-500/20 text-yellow-700 border-yellow-500/30 dark:text-yellow-400 dark:bg-yellow-500/10 dark:border-yellow-500/20";case"Completed":return"bg-purple-500/20 text-purple-700 border-purple-500/30 dark:text-purple-400 dark:bg-purple-500/10 dark:border-purple-500/20";case"Cancelled":return"bg-red-500/20 text-red-700 border-red-500/30 dark:text-red-400 dark:bg-red-500/10 dark:border-red-500/20";default:return"bg-gray-500/20 text-gray-700 border-gray-500/30 dark:text-gray-400 dark:bg-gray-500/10 dark:border-gray-500/20"}},_=(e,s=!1)=>{try{return(0,T.GP)((0,E.H)(e),s?"MMM d, yyyy, HH:mm":"MMM d, yyyy")}catch(e){return"Invalid Date"}};function S({icon:e,label:s,value:a,children:r,valueClassName:l}){return a||r?(0,t.jsxs)("div",{className:"flex items-start py-2",children:[(0,t.jsx)(e,{className:"h-5 w-5 text-accent mr-3 mt-0.5 shrink-0"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:s}),a&&(0,t.jsx)("p",{className:(0,C.cn)("text-base font-semibold text-foreground",l),children:a}),r]})]}):null}function Z(){let e=(0,l.useParams)(),s=(0,l.useRouter)(),{toast:a}=(0,k.dj)(),[i,T]=(0,r.useState)(null),[E,Z]=(0,r.useState)(!0),[z,F]=(0,r.useState)(null),H=e.id,B=(0,r.useCallback)(async()=>{Z(!0),F(null);try{if(H){let e=await (0,w.getDelegationById)(H);e?(e.statusHistory.sort((e,s)=>new Date(s.changedAt).getTime()-new Date(e.changedAt).getTime()),T(e)):(F("Delegation not found."),a({title:"Error",description:"Delegation not found.",variant:"destructive"}))}}catch(s){console.error("Error fetching delegation:",s);let e=s instanceof Error?s.message:"Failed to load delegation details.";F(e),a({title:"Error",description:e,variant:"destructive"})}finally{Z(!1)}},[H,a]),I=async()=>{if(i)try{await (0,w.deleteDelegation)(i.id),a({title:"Delegation Deleted",description:`Delegation "${i.eventName}" has been deleted.`}),s.push("/delegations")}catch(e){console.error("Error deleting delegation:",e),a({title:"Error",description:"Failed to delete delegation. Please try again.",variant:"destructive"})}};return(0,t.jsx)("div",{className:"space-y-8",children:(0,t.jsx)(R.gO,{isLoading:E,error:z,data:i,onRetry:B,loadingComponent:(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)(A.z,{title:"Loading Delegation...",icon:m.A}),(0,t.jsx)(R.jt,{variant:"card",count:1}),(0,t.jsxs)("div",{className:"grid md:grid-cols-3 gap-6 items-start",children:[(0,t.jsx)(R.jt,{variant:"card",count:1,className:"md:col-span-2"}),(0,t.jsx)(R.jt,{variant:"card",count:1})]})]}),emptyComponent:(0,t.jsxs)("div",{className:"text-center py-10",children:[(0,t.jsx)(A.z,{title:"Delegation Not Found",icon:m.A}),(0,t.jsx)("p",{className:"mb-4",children:"The requested delegation could not be found."}),(0,t.jsx)(c.r,{actionType:"primary",onClick:()=>s.push("/delegations"),icon:(0,t.jsx)(x.A,{className:"h-4 w-4"}),children:"Back to List"})]}),children:e=>(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(A.z,{title:e.eventName,icon:m.A,children:(0,t.jsxs)("div",{className:"flex gap-2 items-center flex-wrap",children:[(0,t.jsx)(c.r,{actionType:"tertiary",onClick:()=>s.push("/delegations"),icon:(0,t.jsx)(x.A,{className:"h-4 w-4"}),children:"Back to List"}),(0,t.jsx)(c.r,{actionType:"secondary",asChild:!0,icon:(0,t.jsx)(u.A,{className:"h-4 w-4"}),children:(0,t.jsx)(n(),{href:`/delegations/${e.id}/edit`,children:"Edit"})}),(0,t.jsx)(M.M,{href:`/delegations/${e.id}/report`}),(0,t.jsxs)(q.Lt,{children:[(0,t.jsx)(q.tv,{asChild:!0,children:(0,t.jsx)(c.r,{actionType:"danger",icon:(0,t.jsx)(p.A,{className:"h-4 w-4"}),children:"Delete Delegation"})}),(0,t.jsxs)(q.EO,{children:[(0,t.jsxs)(q.wd,{children:[(0,t.jsx)(q.r7,{children:"Are you sure?"}),(0,t.jsx)(q.$v,{children:"This action cannot be undone. This will permanently delete the delegation and all its related information."})]}),(0,t.jsxs)(q.ck,{children:[(0,t.jsx)(q.Zr,{children:"Cancel"}),(0,t.jsx)(q.Rx,{onClick:I,className:"bg-destructive hover:bg-destructive/90",children:"Delete"})]})]})]})]})})," ",(0,t.jsxs)(o.Zp,{className:"shadow-lg bg-card p-0",children:[(0,t.jsx)(o.aR,{className:"border-b p-6",children:(0,t.jsxs)("div",{className:"flex justify-between items-start",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(o.ZB,{className:"text-2xl font-bold text-primary mb-1",children:e.eventName}),(0,t.jsx)(o.BT,{className:"text-muted-foreground",children:"Detailed overview of the delegation."})]}),(0,t.jsx)(D.E,{className:(0,C.cn)("text-sm py-1 px-3 font-semibold",L(e.status)),children:(0,P.fZ)(e.status)})]})}),(0,t.jsxs)(o.Wu,{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-x-6 gap-y-4",children:[(0,t.jsxs)("div",{className:"lg:col-span-1 space-y-1",children:[(0,t.jsx)(S,{icon:h.A,label:"Location",value:e.location}),(0,t.jsx)(S,{icon:g.A,label:"Duration",value:`${_(e.durationFrom)} - ${_(e.durationTo)}`}),e.invitationFrom&&(0,t.jsx)(S,{icon:f.A,label:"Invitation From",value:e.invitationFrom}),e.invitationTo&&(0,t.jsx)(S,{icon:f.A,label:"Invitation To",value:e.invitationTo}),e.notes&&(0,t.jsx)(S,{icon:j.A,label:"General Notes",value:e.notes,valueClassName:"whitespace-pre-wrap"})]}),(0,t.jsx)("div",{className:"relative aspect-[16/10] w-full md:col-span-2 lg:col-span-2 rounded-lg overflow-hidden mb-4 lg:mb-0 shadow-sm",children:(0,t.jsx)(d.default,{src:e.imageUrl||`https://picsum.photos/seed/${e.id}/600/375`,alt:e.eventName,layout:"fill",objectFit:"cover",className:"bg-muted","data-ai-hint":"event venue",priority:!0})})]})]}),(0,t.jsxs)("div",{className:"grid lg:grid-cols-3 gap-6 items-start",children:[(0,t.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[" ",(0,t.jsxs)(o.Zp,{className:"shadow-md bg-card p-0",children:[(0,t.jsx)(o.aR,{className:"p-6",children:(0,t.jsxs)(o.ZB,{className:"text-xl font-semibold text-primary flex items-center",children:[(0,t.jsx)(f.A,{className:"mr-2 h-5 w-5 text-accent"})," Delegates (",e.delegates.length,")"]})}),(0,t.jsx)(o.Wu,{children:e.delegates.length>0?(0,t.jsx)("div",{className:"space-y-3",children:e.delegates.map(e=>(0,t.jsxs)("div",{className:"p-3 border rounded-md bg-background",children:[(0,t.jsx)("p",{className:"font-semibold text-foreground",children:e.name}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:e.title}),e.notes&&(0,t.jsx)("p",{className:"text-xs text-muted-foreground mt-1 italic",children:e.notes})]},e.id))}):(0,t.jsx)("p",{className:"text-muted-foreground",children:"No delegates assigned to this delegation."})})]}),(e.flightArrivalDetails||e.flightDepartureDetails)&&(0,t.jsxs)(o.Zp,{className:"shadow-md bg-card p-0",children:[(0,t.jsx)(o.aR,{className:"p-6",children:(0,t.jsxs)(o.ZB,{className:"text-xl font-semibold text-primary flex items-center",children:[(0,t.jsx)(N.A,{className:"mr-2 h-5 w-5 text-accent"})," Flight Information"]})}),(0,t.jsxs)(o.Wu,{className:"grid md:grid-cols-2 gap-6",children:[e.flightArrivalDetails&&(0,t.jsxs)("div",{className:"space-y-2 p-3 border rounded-md bg-background",children:[(0,t.jsx)("h4",{className:"font-semibold text-foreground",children:"Arrival Details"}),(0,t.jsx)(S,{icon:v.A,label:"Flight No.",value:e.flightArrivalDetails.flightNumber}),(0,t.jsx)(S,{icon:y.A,label:"Date & Time",value:_(e.flightArrivalDetails.dateTime,!0)}),(0,t.jsx)(S,{icon:h.A,label:"Airport",value:e.flightArrivalDetails.airport}),e.flightArrivalDetails.terminal&&(0,t.jsx)(S,{icon:h.A,label:"Terminal",value:e.flightArrivalDetails.terminal}),e.flightArrivalDetails.notes&&(0,t.jsx)(S,{icon:j.A,label:"Notes",value:e.flightArrivalDetails.notes,valueClassName:"whitespace-pre-wrap"})]}),e.flightDepartureDetails&&(0,t.jsxs)("div",{className:"space-y-2 p-3 border rounded-md bg-background",children:[(0,t.jsx)("h4",{className:"font-semibold text-foreground",children:"Departure Details"}),(0,t.jsx)(S,{icon:b.A,label:"Flight No.",value:e.flightDepartureDetails.flightNumber}),(0,t.jsx)(S,{icon:y.A,label:"Date & Time",value:_(e.flightDepartureDetails.dateTime,!0)}),(0,t.jsx)(S,{icon:h.A,label:"Airport",value:e.flightDepartureDetails.airport}),e.flightDepartureDetails.terminal&&(0,t.jsx)(S,{icon:h.A,label:"Terminal",value:e.flightDepartureDetails.terminal}),e.flightDepartureDetails.notes&&(0,t.jsx)(S,{icon:j.A,label:"Notes",value:e.flightDepartureDetails.notes,valueClassName:"whitespace-pre-wrap"})]}),!e.flightArrivalDetails&&!e.flightDepartureDetails&&(0,t.jsx)("p",{className:"text-muted-foreground md:col-span-2",children:"No flight details logged."})]})]})]})," ",(0,t.jsxs)("div",{className:"lg:col-span-1",children:[" ",(0,t.jsxs)(o.Zp,{className:"shadow-md bg-card p-0",children:[(0,t.jsx)(o.aR,{className:"p-6",children:(0,t.jsxs)(o.ZB,{className:"text-xl font-semibold text-primary flex items-center",children:[(0,t.jsx)(y.A,{className:"mr-2 h-5 w-5 text-accent"})," Status History"]})}),(0,t.jsx)(o.Wu,{children:e.statusHistory.length>0?(0,t.jsx)("div",{className:"space-y-3 max-h-96 overflow-y-auto",children:e.statusHistory.map(e=>(0,t.jsxs)("div",{className:"p-3 border rounded-md bg-background text-sm",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)(D.E,{className:(0,C.cn)("text-xs py-0.5 px-1.5",L(e.status)),children:(0,P.fZ)(e.status)}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:_(e.changedAt,!0)})]}),e.reason&&(0,t.jsxs)("p",{className:"text-xs text-muted-foreground mt-1 italic",children:["Reason: ",e.reason]})]},e.id))}):(0,t.jsx)("p",{className:"text-muted-foreground",children:"No status history available."})})]})]})]})]})})})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},13289:(e,s,a)=>{"use strict";a.r(s),a.d(s,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>o,routeModule:()=>x,tree:()=>c});var t=a(65239),r=a(48088),l=a(88170),i=a.n(l),n=a(30893),d={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);a.d(s,d);let c={children:["",{children:["delegations",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,89976)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\delegations\\[id]\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,94431)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\delegations\\[id]\\page.tsx"],m={require:a,loadChunk:()=>Promise.resolve()},x=new t.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/delegations/[id]/page",pathname:"/delegations/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},15795:(e,s,a)=>{"use strict";function t(e){switch(e){case"In_Progress":return"In Progress";case"No_details":return"No Details";default:return e}}a.d(s,{fZ:()=>t})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},36644:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(82614).A)("FileText",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},39133:(e,s,a)=>{Promise.resolve().then(a.bind(a,3018))},43869:(e,s,a)=>{Promise.resolve().then(a.bind(a,89976))},48041:(e,s,a)=>{"use strict";a.d(s,{z:()=>r});var t=a(60687);function r({title:e,description:s,icon:a,children:r}){return(0,t.jsxs)("div",{className:"mb-6 pb-4 border-b border-border/50 flex justify-between items-center",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[a&&(0,t.jsx)(a,{className:"h-8 w-8 text-primary"}),(0,t.jsx)("h1",{className:"text-3xl font-bold tracking-tight text-foreground",children:e})]}),s&&(0,t.jsx)("p",{className:"text-muted-foreground mt-1",children:s})]}),r&&(0,t.jsx)("div",{className:"flex items-center gap-2",children:r})]})}a(43210)},48409:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(82614).A)("PlaneTakeoff",[["path",{d:"M2 22h20",key:"272qi7"}],["path",{d:"M6.36 17.4 4 17l-2-4 1.1-.55a2 2 0 0 1 1.8 0l.17.1a2 2 0 0 0 1.8 0L8 12 5 6l.9-.45a2 2 0 0 1 2.09.2l4.02 3a2 2 0 0 0 2.1.2l4.19-2.06a2.41 2.41 0 0 1 1.73-.17L21 7a1.4 1.4 0 0 1 .87 1.99l-.38.76c-.23.46-.6.84-1.07 1.08L7.58 17.2a2 2 0 0 1-1.22.18Z",key:"fkigj9"}]])},52027:(e,s,a)=>{"use strict";a.d(s,{gO:()=>h,jt:()=>u});var t=a(60687);a(43210);var r=a(11516),l=a(72963),i=a(4780),n=a(85726),d=a(91821),c=a(68752);let o={sm:"h-4 w-4",md:"h-6 w-6",lg:"h-8 w-8",xl:"h-12 w-12"},m={sm:"text-xs",md:"text-sm",lg:"text-base",xl:"text-lg"};function x({size:e="md",className:s,text:a,fullPage:l=!1}){return(0,t.jsx)("div",{className:(0,i.cn)("flex items-center justify-center",l&&"fixed inset-0 bg-background/80 backdrop-blur-sm z-50",s),children:(0,t.jsxs)("div",{className:"flex flex-col items-center",children:[(0,t.jsx)(r.A,{className:(0,i.cn)("animate-spin text-primary",o[e])}),a&&(0,t.jsx)("span",{className:(0,i.cn)("mt-2 text-muted-foreground",m[e]),children:a})]})})}function u({variant:e="default",count:s=1,className:a,testId:r="loading-skeleton"}){return"card"===e?(0,t.jsx)("div",{className:(0,i.cn)("grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",a),"data-testid":r,children:Array(s).fill(0).map((e,s)=>(0,t.jsxs)("div",{className:"overflow-hidden shadow-md rounded-lg border bg-card",children:[(0,t.jsx)(n.E,{className:"aspect-[16/10] w-full"}),(0,t.jsxs)("div",{className:"p-5",children:[(0,t.jsx)(n.E,{className:"h-7 w-3/4 mb-1"}),(0,t.jsx)(n.E,{className:"h-4 w-1/2 mb-3"}),(0,t.jsx)(n.E,{className:"h-px w-full my-3"}),(0,t.jsx)("div",{className:"space-y-2.5",children:[,,,].fill(0).map((e,s)=>(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(n.E,{className:"mr-2.5 h-5 w-5 rounded-full"}),(0,t.jsx)(n.E,{className:"h-5 w-2/3"})]},s))})]})]},s))}):"table"===e?(0,t.jsxs)("div",{className:(0,i.cn)("space-y-3",a),"data-testid":r,children:[(0,t.jsx)("div",{className:"flex gap-4",children:[,,,].fill(0).map((e,s)=>(0,t.jsx)(n.E,{className:"h-8 flex-1"},s))}),Array(s).fill(0).map((e,s)=>(0,t.jsx)("div",{className:"flex gap-4",children:[,,,].fill(0).map((e,s)=>(0,t.jsx)(n.E,{className:"h-6 flex-1"},s))},s))]}):"list"===e?(0,t.jsx)("div",{className:(0,i.cn)("space-y-3",a),"data-testid":r,children:Array(s).fill(0).map((e,s)=>(0,t.jsxs)("div",{className:"flex items-center gap-4",children:[(0,t.jsx)(n.E,{className:"h-12 w-12 rounded-full"}),(0,t.jsxs)("div",{className:"space-y-2 flex-1",children:[(0,t.jsx)(n.E,{className:"h-4 w-1/3"}),(0,t.jsx)(n.E,{className:"h-4 w-full"})]})]},s))}):"stats"===e?(0,t.jsx)("div",{className:(0,i.cn)("grid gap-4 md:grid-cols-2 lg:grid-cols-3",a),"data-testid":r,children:Array(s).fill(0).map((e,s)=>(0,t.jsxs)("div",{className:"rounded-lg border bg-card p-5 shadow-sm",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)(n.E,{className:"h-5 w-1/3"}),(0,t.jsx)(n.E,{className:"h-5 w-5 rounded-full"})]}),(0,t.jsx)(n.E,{className:"h-8 w-1/2 mt-3"}),(0,t.jsx)(n.E,{className:"h-4 w-2/3 mt-2"})]},s))}):(0,t.jsx)("div",{className:(0,i.cn)("space-y-2",a),"data-testid":r,children:Array(s).fill(0).map((e,s)=>(0,t.jsx)(n.E,{className:"w-full h-5"},s))})}function p({message:e,onRetry:s,className:a}){return(0,t.jsxs)(d.Fc,{variant:"destructive",className:(0,i.cn)("my-4",a),children:[(0,t.jsx)(l.A,{className:"h-4 w-4"}),(0,t.jsx)(d.XL,{children:"Error"}),(0,t.jsx)(d.TN,{children:(0,t.jsxs)("div",{className:"mt-2",children:[(0,t.jsx)("p",{className:"text-sm text-muted-foreground mb-4",children:e}),s&&(0,t.jsx)(c.r,{actionType:"tertiary",size:"sm",onClick:s,icon:(0,t.jsx)(r.A,{className:"h-4 w-4"}),children:"Try Again"})]})})]})}function h({isLoading:e,error:s,data:a,onRetry:r,children:l,loadingComponent:n,errorComponent:d,emptyComponent:c,className:o}){return e?n||(0,t.jsx)(x,{className:o,text:"Loading..."}):s?d||(0,t.jsx)(p,{message:s,onRetry:r,className:o}):!a||Array.isArray(a)&&0===a.length?c||(0,t.jsx)("div",{className:(0,i.cn)("text-center py-8",o),children:(0,t.jsx)("p",{className:"text-muted-foreground",children:"No data available"})}):(0,t.jsx)("div",{className:o,children:l(a)})}},52856:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(82614).A)("Plane",[["path",{d:"M17.8 19.2 16 11l3.5-3.5C21 6 21.5 4 21 3c-1-.5-3 0-4.5 1.5L13 8 4.8 6.2c-.5-.1-.9.1-1.1.5l-.3.5c-.2.5-.1 1 .3 1.3L9 12l-2 3H4l-1 1 3 2 2 3 1-1v-3l3-2 3.5 5.3c.3.4.8.5 1.3.3l.5-.2c.4-.3.6-.7.5-1.2z",key:"1v9wt8"}]])},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68752:(e,s,a)=>{"use strict";a.d(s,{r:()=>c});var t=a(60687),r=a(43210),l=a.n(r),i=a(29523),n=a(11516),d=a(4780);let c=l().forwardRef(({actionType:e="primary",icon:s,isLoading:a=!1,loadingText:r,className:l,children:c,disabled:o,asChild:m=!1,...x},u)=>{let{variant:p,className:h}={primary:{variant:"default",className:"shadow-md"},secondary:{variant:"secondary",className:""},tertiary:{variant:"outline",className:""},danger:{variant:"destructive",className:"shadow-md"}}[e];return(0,t.jsx)(i.$,{ref:u,variant:p,className:(0,d.cn)(h,l),disabled:a||o,asChild:m,...x,children:a?(0,t.jsxs)("span",{className:"inline-flex items-center",children:[" ",(0,t.jsx)(n.A,{className:"mr-2 h-4 w-4 animate-spin"}),r||c]}):(0,t.jsxs)("span",{className:"inline-flex items-center",children:[" ",s&&(0,t.jsx)("span",{className:"mr-2",children:s}),c]})})});c.displayName="ActionButton"},69981:(e,s,a)=>{"use strict";a.d(s,{M:()=>c});var t=a(60687);a(43210);var r=a(85814),l=a.n(r),i=a(36644),n=a(60368),d=a(68752);function c({href:e,getReportUrl:s,isList:a=!1,className:r}){if(!e&&!s)return console.error("ViewReportButton requires either href or getReportUrl prop"),null;let c=a?"View List Report":"View Report";return e?(0,t.jsx)(d.r,{actionType:"secondary",asChild:!0,icon:(0,t.jsx)(i.A,{className:"h-4 w-4"}),className:r,children:(0,t.jsxs)(l(),{href:e,target:"_blank",rel:"noopener noreferrer",children:[c,(0,t.jsx)(n.A,{className:"h-3 w-3 ml-1.5 inline-block","aria-hidden":"true"}),(0,t.jsx)("span",{className:"sr-only",children:"(opens in new tab)"})]})}):(0,t.jsxs)(d.r,{actionType:"secondary",onClick:()=>{if(s){let e=s();window.open(e,"_blank","noopener,noreferrer")}},icon:(0,t.jsx)(i.A,{className:"h-4 w-4"}),className:r,children:[c,(0,t.jsx)(n.A,{className:"h-3 w-3 ml-1.5 inline-block","aria-hidden":"true"})]})}},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},85726:(e,s,a)=>{"use strict";a.d(s,{E:()=>l});var t=a(60687),r=a(4780);function l({className:e,...s}){return(0,t.jsx)("div",{className:(0,r.cn)("animate-pulse rounded-md bg-muted",e),...s})}},89976:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>t});let t=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\projects\\\\CarServiceTrackingSystem\\\\TrackerSystem\\\\frontend\\\\src\\\\app\\\\delegations\\\\[id]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\delegations\\[id]\\page.tsx","default")},91645:e=>{"use strict";e.exports=require("net")},93242:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(82614).A)("PlaneLanding",[["path",{d:"M2 22h20",key:"272qi7"}],["path",{d:"M3.77 10.77 2 9l2-4.5 1.1.55c.55.28.9.84.9 1.45s.35 1.17.9 1.45L8 8.5l3-6 1.05.53a2 2 0 0 1 1.09 1.52l.72 5.4a2 2 0 0 0 1.09 1.52l4.4 2.2c.42.22.78.55 1.01.96l.6 1.03c.49.88-.06 1.98-1.06 2.1l-1.18.15c-.47.06-.95-.02-1.37-.24L4.29 11.15a2 2 0 0 1-.52-.38Z",key:"1ma21e"}]])},93500:(e,s,a)=>{"use strict";a.d(s,{$v:()=>g,EO:()=>x,Lt:()=>d,Rx:()=>f,Zr:()=>j,ck:()=>p,r7:()=>h,tv:()=>c,wd:()=>u});var t=a(60687),r=a(43210),l=a(97895),i=a(4780),n=a(29523);let d=l.bL,c=l.l9,o=l.ZL,m=r.forwardRef(({className:e,...s},a)=>(0,t.jsx)(l.hJ,{className:(0,i.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...s,ref:a}));m.displayName=l.hJ.displayName;let x=r.forwardRef(({className:e,...s},a)=>(0,t.jsxs)(o,{children:[(0,t.jsx)(m,{}),(0,t.jsx)(l.UC,{ref:a,className:(0,i.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),...s})]}));x.displayName=l.UC.displayName;let u=({className:e,...s})=>(0,t.jsx)("div",{className:(0,i.cn)("flex flex-col space-y-2 text-center sm:text-left",e),...s});u.displayName="AlertDialogHeader";let p=({className:e,...s})=>(0,t.jsx)("div",{className:(0,i.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",e),...s});p.displayName="AlertDialogFooter";let h=r.forwardRef(({className:e,...s},a)=>(0,t.jsx)(l.hE,{ref:a,className:(0,i.cn)("text-lg font-semibold",e),...s}));h.displayName=l.hE.displayName;let g=r.forwardRef(({className:e,...s},a)=>(0,t.jsx)(l.VY,{ref:a,className:(0,i.cn)("text-sm text-muted-foreground",e),...s}));g.displayName=l.VY.displayName;let f=r.forwardRef(({className:e,...s},a)=>(0,t.jsx)(l.rc,{ref:a,className:(0,i.cn)((0,n.r)(),e),...s}));f.displayName=l.rc.displayName;let j=r.forwardRef(({className:e,...s},a)=>(0,t.jsx)(l.ZD,{ref:a,className:(0,i.cn)((0,n.r)({variant:"outline"}),"mt-2 sm:mt-0",e),...s}));j.displayName=l.ZD.displayName},94735:e=>{"use strict";e.exports=require("events")},99196:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(82614).A)("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]])}};var s=require("../../../webpack-runtime.js");s.C(e);var a=e=>s(s.s=e),t=s.X(0,[4447,3622,1658,5880,474,424,8141,3983],()=>a(13289));module.exports=t})();