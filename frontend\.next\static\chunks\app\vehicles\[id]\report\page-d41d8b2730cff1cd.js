(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3015],{3638:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(40157).A)("History",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}],["path",{d:"M12 7v5l4 2",key:"1fdv2h"}]])},6560:(e,s,a)=>{"use strict";a.d(s,{r:()=>d});var t=a(95155),r=a(12115),l=a(30285),c=a(50172),i=a(59434);let d=r.forwardRef((e,s)=>{let{actionType:a="primary",icon:r,isLoading:d=!1,loadingText:n,className:o,children:m,disabled:x,asChild:h=!1,...u}=e,{variant:j,className:p}={primary:{variant:"default",className:"shadow-md"},secondary:{variant:"secondary",className:""},tertiary:{variant:"outline",className:""},danger:{variant:"destructive",className:"shadow-md"}}[a];return(0,t.jsx)(l.$,{ref:s,variant:j,className:(0,i.cn)(p,o),disabled:d||x,asChild:h,...u,children:d?(0,t.jsxs)("span",{className:"inline-flex items-center",children:[" ",(0,t.jsx)(c.A,{className:"mr-2 h-4 w-4 animate-spin"}),n||m]}):(0,t.jsxs)("span",{className:"inline-flex items-center",children:[" ",r&&(0,t.jsx)("span",{className:"mr-2",children:r}),m]})})});d.displayName="ActionButton"},10829:(e,s,a)=>{Promise.resolve().then(a.bind(a,97261))},35695:(e,s,a)=>{"use strict";var t=a(18999);a.o(t,"useParams")&&a.d(s,{useParams:function(){return t.useParams}}),a.o(t,"usePathname")&&a.d(s,{usePathname:function(){return t.usePathname}}),a.o(t,"useRouter")&&a.d(s,{useRouter:function(){return t.useRouter}}),a.o(t,"useSearchParams")&&a.d(s,{useSearchParams:function(){return t.useSearchParams}})},55365:(e,s,a)=>{"use strict";a.d(s,{Fc:()=>d,TN:()=>o,XL:()=>n});var t=a(95155),r=a(12115),l=a(74466),c=a(59434);let i=(0,l.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),d=r.forwardRef((e,s)=>{let{className:a,variant:r,...l}=e;return(0,t.jsx)("div",{ref:s,role:"alert",className:(0,c.cn)(i({variant:r}),a),...l})});d.displayName="Alert";let n=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)("h5",{ref:s,className:(0,c.cn)("mb-1 font-medium leading-none tracking-tight",a),...r})});n.displayName="AlertTitle";let o=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)("div",{ref:s,className:(0,c.cn)("text-sm [&_p]:leading-relaxed",a),...r})});o.displayName="AlertDescription"},68856:(e,s,a)=>{"use strict";a.d(s,{E:()=>l});var t=a(95155),r=a(59434);function l(e){let{className:s,...a}=e;return(0,t.jsx)("div",{className:(0,r.cn)("animate-pulse rounded-md bg-muted",s),...a})}},77023:(e,s,a)=>{"use strict";a.d(s,{gO:()=>j,jt:()=>h});var t=a(95155);a(12115);var r=a(50172),l=a(11133),c=a(59434),i=a(68856),d=a(55365),n=a(6560);let o={sm:"h-4 w-4",md:"h-6 w-6",lg:"h-8 w-8",xl:"h-12 w-12"},m={sm:"text-xs",md:"text-sm",lg:"text-base",xl:"text-lg"};function x(e){let{size:s="md",className:a,text:l,fullPage:i=!1}=e;return(0,t.jsx)("div",{className:(0,c.cn)("flex items-center justify-center",i&&"fixed inset-0 bg-background/80 backdrop-blur-sm z-50",a),children:(0,t.jsxs)("div",{className:"flex flex-col items-center",children:[(0,t.jsx)(r.A,{className:(0,c.cn)("animate-spin text-primary",o[s])}),l&&(0,t.jsx)("span",{className:(0,c.cn)("mt-2 text-muted-foreground",m[s]),children:l})]})})}function h(e){let{variant:s="default",count:a=1,className:r,testId:l="loading-skeleton"}=e;return"card"===s?(0,t.jsx)("div",{className:(0,c.cn)("grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",r),"data-testid":l,children:Array(a).fill(0).map((e,s)=>(0,t.jsxs)("div",{className:"overflow-hidden shadow-md rounded-lg border bg-card",children:[(0,t.jsx)(i.E,{className:"aspect-[16/10] w-full"}),(0,t.jsxs)("div",{className:"p-5",children:[(0,t.jsx)(i.E,{className:"h-7 w-3/4 mb-1"}),(0,t.jsx)(i.E,{className:"h-4 w-1/2 mb-3"}),(0,t.jsx)(i.E,{className:"h-px w-full my-3"}),(0,t.jsx)("div",{className:"space-y-2.5",children:[,,,].fill(0).map((e,s)=>(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(i.E,{className:"mr-2.5 h-5 w-5 rounded-full"}),(0,t.jsx)(i.E,{className:"h-5 w-2/3"})]},s))})]})]},s))}):"table"===s?(0,t.jsxs)("div",{className:(0,c.cn)("space-y-3",r),"data-testid":l,children:[(0,t.jsx)("div",{className:"flex gap-4",children:[,,,].fill(0).map((e,s)=>(0,t.jsx)(i.E,{className:"h-8 flex-1"},s))}),Array(a).fill(0).map((e,s)=>(0,t.jsx)("div",{className:"flex gap-4",children:[,,,].fill(0).map((e,s)=>(0,t.jsx)(i.E,{className:"h-6 flex-1"},s))},s))]}):"list"===s?(0,t.jsx)("div",{className:(0,c.cn)("space-y-3",r),"data-testid":l,children:Array(a).fill(0).map((e,s)=>(0,t.jsxs)("div",{className:"flex items-center gap-4",children:[(0,t.jsx)(i.E,{className:"h-12 w-12 rounded-full"}),(0,t.jsxs)("div",{className:"space-y-2 flex-1",children:[(0,t.jsx)(i.E,{className:"h-4 w-1/3"}),(0,t.jsx)(i.E,{className:"h-4 w-full"})]})]},s))}):"stats"===s?(0,t.jsx)("div",{className:(0,c.cn)("grid gap-4 md:grid-cols-2 lg:grid-cols-3",r),"data-testid":l,children:Array(a).fill(0).map((e,s)=>(0,t.jsxs)("div",{className:"rounded-lg border bg-card p-5 shadow-sm",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)(i.E,{className:"h-5 w-1/3"}),(0,t.jsx)(i.E,{className:"h-5 w-5 rounded-full"})]}),(0,t.jsx)(i.E,{className:"h-8 w-1/2 mt-3"}),(0,t.jsx)(i.E,{className:"h-4 w-2/3 mt-2"})]},s))}):(0,t.jsx)("div",{className:(0,c.cn)("space-y-2",r),"data-testid":l,children:Array(a).fill(0).map((e,s)=>(0,t.jsx)(i.E,{className:"w-full h-5"},s))})}function u(e){let{message:s,onRetry:a,className:i}=e;return(0,t.jsxs)(d.Fc,{variant:"destructive",className:(0,c.cn)("my-4",i),children:[(0,t.jsx)(l.A,{className:"h-4 w-4"}),(0,t.jsx)(d.XL,{children:"Error"}),(0,t.jsx)(d.TN,{children:(0,t.jsxs)("div",{className:"mt-2",children:[(0,t.jsx)("p",{className:"text-sm text-muted-foreground mb-4",children:s}),a&&(0,t.jsx)(n.r,{actionType:"tertiary",size:"sm",onClick:a,icon:(0,t.jsx)(r.A,{className:"h-4 w-4"}),children:"Try Again"})]})})]})}function j(e){let{isLoading:s,error:a,data:r,onRetry:l,children:i,loadingComponent:d,errorComponent:n,emptyComponent:o,className:m}=e;return s?d||(0,t.jsx)(x,{className:m,text:"Loading..."}):a?n||(0,t.jsx)(u,{message:a,onRetry:l,className:m}):!r||Array.isArray(r)&&0===r.length?o||(0,t.jsx)("div",{className:(0,c.cn)("text-center py-8",m),children:(0,t.jsx)("p",{className:"text-muted-foreground",children:"No data available"})}):(0,t.jsx)("div",{className:m,children:i(r)})}},97261:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>u});var t=a(95155),r=a(12115),l=a(35695),c=a(2730),i=a(66766),d=a(39798),n=a(3638),o=a(77023),m=a(6874),x=a.n(m),h=a(6560);function u(){let e=(0,l.useParams)(),[s,a]=(0,r.useState)(null),[m,u]=(0,r.useState)(!0),[j,p]=(0,r.useState)(null),N=e.id,v=async()=>{if(u(!0),p(null),N)try{let e=await (0,c.getVehicleById)(Number(N));e?(e.serviceHistory.sort((e,s)=>new Date(e.date).getTime()-new Date(s.date).getTime()||e.odometer-s.odometer),a(e),document.title="".concat(e.make," ").concat(e.model," - Maintenance Report")):p("Vehicle not found.")}catch(e){console.error("Error fetching vehicle for report:",e),p(e instanceof Error?e.message:"Failed to load vehicle data.")}finally{u(!1)}else p("No Vehicle ID provided."),u(!1)};return(0,r.useEffect)(()=>{v()},[N]),(0,t.jsx)("div",{className:"max-w-4xl mx-auto bg-white p-2 sm:p-4 text-gray-800",children:(0,t.jsx)(o.gO,{isLoading:m,error:j,data:s,onRetry:v,loadingComponent:(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-gray-800 text-center",children:"Loading Report..."}),(0,t.jsx)(o.jt,{variant:"card",count:1}),(0,t.jsx)(o.jt,{variant:"table",count:3,className:"mt-6"})]}),emptyComponent:(0,t.jsx)("div",{className:"text-center py-10",children:"Vehicle not found or could not be loaded."}),children:e=>(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("div",{className:"flex justify-between items-center mb-4 no-print",children:[(0,t.jsx)(h.r,{actionType:"tertiary",asChild:!0,icon:(0,t.jsx)(n.A,{className:"h-4 w-4"}),children:(0,t.jsx)(x(),{href:"/vehicles/".concat(N,"/report/service-history"),children:"Detailed Service History"})}),(0,t.jsx)(d.k,{reportContentId:"#vehicle-report-content",reportType:"vehicle",entityId:N,tableId:"#service-history-table",fileName:"vehicle-report-".concat(e.make,"-").concat(e.model),enableCsv:e.serviceHistory.length>0})]}),(0,t.jsxs)("div",{id:"vehicle-report-content",className:"report-content",children:[(0,t.jsxs)("header",{className:"text-center mb-8 pb-4 border-b-2 border-gray-300 report-header",children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-gray-800",children:"Maintenance Report"}),(0,t.jsxs)("p",{className:"text-xl text-gray-600",children:[e.make," ",e.model," (",e.year,")"]}),e.licensePlate&&(0,t.jsxs)("p",{className:"text-md text-gray-500",children:["Plate: ",e.licensePlate]})]}),(0,t.jsxs)("section",{className:"mb-8 card-print p-4 border border-gray-200 rounded",children:[(0,t.jsx)("h2",{className:"text-2xl font-semibold text-gray-700 mb-4 pb-2 border-b border-gray-200",children:"Vehicle Details"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Make:"})," ",e.make]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Model:"})," ",e.model]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Year:"})," ",e.year]}),e.licensePlate&&(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Plate Number:"})," ",e.licensePlate]}),e.color&&(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Color:"})," ",e.color]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Initial Odometer:"})," ",null!==e.initialOdometer?"".concat(e.initialOdometer.toLocaleString()," miles"):"Not recorded"]})]}),e.imageUrl&&(0,t.jsx)("div",{className:"mt-4 relative aspect-[16/9] w-full max-w-md mx-auto overflow-hidden rounded no-print",children:(0,t.jsx)(i.default,{src:e.imageUrl,alt:"".concat(e.make," ").concat(e.model),layout:"fill",objectFit:"contain","data-ai-hint":"car side"})})]}),(0,t.jsxs)("section",{className:"card-print p-4 border border-gray-200 rounded",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center mb-4 pb-2 border-b border-gray-200",children:[(0,t.jsx)("h2",{className:"text-2xl font-semibold text-gray-700",children:"Service History"}),(0,t.jsx)("p",{className:"text-sm text-gray-500 no-print",children:(0,t.jsx)(x(),{href:"/vehicles/".concat(N,"/report/service-history"),className:"text-blue-600 hover:underline",children:"View detailed service history"})})]}),0===e.serviceHistory.length?(0,t.jsx)("p",{className:"text-gray-500",children:"No service records available for this vehicle."}):(0,t.jsxs)("table",{id:"service-history-table",className:"w-full text-sm text-left text-gray-600",children:[(0,t.jsx)("thead",{className:"text-xs text-gray-700 uppercase bg-gray-50",children:(0,t.jsxs)("tr",{children:[(0,t.jsx)("th",{scope:"col",className:"px-3 py-2",children:"Date"}),(0,t.jsx)("th",{scope:"col",className:"px-3 py-2",children:"Odometer"}),(0,t.jsx)("th",{scope:"col",className:"px-3 py-2",children:"Service Performed"}),(0,t.jsx)("th",{scope:"col",className:"px-3 py-2",children:"Notes"}),(0,t.jsx)("th",{scope:"col",className:"px-3 py-2 text-right",children:"Cost"})]})}),(0,t.jsx)("tbody",{children:e.serviceHistory.map(e=>(0,t.jsxs)("tr",{className:"bg-white border-b hover:bg-gray-50",children:[(0,t.jsx)("td",{className:"px-3 py-2",children:new Date(e.date).toLocaleDateString()}),(0,t.jsx)("td",{className:"px-3 py-2",children:e.odometer.toLocaleString()}),(0,t.jsx)("td",{className:"px-3 py-2",children:e.servicePerformed.join(", ")}),(0,t.jsx)("td",{className:"px-3 py-2",children:e.notes||"-"}),(0,t.jsx)("td",{className:"px-3 py-2 text-right",children:e.cost?"$".concat(Number(e.cost).toFixed(2)):"-"})]},e.id))})]})]}),(0,t.jsxs)("footer",{className:"mt-12 pt-4 border-t-2 border-gray-300 text-center text-xs text-gray-500 report-footer",children:[(0,t.jsxs)("p",{children:["Report generated on: ",new Date().toLocaleDateString()]}),(0,t.jsx)("p",{children:"WorkHub"})]})]})]})})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[3796,6113,832,2688,2512,7529,6766,8782,6045,8162,2730,9798,8441,1684,7358],()=>s(10829)),_N_E=e.O()}]);