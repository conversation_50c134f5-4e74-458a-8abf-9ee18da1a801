(()=>{var e={};e.id=5055,e.ids=[5055],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},15036:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(82614).A)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},15795:(e,t,r)=>{"use strict";function s(e){switch(e){case"In_Progress":return"In Progress";case"No_details":return"No Details";default:return e}}r.d(t,{fZ:()=>s})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20731:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>y});var s=r(60687),a=r(43210),i=r(16189);r(28840);var l=r(30474),n=r(52856),o=r(15036),d=r(26398),c=r(18578),x=r(76869),p=r(58261),m=r(96834),h=r(4780),g=r(15795),u=r(52027);let b=(e,t=!1)=>{if(!e)return"N/A";try{return(0,x.GP)((0,p.H)(e),t?"MMM d, yyyy, HH:mm":"MMM d, yyyy")}catch(e){return"Invalid Date"}},j=e=>{switch(e){case"Planned":return"bg-blue-100 text-blue-800 border-blue-300";case"Confirmed":return"bg-green-100 text-green-800 border-green-300";case"In_Progress":return"bg-yellow-100 text-yellow-800 border-yellow-300";case"Completed":return"bg-purple-100 text-purple-800 border-purple-300";case"Cancelled":return"bg-red-100 text-red-800 border-red-300";default:return"bg-gray-100 text-gray-800 border-gray-300"}};function y(){let e=(0,i.useParams)(),[t,r]=(0,a.useState)(null),[x,p]=(0,a.useState)(!0);return(e.id,x)?(0,s.jsxs)("div",{className:"max-w-4xl mx-auto p-4",children:[(0,s.jsx)(u.jt,{variant:"card",count:1}),(0,s.jsx)(u.jt,{variant:"table",count:3,className:"mt-6"}),(0,s.jsx)(u.jt,{variant:"table",count:2,className:"mt-6"})]}):t?(0,s.jsxs)("div",{className:"max-w-4xl mx-auto bg-white p-2 sm:p-4 text-gray-800",children:[(0,s.jsx)("div",{className:"text-right mb-4 no-print",children:(0,s.jsx)(c.k,{reportContentId:"#delegation-report-content",tableId:"#delegates-table",fileName:`delegation-report-${t.eventName.replace(/\s+/g,"-")}`,enableCsv:t.delegates.length>0||t.statusHistory&&t.statusHistory.length>0})}),(0,s.jsxs)("div",{id:"delegation-report-content",className:"report-content",children:[(0,s.jsxs)("header",{className:"text-center mb-8 pb-4 border-b-2 border-gray-300",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-gray-800",children:"Delegation Report"}),(0,s.jsx)("p",{className:"text-xl text-gray-600",children:t.eventName}),(0,s.jsxs)(m.E,{className:(0,h.cn)("mt-2 text-sm py-1 px-3 font-semibold",j(t.status)),children:["Status: ",(0,g.fZ)(t.status)]})]}),(0,s.jsxs)("section",{className:"mb-6 card-print p-4 border border-gray-200 rounded",children:[(0,s.jsx)("h2",{className:"text-xl font-semibold text-gray-700 mb-3 pb-2 border-b border-gray-200",children:"Delegation Summary"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-x-4 gap-y-2 text-sm",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Event Name:"})," ",t.eventName]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Location:"})," ",t.location]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Duration:"})," ",b(t.durationFrom)," ","to ",b(t.durationTo)]}),t.invitationFrom&&(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Invitation From:"})," ",t.invitationFrom]}),t.invitationTo&&(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Invitation To:"})," ",t.invitationTo]})]}),t.imageUrl&&(0,s.jsx)("div",{className:"mt-4 relative aspect-[16/9] w-full max-w-md mx-auto overflow-hidden rounded no-print",children:(0,s.jsx)(l.default,{src:t.imageUrl,alt:t.eventName,layout:"fill",objectFit:"contain","data-ai-hint":"event placeholder"})}),t.notes&&(0,s.jsxs)("div",{className:"mt-3 text-sm",children:[(0,s.jsx)("strong",{children:"Notes:"})," ",(0,s.jsx)("span",{className:"italic",children:t.notes})]})]}),(0,s.jsxs)("section",{className:"mb-6 card-print p-4 border border-gray-200 rounded",children:[(0,s.jsxs)("h2",{className:"text-xl font-semibold text-gray-700 mb-3 pb-2 border-b border-gray-200",children:["Delegates (",t.delegates.length,")"]}),t.delegates.length>0?(0,s.jsxs)("table",{id:"delegates-table",className:"w-full text-sm text-left text-gray-600",children:[(0,s.jsx)("thead",{className:"text-xs text-gray-700 uppercase bg-gray-50",children:(0,s.jsxs)("tr",{children:[(0,s.jsx)("th",{scope:"col",className:"px-3 py-2",children:"Name"}),(0,s.jsx)("th",{scope:"col",className:"px-3 py-2",children:"Title"}),(0,s.jsx)("th",{scope:"col",className:"px-3 py-2",children:"Notes"})]})}),(0,s.jsx)("tbody",{children:t.delegates.map(e=>(0,s.jsxs)("tr",{className:"bg-white border-b hover:bg-gray-50",children:[(0,s.jsx)("td",{className:"px-3 py-2 font-medium",children:e.name}),(0,s.jsx)("td",{className:"px-3 py-2",children:e.title}),(0,s.jsx)("td",{className:"px-3 py-2",children:e.notes||"-"})]},e.id))})]}):(0,s.jsx)("p",{className:"text-gray-500 text-sm",children:"No delegates listed."})]}),(t.flightArrivalDetails||t.flightDepartureDetails)&&(0,s.jsxs)("section",{className:"mb-6 card-print p-4 border border-gray-200 rounded",children:[(0,s.jsx)("h2",{className:"text-xl font-semibold text-gray-700 mb-3 pb-2 border-b border-gray-200",children:"Flight Information"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 text-sm",children:[t.flightArrivalDetails&&(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"font-semibold text-md mb-1",children:"Arrival Details"}),(0,s.jsxs)("p",{children:[(0,s.jsx)(n.A,{className:"inline h-4 w-4 mr-1"}),(0,s.jsx)("strong",{children:"Flight:"})," ",t.flightArrivalDetails.flightNumber]}),(0,s.jsxs)("p",{children:[(0,s.jsx)(o.A,{className:"inline h-4 w-4 mr-1"}),(0,s.jsx)("strong",{children:"Time:"})," ",b(t.flightArrivalDetails.dateTime,!0)]}),(0,s.jsxs)("p",{children:[(0,s.jsx)(d.A,{className:"inline h-4 w-4 mr-1"}),(0,s.jsx)("strong",{children:"Airport:"})," ",t.flightArrivalDetails.airport," ",t.flightArrivalDetails.terminal&&`(Terminal ${t.flightArrivalDetails.terminal})`]}),t.flightArrivalDetails.notes&&(0,s.jsxs)("p",{className:"mt-1 text-xs italic",children:[(0,s.jsx)("strong",{children:"Notes:"})," ",t.flightArrivalDetails.notes]})]}),t.flightDepartureDetails&&(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"font-semibold text-md mb-1",children:"Departure Details"}),(0,s.jsxs)("p",{children:[(0,s.jsx)(n.A,{className:"inline h-4 w-4 mr-1"}),(0,s.jsx)("strong",{children:"Flight:"})," ",t.flightDepartureDetails.flightNumber]}),(0,s.jsxs)("p",{children:[(0,s.jsx)(o.A,{className:"inline h-4 w-4 mr-1"}),(0,s.jsx)("strong",{children:"Time:"})," ",b(t.flightDepartureDetails.dateTime,!0)]}),(0,s.jsxs)("p",{children:[(0,s.jsx)(d.A,{className:"inline h-4 w-4 mr-1"}),(0,s.jsx)("strong",{children:"Airport:"})," ",t.flightDepartureDetails.airport," ",t.flightDepartureDetails.terminal&&`(Terminal ${t.flightDepartureDetails.terminal})`]}),t.flightDepartureDetails.notes&&(0,s.jsxs)("p",{className:"mt-1 text-xs italic",children:[(0,s.jsx)("strong",{children:"Notes:"})," ",t.flightDepartureDetails.notes]})]})]}),!t.flightArrivalDetails&&!t.flightDepartureDetails&&(0,s.jsx)("p",{className:"text-gray-500 text-sm",children:"No flight details logged."})]}),(0,s.jsxs)("section",{className:"card-print p-4 border border-gray-200 rounded",children:[(0,s.jsx)("h2",{className:"text-xl font-semibold text-gray-700 mb-3 pb-2 border-b border-gray-200",children:"Status History"}),t.statusHistory&&t.statusHistory.length>0?(0,s.jsxs)("table",{id:"status-history-table",className:"w-full text-sm text-left text-gray-600",children:[(0,s.jsx)("thead",{className:"text-xs text-gray-700 uppercase bg-gray-50",children:(0,s.jsxs)("tr",{children:[(0,s.jsx)("th",{scope:"col",className:"px-3 py-2",children:"Status"}),(0,s.jsx)("th",{scope:"col",className:"px-3 py-2",children:"Changed At"}),(0,s.jsx)("th",{scope:"col",className:"px-3 py-2",children:"Reason"})]})}),(0,s.jsx)("tbody",{children:t.statusHistory.slice().sort((e,t)=>new Date(t.changedAt).getTime()-new Date(e.changedAt).getTime()).map(e=>(0,s.jsxs)("tr",{className:"bg-white border-b hover:bg-gray-50",children:[(0,s.jsx)("td",{className:"px-3 py-2",children:(0,s.jsx)(m.E,{className:(0,h.cn)("text-xs py-0.5 px-1.5",j(e.status)),children:(0,g.fZ)(e.status)})}),(0,s.jsx)("td",{className:"px-3 py-2",children:b(e.changedAt,!0)}),(0,s.jsx)("td",{className:"px-3 py-2",children:e.reason||"-"})]},e.id))})]}):(0,s.jsx)("p",{className:"text-gray-500 text-sm",children:"No status history available."})]}),(0,s.jsxs)("footer",{className:"mt-10 pt-4 border-t-2 border-gray-300 text-center text-xs text-gray-500",children:[(0,s.jsxs)("p",{children:["Report generated on: ",new Date().toLocaleDateString()]}),(0,s.jsx)("p",{children:"WorkHub - Delegation Management"})]})]})]}):(0,s.jsx)("div",{className:"text-center py-10",children:"Delegation not found."})}},26398:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(82614).A)("MapPin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31342:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i,metadata:()=>a});var s=r(37413);let a={title:"Delegation Report"};function i({children:e}){return(0,s.jsx)(s.Fragment,{children:e})}},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},39681:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>l.a,__next_app__:()=>x,pages:()=>c,routeModule:()=>p,tree:()=>d});var s=r(65239),a=r(48088),i=r(88170),l=r.n(i),n=r(30893),o={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);r.d(t,o);let d={children:["",{children:["delegations",{children:["[id]",{children:["report",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,52213)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\delegations\\[id]\\report\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,31342)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\delegations\\[id]\\report\\layout.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\delegations\\[id]\\report\\page.tsx"],x={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/delegations/[id]/report/page",pathname:"/delegations/[id]/report",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},50013:(e,t,r)=>{Promise.resolve().then(r.bind(r,20731))},52213:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\projects\\\\CarServiceTrackingSystem\\\\TrackerSystem\\\\frontend\\\\src\\\\app\\\\delegations\\\\[id]\\\\report\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\delegations\\[id]\\report\\page.tsx","default")},52856:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(82614).A)("Plane",[["path",{d:"M17.8 19.2 16 11l3.5-3.5C21 6 21.5 4 21 3c-1-.5-3 0-4.5 1.5L13 8 4.8 6.2c-.5-.1-.9.1-1.1.5l-.3.5c-.2.5-.1 1 .3 1.3L9 12l-2 3H4l-1 1 3 2 2 3 1-1v-3l3-2 3.5 5.3c.3.4.8.5 1.3.3l.5-.2c.4-.3.6-.7.5-1.2z",key:"1v9wt8"}]])},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63517:(e,t,r)=>{Promise.resolve().then(r.bind(r,52213))},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,3622,1658,5880,474,6055,8141,3983,4318],()=>r(39681));module.exports=s})();