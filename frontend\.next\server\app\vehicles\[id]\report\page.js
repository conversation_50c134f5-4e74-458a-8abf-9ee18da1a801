(()=>{var e={};e.id=3015,e.ids=[3015],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33011:(e,r,t)=>{Promise.resolve().then(t.bind(t,86529))},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},74939:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\projects\\\\CarServiceTrackingSystem\\\\TrackerSystem\\\\frontend\\\\src\\\\app\\\\vehicles\\\\[id]\\\\report\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\vehicles\\[id]\\report\\page.tsx","default")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79867:(e,r,t)=>{Promise.resolve().then(t.bind(t,74939))},81630:e=>{"use strict";e.exports=require("http")},82196:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a,metadata:()=>i});var s=t(37413);let i={title:"Vehicle Report"};function a({children:e}){return(0,s.jsx)(s.Fragment,{children:e})}},86529:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>m});var s=t(60687),i=t(43210),a=t(16189),o=t(28840),n=t(30474),l=t(18578),c=t(44610),d=t(52027),p=t(85814),x=t.n(p),h=t(68752);function m(){let e=(0,a.useParams)(),[r,t]=(0,i.useState)(null),[p,m]=(0,i.useState)(!0),[u,v]=(0,i.useState)(null),y=e.id,g=async()=>{if(m(!0),v(null),y)try{let e=await (0,o.getVehicleById)(Number(y));e?(e.serviceHistory.sort((e,r)=>new Date(e.date).getTime()-new Date(r.date).getTime()||e.odometer-r.odometer),t(e),document.title=`${e.make} ${e.model} - Maintenance Report`):v("Vehicle not found.")}catch(e){console.error("Error fetching vehicle for report:",e),v(e instanceof Error?e.message:"Failed to load vehicle data.")}finally{m(!1)}else v("No Vehicle ID provided."),m(!1)};return(0,s.jsx)("div",{className:"max-w-4xl mx-auto bg-white p-2 sm:p-4 text-gray-800",children:(0,s.jsx)(d.gO,{isLoading:p,error:u,data:r,onRetry:g,loadingComponent:(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-gray-800 text-center",children:"Loading Report..."}),(0,s.jsx)(d.jt,{variant:"card",count:1}),(0,s.jsx)(d.jt,{variant:"table",count:3,className:"mt-6"})]}),emptyComponent:(0,s.jsx)("div",{className:"text-center py-10",children:"Vehicle not found or could not be loaded."}),children:e=>(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-4 no-print",children:[(0,s.jsx)(h.r,{actionType:"tertiary",asChild:!0,icon:(0,s.jsx)(c.A,{className:"h-4 w-4"}),children:(0,s.jsx)(x(),{href:`/vehicles/${y}/report/service-history`,children:"Detailed Service History"})}),(0,s.jsx)(l.k,{reportContentId:"#vehicle-report-content",reportType:"vehicle",entityId:y,tableId:"#service-history-table",fileName:`vehicle-report-${e.make}-${e.model}`,enableCsv:e.serviceHistory.length>0})]}),(0,s.jsxs)("div",{id:"vehicle-report-content",className:"report-content",children:[(0,s.jsxs)("header",{className:"text-center mb-8 pb-4 border-b-2 border-gray-300 report-header",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-gray-800",children:"Maintenance Report"}),(0,s.jsxs)("p",{className:"text-xl text-gray-600",children:[e.make," ",e.model," (",e.year,")"]}),e.licensePlate&&(0,s.jsxs)("p",{className:"text-md text-gray-500",children:["Plate: ",e.licensePlate]})]}),(0,s.jsxs)("section",{className:"mb-8 card-print p-4 border border-gray-200 rounded",children:[(0,s.jsx)("h2",{className:"text-2xl font-semibold text-gray-700 mb-4 pb-2 border-b border-gray-200",children:"Vehicle Details"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Make:"})," ",e.make]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Model:"})," ",e.model]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Year:"})," ",e.year]}),e.licensePlate&&(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Plate Number:"})," ",e.licensePlate]}),e.color&&(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Color:"})," ",e.color]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Initial Odometer:"})," ",null!==e.initialOdometer?`${e.initialOdometer.toLocaleString()} miles`:"Not recorded"]})]}),e.imageUrl&&(0,s.jsx)("div",{className:"mt-4 relative aspect-[16/9] w-full max-w-md mx-auto overflow-hidden rounded no-print",children:(0,s.jsx)(n.default,{src:e.imageUrl,alt:`${e.make} ${e.model}`,layout:"fill",objectFit:"contain","data-ai-hint":"car side"})})]}),(0,s.jsxs)("section",{className:"card-print p-4 border border-gray-200 rounded",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-4 pb-2 border-b border-gray-200",children:[(0,s.jsx)("h2",{className:"text-2xl font-semibold text-gray-700",children:"Service History"}),(0,s.jsx)("p",{className:"text-sm text-gray-500 no-print",children:(0,s.jsx)(x(),{href:`/vehicles/${y}/report/service-history`,className:"text-blue-600 hover:underline",children:"View detailed service history"})})]}),0===e.serviceHistory.length?(0,s.jsx)("p",{className:"text-gray-500",children:"No service records available for this vehicle."}):(0,s.jsxs)("table",{id:"service-history-table",className:"w-full text-sm text-left text-gray-600",children:[(0,s.jsx)("thead",{className:"text-xs text-gray-700 uppercase bg-gray-50",children:(0,s.jsxs)("tr",{children:[(0,s.jsx)("th",{scope:"col",className:"px-3 py-2",children:"Date"}),(0,s.jsx)("th",{scope:"col",className:"px-3 py-2",children:"Odometer"}),(0,s.jsx)("th",{scope:"col",className:"px-3 py-2",children:"Service Performed"}),(0,s.jsx)("th",{scope:"col",className:"px-3 py-2",children:"Notes"}),(0,s.jsx)("th",{scope:"col",className:"px-3 py-2 text-right",children:"Cost"})]})}),(0,s.jsx)("tbody",{children:e.serviceHistory.map(e=>(0,s.jsxs)("tr",{className:"bg-white border-b hover:bg-gray-50",children:[(0,s.jsx)("td",{className:"px-3 py-2",children:new Date(e.date).toLocaleDateString()}),(0,s.jsx)("td",{className:"px-3 py-2",children:e.odometer.toLocaleString()}),(0,s.jsx)("td",{className:"px-3 py-2",children:e.servicePerformed.join(", ")}),(0,s.jsx)("td",{className:"px-3 py-2",children:e.notes||"-"}),(0,s.jsx)("td",{className:"px-3 py-2 text-right",children:e.cost?`$${Number(e.cost).toFixed(2)}`:"-"})]},e.id))})]})]}),(0,s.jsxs)("footer",{className:"mt-12 pt-4 border-t-2 border-gray-300 text-center text-xs text-gray-500 report-footer",children:[(0,s.jsxs)("p",{children:["Report generated on: ",new Date().toLocaleDateString()]}),(0,s.jsx)("p",{children:"WorkHub"})]})]})]})})})}},89113:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>o.a,__next_app__:()=>p,pages:()=>d,routeModule:()=>x,tree:()=>c});var s=t(65239),i=t(48088),a=t(88170),o=t.n(a),n=t(30893),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);t.d(r,l);let c={children:["",{children:["vehicles",{children:["[id]",{children:["report",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,74939)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\vehicles\\[id]\\report\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,82196)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\vehicles\\[id]\\report\\layout.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\vehicles\\[id]\\report\\page.tsx"],p={require:t,loadChunk:()=>Promise.resolve()},x=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/vehicles/[id]/report/page",pathname:"/vehicles/[id]/report",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,3622,1658,5880,474,6055,8141,3983,4318],()=>t(89113));module.exports=s})();