(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6683],{14247:(e,a,t)=>{Promise.resolve().then(t.bind(t,55201))},18763:(e,a,t)=>{"use strict";t.d(a,{A:()=>i});let i=(0,t(40157).A)("SquarePen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},55201:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>p});var i=t(95155),s=t(12115),l=t(35695),c=t(36521),d=t(2730),n=t(95647),r=t(18763),o=t(28328),u=t(87481),h=t(68856),m=t(30285);let p=()=>{let e=(0,l.useRouter)(),a=(0,l.useParams)(),{toast:t}=(0,u.dj)(),p=a.id?Number(a.id):null,[v,x]=(0,s.useState)(null),[f,j]=(0,s.useState)(!1),[y,N]=(0,s.useState)(!0),[E,g]=(0,s.useState)(null),k=(0,s.useCallback)(async()=>{if(!p){g("No vehicle ID provided."),N(!1);return}N(!0);try{let e=await (0,d.getVehicleById)(p);e?x(e):g("Vehicle not found.")}catch(e){console.error("Failed to fetch vehicle:",e),g(e.message||"Failed to load vehicle data.")}finally{N(!1)}},[p]);(0,s.useEffect)(()=>{k()},[k]);let w=async a=>{if(!p)return void g("Cannot update vehicle without an ID.");j(!0),g(null);try{let i={...a,initialOdometer:void 0===a.initialOdometer?(null==v?void 0:v.initialOdometer)||0:a.initialOdometer};await (0,d.updateVehicle)(p,i),t({title:"Vehicle Updated",description:"".concat(a.make," ").concat(a.model," has been successfully updated."),variant:"default"}),e.push("/vehicles")}catch(e){console.error("Failed to update vehicle:",e),g(e.message||"An unexpected error occurred. Please try again."),t({title:"Error Updating Vehicle",description:e.message||"Could not update the vehicle. Please check the details and try again.",variant:"destructive"})}finally{j(!1)}};return y?(0,i.jsxs)("div",{className:"container mx-auto py-8 space-y-8",children:[(0,i.jsx)(n.z,{title:"Edit Vehicle",description:"Loading vehicle details...",icon:r.A}),(0,i.jsxs)("div",{className:"max-w-2xl mx-auto space-y-6",children:[(0,i.jsx)(h.E,{className:"h-10 w-1/3"}),(0,i.jsx)(h.E,{className:"h-12 w-full"}),(0,i.jsx)(h.E,{className:"h-12 w-full"}),(0,i.jsx)(h.E,{className:"h-12 w-full"}),(0,i.jsxs)("div",{className:"flex justify-end space-x-3 pt-6",children:[(0,i.jsx)(h.E,{className:"h-10 w-24"}),(0,i.jsx)(h.E,{className:"h-10 w-24"})]})]})]}):E&&!v?(0,i.jsxs)("div",{className:"container mx-auto py-8 space-y-8 text-center",children:[(0,i.jsx)(n.z,{title:"Error",description:E,icon:o.A}),(0,i.jsx)(m.$,{onClick:()=>e.push("/vehicles"),children:"Back to Vehicles"})]}):(0,i.jsxs)("div",{className:"container mx-auto py-8 space-y-8",children:[(0,i.jsx)(n.z,{title:"Edit Vehicle",description:"Update details for ".concat((null==v?void 0:v.make)||"vehicle"," ").concat((null==v?void 0:v.model)||""),icon:r.A}),E&&v&&(0,i.jsxs)("p",{className:"text-red-500 bg-red-100 p-3 rounded-md",children:["Error: ",E]}),v&&(0,i.jsx)(c.A,{onSubmit:w,initialData:v,isEditing:!0,isLoading:f})]})}},68856:(e,a,t)=>{"use strict";t.d(a,{E:()=>l});var i=t(95155),s=t(59434);function l(e){let{className:a,...t}=e;return(0,i.jsx)("div",{className:(0,s.cn)("animate-pulse rounded-md bg-muted",a),...t})}}},e=>{var a=a=>e(e.s=a);e.O(0,[3796,6113,832,4066,8162,2730,9634,8441,1684,7358],()=>a(14247)),_N_E=e.O()}]);