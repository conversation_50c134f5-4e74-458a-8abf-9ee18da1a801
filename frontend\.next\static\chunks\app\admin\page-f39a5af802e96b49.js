(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3698],{6560:(e,s,t)=>{"use strict";t.d(s,{r:()=>c});var a=t(95155),r=t(12115),l=t(30285),n=t(50172),i=t(59434);let c=r.forwardRef((e,s)=>{let{actionType:t="primary",icon:r,isLoading:c=!1,loadingText:d,className:o,children:m,disabled:x,asChild:u=!1,...h}=e,{variant:f,className:p}={primary:{variant:"default",className:"shadow-md"},secondary:{variant:"secondary",className:""},tertiary:{variant:"outline",className:""},danger:{variant:"destructive",className:"shadow-md"}}[t];return(0,a.jsx)(l.$,{ref:s,variant:f,className:(0,i.cn)(p,o),disabled:c||x,asChild:u,...h,children:c?(0,a.jsxs)("span",{className:"inline-flex items-center",children:[" ",(0,a.jsx)(n.A,{className:"mr-2 h-4 w-4 animate-spin"}),d||m]}):(0,a.jsxs)("span",{className:"inline-flex items-center",children:[" ",r&&(0,a.jsx)("span",{className:"mr-2",children:r}),m]})})});c.displayName="ActionButton"},26126:(e,s,t)=>{"use strict";t.d(s,{E:()=>i});var a=t(95155);t(12115);var r=t(74466),l=t(59434);let n=(0,r.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function i(e){let{className:s,variant:t,...r}=e;return(0,a.jsx)("div",{className:(0,l.cn)(n({variant:t}),s),...r})}},33066:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>ej});var a=t(95155),r=t(95647),l=t(18271),n=t(50594),i=t(34301),c=t(12115),d=t(60704),o=t(59434);let m=d.bL,x=c.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(d.B8,{ref:s,className:(0,o.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",t),...r})});x.displayName=d.B8.displayName;let u=c.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(d.l9,{ref:s,className:(0,o.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",t),...r})});u.displayName=d.l9.displayName;let h=c.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(d.UC,{ref:s,className:(0,o.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",t),...r})});h.displayName=d.UC.displayName;var f=t(66695),p=t(26126),j=t(8376),N=t(24371),g=t(11133),v=t(67554),b=t(6560),y=t(52582);let w={maxRetries:3,initialDelay:300,maxDelay:5e3,backoffFactor:2,shouldRetry:e=>!e.response||e.response.status>=500&&e.response.status<600},R=e=>new Promise(s=>setTimeout(s,e)),k=function(e){let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:w,{initialDelay:t=300,backoffFactor:a=2,maxDelay:r=5e3}=s;return Math.min(t*Math.pow(a,e)+100*Math.random(),r)};async function C(e){let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:w,{maxRetries:t=3,shouldRetry:a=w.shouldRetry}=s,r=0;for(;;)try{return await e()}catch(l){if(++r>=t||!a(l))throw l;let e=k(r,s);console.warn("API call failed, retrying (".concat(r,"/").concat(t,") after ").concat(e,"ms"),l),await R(e)}}function A(e){return async function(){for(var s=arguments.length,t=Array(s),a=0;a<s;a++)t[a]=arguments[a];var r=await e(...t);if(null==r)return r;if(r&&"object"==typeof r&&"status"in r){if("success"===r.status&&"data"in r)return r.data;else if("error"===r.status)throw Error(r.message||"Unknown error")}return r}}let S=async()=>A(()=>C(()=>(0,y.Fd)("/admin/health")))(),E=async()=>A(()=>C(()=>(0,y.Fd)("/admin/performance")))(),T=async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{page:s=1,limit:t=10,level:a}=e,r="/admin/errors?page=".concat(s,"&limit=").concat(t);a&&(r+="&level=".concat(a));let l=await C(()=>(0,y.Fd)(r));return l&&"object"==typeof l&&"data"in l&&"pagination"in l?l:l&&"object"==typeof l&&"status"in l&&"success"===l.status?{data:l.data,pagination:l.pagination}:l};var L=t(68856);function D(e){let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{initialData:t=null,autoFetch:a=!0,deps:r=[],onSuccess:l,onError:n,...i}=s,[d,o]=(0,c.useState)(t),[m,x]=(0,c.useState)(a),[u,h]=(0,c.useState)(null),f=(0,c.useCallback)(async()=>{x(!0),h(null);try{let s=await C(()=>e(),i);o(s),l&&l(s)}catch(e){h(e.message||"An unexpected error occurred"),n&&n(e),console.error("API call failed:",e)}finally{x(!1)}},[e,...r]);return(0,c.useEffect)(()=>{a&&f()},[f,a]),{data:d,isLoading:m,error:u,refetch:f}}var B=t(50172),F=t(55365),P=t(30285);function _(e){let{message:s,onRetry:t,className:r}=e;return(0,a.jsxs)(F.Fc,{variant:"destructive",className:(0,o.cn)("my-4",r),children:[(0,a.jsx)(g.A,{className:"h-4 w-4"}),(0,a.jsx)(F.XL,{children:"Error"}),(0,a.jsx)(F.TN,{children:(0,a.jsxs)("div",{className:"mt-2",children:[(0,a.jsx)("p",{className:"text-sm text-muted-foreground mb-4",children:s}),t&&(0,a.jsxs)(P.$,{variant:"outline",size:"sm",onClick:t,className:"flex items-center",children:[(0,a.jsx)(B.A,{className:"mr-2 h-4 w-4"}),"Retry"]})]})})]})}var H=t(31949);class I extends c.Component{static getDerivedStateFromError(e){return{hasError:!0,error:e,errorInfo:null}}componentDidCatch(e,s){console.error("Error caught by ErrorBoundary:",e,s),this.setState({errorInfo:s})}render(){if(this.state.hasError){var e;return this.props.fallback?this.props.fallback:(0,a.jsxs)(F.Fc,{variant:"destructive",className:"my-4",children:[(0,a.jsx)(H.A,{className:"h-4 w-4"}),(0,a.jsx)(F.XL,{children:"Something went wrong"}),(0,a.jsx)(F.TN,{children:(0,a.jsxs)("div",{className:"mt-2",children:[(0,a.jsx)("p",{className:"text-sm text-muted-foreground mb-4",children:(null==(e=this.state.error)?void 0:e.message)||"An unexpected error occurred"}),(0,a.jsxs)(P.$,{variant:"outline",size:"sm",onClick:this.resetErrorBoundary,className:"flex items-center",children:[(0,a.jsx)(v.A,{className:"mr-2 h-4 w-4"}),"Try again"]})]})})]})}return this.props.children}constructor(e){super(e),this.resetErrorBoundary=()=>{this.props.onReset&&this.props.onReset(),this.setState({hasError:!1,error:null,errorInfo:null})},this.state={hasError:!1,error:null,errorInfo:null}}}function W(){let{data:e,isLoading:s,error:t,refetch:r}=D(()=>S(),{maxRetries:3,initialDelay:500,deps:[Math.floor(Date.now()/3e4)]}),[l,n]=(0,c.useState)(!1),i=async()=>{n(!0),await r(),n(!1)},d=e=>"UP"===e?(0,a.jsx)(j.A,{className:"h-5 w-5 text-green-500"}):"DOWN"===e?(0,a.jsx)(N.A,{className:"h-5 w-5 text-red-500"}):(0,a.jsx)(g.A,{className:"h-5 w-5 text-yellow-500"}),o=e=>"UP"===e?(0,a.jsx)(p.E,{variant:"outline",className:"bg-green-500/20 text-green-700 border-green-500/30",children:"Connected"}):"DOWN"===e?(0,a.jsx)(p.E,{variant:"outline",className:"bg-red-500/20 text-red-700 border-red-500/30",children:"Disconnected"}):(0,a.jsx)(p.E,{variant:"outline",className:"bg-yellow-500/20 text-yellow-700 border-yellow-500/30",children:"Unknown"});return(0,a.jsx)(I,{children:(0,a.jsxs)(f.Zp,{className:"shadow-md",children:[(0,a.jsxs)(f.aR,{className:"pb-2 p-5",children:[(0,a.jsx)(f.ZB,{className:"text-xl font-semibold text-primary",children:"Connection Status"}),(0,a.jsx)(f.BT,{children:"Current status of database connections"})]}),(0,a.jsx)(f.Wu,{className:"p-5",children:s||l?(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)(L.E,{className:"h-6 w-full"}),(0,a.jsx)(L.E,{className:"h-6 w-full"}),(0,a.jsx)(L.E,{className:"h-6 w-full"})]}):t?(0,a.jsx)(_,{message:t,onRetry:r}):e?(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("div",{className:"flex items-center space-x-2",children:(0,a.jsx)("span",{className:"font-medium",children:"Overall Status:"})}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[d(e.status),o(e.status)]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"font-medium",children:"Database:"}),(0,a.jsx)("span",{className:"text-sm text-muted-foreground",children:e.components.database.type})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[d(e.components.database.status),o(e.components.database.status)]})]}),e.components.database.error&&(0,a.jsxs)("div",{className:"ml-6 p-2 text-sm border-l-2 border-red-300 bg-red-500/10 text-red-700 dark:text-red-400",children:["Error: ",e.components.database.error.message||String(e.components.database.error)]}),e.components.supabase&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"font-medium",children:"Supabase:"}),(0,a.jsx)("span",{className:"text-sm text-muted-foreground",children:e.components.supabase.url})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[d(e.components.supabase.status),o(e.components.supabase.status)]})]}),e.components.supabase.error&&(0,a.jsxs)("div",{className:"ml-6 p-2 text-sm border-l-2 border-red-300 bg-red-500/10 text-red-700 dark:text-red-400",children:["Error: ",e.components.supabase.error.message||String(e.components.supabase.error)]})]}),e.version&&(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"font-medium",children:"Version:"}),(0,a.jsx)("span",{children:e.version})]}),void 0!==e.uptime&&(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"font-medium",children:"Uptime:"}),(0,a.jsxs)("span",{children:[Math.floor(e.uptime/3600),"h"," ",Math.floor(e.uptime%3600/60),"m"]})]}),(0,a.jsxs)("div",{className:"text-xs text-muted-foreground",children:["Last updated: ",new Date(e.timestamp).toLocaleString()]})]}):null}),(0,a.jsx)(f.wL,{className:"p-5",children:(0,a.jsx)(b.r,{actionType:"tertiary",size:"sm",className:"w-full",onClick:i,isLoading:l||s,loadingText:"Refreshing...",icon:(0,a.jsx)(v.A,{className:"h-4 w-4"}),children:"Refresh Status"})})]})})}var Z=t(55863);let M=c.forwardRef((e,s)=>{let{className:t,value:r,...l}=e;return(0,a.jsx)(Z.bL,{ref:s,className:(0,o.cn)("relative h-4 w-full overflow-hidden rounded-full bg-secondary",t),...l,children:(0,a.jsx)(Z.C1,{className:"h-full w-full flex-1 bg-primary transition-all",style:{transform:"translateX(-".concat(100-(r||0),"%)")}})})});function O(){let{data:e,isLoading:s,error:t,refetch:r}=D(()=>E(),{maxRetries:3,initialDelay:500,deps:[Math.floor(Date.now()/6e4)]}),[l,n]=(0,c.useState)(!1),i=async()=>{n(!0),await r(),n(!1)},d=e=>e>=90?"bg-green-500":e>=70?"bg-yellow-500":"bg-red-500";return(0,a.jsx)(I,{children:(0,a.jsxs)(f.Zp,{className:"shadow-md",children:[(0,a.jsxs)(f.aR,{className:"pb-2 p-5",children:[(0,a.jsx)(f.ZB,{className:"text-xl font-semibold text-primary",children:"Database Health"}),(0,a.jsx)(f.BT,{children:"Performance metrics and health indicators"})]}),(0,a.jsx)(f.Wu,{className:"p-5",children:s||l?(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)(L.E,{className:"h-6 w-full"}),(0,a.jsx)(L.E,{className:"h-6 w-full"}),(0,a.jsx)(L.E,{className:"h-6 w-full"}),(0,a.jsx)(L.E,{className:"h-6 w-full"})]}):t?(0,a.jsx)(_,{message:t,onRetry:r}):e?(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex justify-between mb-1",children:[(0,a.jsx)("span",{className:"text-sm font-medium",children:"Index Cache Hit Rate"}),(0,a.jsxs)("span",{className:"text-sm font-medium",children:[e.cacheHitRate.indexHitRate.toFixed(1),"%"]})]}),(0,a.jsx)(M,{value:e.cacheHitRate.indexHitRate,className:d(e.cacheHitRate.indexHitRate)}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground mt-1",children:"Percentage of index lookups served from cache"})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex justify-between mb-1",children:[(0,a.jsx)("span",{className:"text-sm font-medium",children:"Table Cache Hit Rate"}),(0,a.jsxs)("span",{className:"text-sm font-medium",children:[e.cacheHitRate.tableHitRate.toFixed(1),"%"]})]}),(0,a.jsx)(M,{value:e.cacheHitRate.tableHitRate,className:d(e.cacheHitRate.tableHitRate)}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground mt-1",children:"Percentage of table lookups served from cache"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4 pt-2",children:[(0,a.jsxs)("div",{className:"border rounded-md p-3",children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:e.connectionCount}),(0,a.jsx)("div",{className:"text-xs text-muted-foreground",children:"Active Connections"})]}),(0,a.jsxs)("div",{className:"border rounded-md p-3",children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:e.activeQueries}),(0,a.jsx)("div",{className:"text-xs text-muted-foreground",children:"Running Queries"})]}),(0,a.jsxs)("div",{className:"border rounded-md p-3 col-span-2",children:[(0,a.jsxs)("div",{className:"text-2xl font-bold",children:[e.avgQueryTime.toFixed(2),"ms"]}),(0,a.jsx)("div",{className:"text-xs text-muted-foreground",children:"Average Query Time"})]})]}),e.timestamp&&(0,a.jsxs)("div",{className:"text-xs text-muted-foreground",children:["Last updated: ",new Date(e.timestamp).toLocaleString()]})]}):null}),(0,a.jsx)(f.wL,{className:"p-5",children:(0,a.jsx)(b.r,{actionType:"tertiary",size:"sm",className:"w-full",onClick:i,isLoading:l||s,loadingText:"Refreshing...",icon:(0,a.jsx)(v.A,{className:"h-4 w-4"}),children:"Refresh Metrics"})})]})})}M.displayName=Z.bL.displayName;var z=t(9572),U=t(66424),V=t(59409);function q(){let[e,s]=(0,c.useState)(void 0),[t,r]=(0,c.useState)(!1),{data:l,isLoading:i,error:d,refetch:o,page:m,totalPages:x,nextPage:u,prevPage:h,hasNextPage:j,hasPrevPage:g}=function(e){let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{initialPage:t=1,pageSize:a=10,...r}=s,[l,n]=(0,c.useState)(t),[i,d]=(0,c.useState)(1),[o,m]=(0,c.useState)(0),x=D((0,c.useCallback)(()=>e(l,a).then(e=>(d(e.pagination.totalPages),m(e.pagination.total),e.data)),[e,l,a]),{...r,deps:[...r.deps||[],l,a]}),u=(0,c.useCallback)(()=>{l<i&&n(e=>e+1)},[l,i]),h=(0,c.useCallback)(()=>{l>1&&n(e=>e-1)},[l]),f=(0,c.useCallback)(e=>{e>=1&&e<=i&&n(e)},[i]);return{...x,page:l,totalPages:i,totalItems:o,nextPage:u,prevPage:h,goToPage:f,hasNextPage:l<i,hasPrevPage:l>1}}((s,t)=>T({page:s,limit:t,level:e}),{maxRetries:3,initialDelay:500,pageSize:10,deps:[e]}),y=async()=>{r(!0),await o(),r(!1)},w=e=>"ERROR"===e?(0,a.jsx)(N.A,{className:"h-4 w-4 text-red-500"}):"WARNING"===e?(0,a.jsx)(H.A,{className:"h-4 w-4 text-yellow-500"}):(0,a.jsx)(n.A,{className:"h-4 w-4 text-blue-500"}),R=e=>"ERROR"===e?(0,a.jsx)(p.E,{variant:"outline",className:"bg-red-500/20 text-red-700 border-red-500/30",children:"Error"}):"WARNING"===e?(0,a.jsx)(p.E,{variant:"outline",className:"bg-yellow-500/20 text-yellow-700 border-yellow-500/30",children:"Warning"}):(0,a.jsx)(p.E,{variant:"outline",className:"bg-blue-500/20 text-blue-700 border-blue-500/30",children:"Info"});return(0,a.jsx)(I,{children:(0,a.jsxs)(f.Zp,{className:"shadow-md",children:[(0,a.jsxs)(f.aR,{className:"pb-2 p-5",children:[(0,a.jsx)(f.ZB,{className:"text-xl font-semibold text-primary",children:"Recent Errors & Warnings"}),(0,a.jsx)(f.BT,{children:"Latest system errors and warnings"})]}),(0,a.jsx)("div",{className:"px-5 pb-2",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(z.A,{className:"h-4 w-4 text-muted-foreground"}),(0,a.jsx)("span",{className:"text-sm font-medium",children:"Filter by level:"}),(0,a.jsxs)(V.l6,{value:e||"all",onValueChange:e=>{s("all"===e?void 0:e)},children:[(0,a.jsx)(V.bq,{className:"w-[140px]",children:(0,a.jsx)(V.yv,{placeholder:"All levels"})}),(0,a.jsxs)(V.gC,{children:[(0,a.jsx)(V.eb,{value:"all",children:"All levels"}),(0,a.jsx)(V.eb,{value:"ERROR",children:"Errors only"}),(0,a.jsx)(V.eb,{value:"WARNING",children:"Warnings only"}),(0,a.jsx)(V.eb,{value:"INFO",children:"Info only"})]})]})]})}),(0,a.jsx)(f.Wu,{className:"p-5",children:i||t?(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)(L.E,{className:"h-6 w-full"}),(0,a.jsx)(L.E,{className:"h-6 w-full"}),(0,a.jsx)(L.E,{className:"h-6 w-full"}),(0,a.jsx)(L.E,{className:"h-6 w-full"}),(0,a.jsx)(L.E,{className:"h-6 w-full"})]}):d?(0,a.jsx)(_,{message:d,onRetry:o}):l&&l.length>0?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(U.F,{className:"h-[300px] pr-4",children:(0,a.jsx)("div",{className:"space-y-3",children:l.map(e=>(0,a.jsxs)("div",{className:"p-3 border rounded-md hover:bg-accent/50 transition-colors",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-1",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[w(e.level),(0,a.jsx)("span",{className:"font-medium",children:e.message})]}),R(e.level)]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("div",{className:"text-xs text-muted-foreground",children:new Date(e.timestamp).toLocaleString()}),e.source&&(0,a.jsxs)("div",{className:"text-xs text-muted-foreground",children:["Source: ",e.source]})]}),e.details&&Object.keys(e.details).length>0&&(0,a.jsx)("div",{className:"mt-2 text-xs p-2 bg-muted rounded",children:(0,a.jsx)("pre",{className:"whitespace-pre-wrap",children:JSON.stringify(e.details,null,2)})})]},e.id))})}),x>1&&(0,a.jsxs)("div",{className:"flex items-center justify-between mt-4",children:[(0,a.jsx)(b.r,{actionType:"tertiary",size:"sm",onClick:h,disabled:!g||i||t,children:"Previous"}),(0,a.jsxs)("span",{className:"text-sm text-muted-foreground",children:["Page ",m," of ",x]}),(0,a.jsx)(b.r,{actionType:"tertiary",size:"sm",onClick:u,disabled:!j||i||t,children:"Next"})]})]}):(0,a.jsxs)("div",{className:"p-8 text-center text-muted-foreground",children:[(0,a.jsx)(H.A,{className:"mx-auto h-8 w-8 mb-2 text-muted-foreground/50"}),(0,a.jsx)("p",{children:"No errors or warnings found for the selected filter."}),e&&(0,a.jsx)("p",{className:"text-sm mt-2",children:"Try changing the filter to see more results."})]})}),(0,a.jsx)(f.wL,{className:"p-5",children:(0,a.jsx)(b.r,{actionType:"tertiary",size:"sm",className:"w-full",onClick:y,isLoading:t||i,loadingText:"Refreshing...",icon:(0,a.jsx)(v.A,{className:"h-4 w-4"}),children:"Refresh Logs"})})]})})}var K=t(40207),Q=t(83540),X=t(94517),J=t(24026);let G={light:"",dark:".dark"},$=c.createContext(null);function Y(){let e=c.useContext($);if(!e)throw Error("useChart must be used within a <ChartContainer />");return e}let ee=c.forwardRef((e,s)=>{let{id:t,className:r,children:l,config:n,...i}=e,d=c.useId(),m="chart-".concat(t||d.replace(/:/g,""));return(0,a.jsx)($.Provider,{value:{config:n},children:(0,a.jsxs)("div",{"data-chart":m,ref:s,className:(0,o.cn)("flex aspect-video justify-center text-xs [&_.recharts-cartesian-axis-tick_text]:fill-muted-foreground [&_.recharts-cartesian-grid_line[stroke='#ccc']]:stroke-border/50 [&_.recharts-curve.recharts-tooltip-cursor]:stroke-border [&_.recharts-dot[stroke='#fff']]:stroke-transparent [&_.recharts-layer]:outline-none [&_.recharts-polar-grid_[stroke='#ccc']]:stroke-border [&_.recharts-radial-bar-background-sector]:fill-muted [&_.recharts-rectangle.recharts-tooltip-cursor]:fill-muted [&_.recharts-reference-line_[stroke='#ccc']]:stroke-border [&_.recharts-sector[stroke='#fff']]:stroke-transparent [&_.recharts-sector]:outline-none [&_.recharts-surface]:outline-none",r),...i,children:[(0,a.jsx)(es,{id:m,config:n}),(0,a.jsx)(Q.u,{children:l})]})})});ee.displayName="Chart";let es=e=>{let{id:s,config:t}=e,r=Object.entries(t).filter(e=>{let[,s]=e;return s.theme||s.color});return r.length?(0,a.jsx)("style",{dangerouslySetInnerHTML:{__html:Object.entries(G).map(e=>{let[t,a]=e;return"\n".concat(a," [data-chart=").concat(s,"] {\n").concat(r.map(e=>{var s;let[a,r]=e,l=(null==(s=r.theme)?void 0:s[t])||r.color;return l?"  --color-".concat(a,": ").concat(l,";"):null}).join("\n"),"\n}\n")}).join("\n")}}):null},et=X.m,ea=c.forwardRef((e,s)=>{let{active:t,payload:r,className:l,indicator:n="dot",hideLabel:i=!1,hideIndicator:d=!1,label:m,labelFormatter:x,labelClassName:u,formatter:h,color:f,nameKey:p,labelKey:j}=e,{config:N}=Y(),g=c.useMemo(()=>{var e;if(i||!(null==r?void 0:r.length))return null;let[s]=r,t="".concat(j||s.dataKey||s.name||"value"),l=en(N,s,t),n=j||"string"!=typeof m?null==l?void 0:l.label:(null==(e=N[m])?void 0:e.label)||m;return x?(0,a.jsx)("div",{className:(0,o.cn)("font-medium",u),children:x(n,r)}):n?(0,a.jsx)("div",{className:(0,o.cn)("font-medium",u),children:n}):null},[m,x,r,i,u,N,j]);if(!t||!(null==r?void 0:r.length))return null;let v=1===r.length&&"dot"!==n;return(0,a.jsxs)("div",{ref:s,className:(0,o.cn)("grid min-w-[8rem] items-start gap-1.5 rounded-lg border border-border/50 bg-background px-2.5 py-1.5 text-xs shadow-xl",l),children:[v?null:g,(0,a.jsx)("div",{className:"grid gap-1.5",children:r.map((e,s)=>{let t="".concat(p||e.name||e.dataKey||"value"),r=en(N,e,t),l=f||e.payload.fill||e.color;return(0,a.jsx)("div",{className:(0,o.cn)("flex w-full flex-wrap items-stretch gap-2 [&>svg]:h-2.5 [&>svg]:w-2.5 [&>svg]:text-muted-foreground","dot"===n&&"items-center"),children:h&&(null==e?void 0:e.value)!==void 0&&e.name?h(e.value,e.name,e,s,e.payload):(0,a.jsxs)(a.Fragment,{children:[(null==r?void 0:r.icon)?(0,a.jsx)(r.icon,{}):!d&&(0,a.jsx)("div",{className:(0,o.cn)("shrink-0 rounded-[2px] border-[--color-border] bg-[--color-bg]",{"h-2.5 w-2.5":"dot"===n,"w-1":"line"===n,"w-0 border-[1.5px] border-dashed bg-transparent":"dashed"===n,"my-0.5":v&&"dashed"===n}),style:{"--color-bg":l,"--color-border":l}}),(0,a.jsxs)("div",{className:(0,o.cn)("flex flex-1 justify-between leading-none",v?"items-end":"items-center"),children:[(0,a.jsxs)("div",{className:"grid gap-1.5",children:[v?g:null,(0,a.jsx)("span",{className:"text-muted-foreground",children:(null==r?void 0:r.label)||e.name})]}),e.value&&(0,a.jsx)("span",{className:"font-mono font-medium tabular-nums text-foreground",children:e.value.toLocaleString()})]})]})},e.dataKey)})})]})});ea.displayName="ChartTooltip";let er=J.s,el=c.forwardRef((e,s)=>{let{className:t,hideIcon:r=!1,payload:l,verticalAlign:n="bottom",nameKey:i}=e,{config:c}=Y();return(null==l?void 0:l.length)?(0,a.jsx)("div",{ref:s,className:(0,o.cn)("flex items-center justify-center gap-4","top"===n?"pb-3":"pt-3",t),children:l.map(e=>{let s="".concat(i||e.dataKey||"value"),t=en(c,e,s);return(0,a.jsxs)("div",{className:(0,o.cn)("flex items-center gap-1.5 [&>svg]:h-3 [&>svg]:w-3 [&>svg]:text-muted-foreground"),children:[(null==t?void 0:t.icon)&&!r?(0,a.jsx)(t.icon,{}):(0,a.jsx)("div",{className:"h-2 w-2 shrink-0 rounded-[2px]",style:{backgroundColor:e.color}}),null==t?void 0:t.label]},e.value)})}):null});function en(e,s,t){if("object"!=typeof s||null===s)return;let a="payload"in s&&"object"==typeof s.payload&&null!==s.payload?s.payload:void 0,r=t;return t in s&&"string"==typeof s[t]?r=s[t]:a&&t in a&&"string"==typeof a[t]&&(r=a[t]),r in e?e[r]:e[t]}el.displayName="ChartLegend";var ei=t(56965),ec=t(94754),ed=t(96025),eo=t(16238),em=t(21374);let ex=()=>{let e=[],s=new Date;for(let t=24;t>=0;t--){let a=new Date(s.getTime()-36e5*t);e.push({time:a.toISOString(),queryTime:2*Math.random()+.5,connections:Math.floor(15*Math.random())+5,cacheHitRate:10*Math.random()+90})}return e};function eu(){let{data:e,isLoading:s,error:t,refetch:r}=D(()=>E(),{maxRetries:3,initialDelay:500,deps:[Math.floor(Date.now()/12e4)]}),[l,n]=(0,c.useState)([]),[i,d]=(0,c.useState)(!1);(0,c.useEffect)(()=>{e&&n(ex())},[e]);let o=async()=>{d(!0),await r(),d(!1)};return(0,a.jsx)(I,{children:(0,a.jsxs)(f.Zp,{className:"shadow-md",children:[(0,a.jsxs)(f.aR,{className:"pb-2 p-5",children:[(0,a.jsx)(f.ZB,{className:"text-xl font-semibold text-primary",children:"Performance Statistics"}),(0,a.jsx)(f.BT,{children:"Database performance over time"})]}),(0,a.jsx)(f.Wu,{className:"p-5",children:s||i?(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)(L.E,{className:"h-[200px] w-full"}),(0,a.jsxs)("div",{className:"grid grid-cols-3 gap-4",children:[(0,a.jsx)(L.E,{className:"h-16 w-full"}),(0,a.jsx)(L.E,{className:"h-16 w-full"}),(0,a.jsx)(L.E,{className:"h-16 w-full"})]})]}):t?(0,a.jsx)(_,{message:t,onRetry:r}):e&&l.length>0?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"h-[200px] mt-4",children:(0,a.jsx)(ee,{config:{queryTime:{label:"Avg Query Time (ms)",color:"hsl(var(--chart-1))"},connections:{label:"Connections",color:"hsl(var(--chart-2))"}},children:(0,a.jsxs)(ei.b,{data:l,margin:{left:12,right:12},children:[(0,a.jsx)(er,{content:(0,a.jsx)(el,{})}),(0,a.jsx)(ec.d,{vertical:!1}),(0,a.jsx)(ed.W,{dataKey:"time",tickFormatter:e=>{let s=new Date(e);return"".concat(s.getHours(),":00")}}),(0,a.jsx)(eo.h,{}),(0,a.jsx)(et,{content:(0,a.jsx)(ea,{})}),(0,a.jsx)(em.N,{type:"monotone",dataKey:"queryTime",stroke:"var(--color-queryTime)",strokeWidth:2,dot:!1}),(0,a.jsx)(em.N,{type:"monotone",dataKey:"connections",stroke:"var(--color-connections)",strokeWidth:2,dot:!1})]})})}),(0,a.jsxs)("div",{className:"grid grid-cols-3 gap-4 mt-6",children:[(0,a.jsxs)("div",{className:"border rounded-md p-3 text-center",children:[(0,a.jsxs)("div",{className:"text-2xl font-bold",children:[e.avgQueryTime.toFixed(2),"ms"]}),(0,a.jsx)("div",{className:"text-xs text-muted-foreground",children:"Current Query Time"})]}),(0,a.jsxs)("div",{className:"border rounded-md p-3 text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:e.connectionCount}),(0,a.jsx)("div",{className:"text-xs text-muted-foreground",children:"Current Connections"})]}),(0,a.jsxs)("div",{className:"border rounded-md p-3 text-center",children:[(0,a.jsxs)("div",{className:"text-2xl font-bold",children:[e.cacheHitRate.indexHitRate.toFixed(1),"%"]}),(0,a.jsx)("div",{className:"text-xs text-muted-foreground",children:"Cache Hit Rate"})]})]}),e.timestamp&&(0,a.jsxs)("div",{className:"text-xs text-muted-foreground text-center mt-4",children:["Last updated: ",new Date(e.timestamp).toLocaleString()]})]}):(0,a.jsxs)("div",{className:"p-8 text-center text-muted-foreground",children:[(0,a.jsx)(K.A,{className:"mx-auto h-8 w-8 mb-2 text-muted-foreground/50"}),(0,a.jsx)("p",{children:"No performance data available"})]})}),(0,a.jsx)(f.wL,{className:"p-5",children:(0,a.jsx)(b.r,{actionType:"tertiary",size:"sm",className:"w-full",onClick:o,isLoading:i||s,loadingText:"Refreshing...",icon:(0,a.jsx)(v.A,{className:"h-4 w-4"}),children:"Refresh Statistics"})})]})})}var eh=t(45727),ef=t(69321);function ep(){return(0,a.jsxs)(f.Zp,{className:"border-none shadow-none",children:[(0,a.jsxs)(f.aR,{className:"px-0 pt-0",children:[(0,a.jsx)(f.ZB,{className:"text-2xl font-bold",children:"Supabase Diagnostics"}),(0,a.jsx)(f.BT,{children:"Monitor and troubleshoot your Supabase database connection"})]}),(0,a.jsx)(f.Wu,{className:"px-0",children:(0,a.jsxs)(m,{defaultValue:"connection",className:"w-full",children:[(0,a.jsxs)(x,{className:"grid w-full grid-cols-4",children:[(0,a.jsxs)(u,{value:"connection",className:"flex items-center",children:[(0,a.jsx)(eh.A,{className:"mr-2 h-4 w-4"}),(0,a.jsx)("span",{className:"hidden sm:inline",children:"Connection"})]}),(0,a.jsxs)(u,{value:"health",className:"flex items-center",children:[(0,a.jsx)(ef.A,{className:"mr-2 h-4 w-4"}),(0,a.jsx)("span",{className:"hidden sm:inline",children:"Health"})]}),(0,a.jsxs)(u,{value:"errors",className:"flex items-center",children:[(0,a.jsx)(H.A,{className:"mr-2 h-4 w-4"}),(0,a.jsx)("span",{className:"hidden sm:inline",children:"Errors"})]}),(0,a.jsxs)(u,{value:"performance",className:"flex items-center",children:[(0,a.jsx)(K.A,{className:"mr-2 h-4 w-4"}),(0,a.jsx)("span",{className:"hidden sm:inline",children:"Performance"})]})]}),(0,a.jsx)(h,{value:"connection",className:"mt-4",children:(0,a.jsx)(W,{})}),(0,a.jsx)(h,{value:"health",className:"mt-4",children:(0,a.jsx)(O,{})}),(0,a.jsx)(h,{value:"errors",className:"mt-4",children:(0,a.jsx)(q,{})}),(0,a.jsx)(h,{value:"performance",className:"mt-4",children:(0,a.jsx)(eu,{})})]})})]})}function ej(){return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)(r.z,{title:"Admin Dashboard",description:"System administration and diagnostics",icon:l.A}),(0,a.jsxs)(F.Fc,{children:[(0,a.jsx)(n.A,{className:"h-4 w-4"}),(0,a.jsx)(F.XL,{children:"Information"}),(0,a.jsx)(F.TN,{children:"This admin dashboard provides system diagnostics and monitoring tools. No authentication is required for demonstration purposes."})]}),(0,a.jsxs)(m,{defaultValue:"diagnostics",className:"w-full",children:[(0,a.jsxs)(x,{className:"grid w-full grid-cols-1 md:grid-cols-3",children:[(0,a.jsx)(u,{value:"diagnostics",children:"Supabase Diagnostics"}),(0,a.jsx)(u,{value:"system",children:"System Status"}),(0,a.jsx)(u,{value:"docs",children:"Documentation"})]}),(0,a.jsx)(h,{value:"diagnostics",className:"mt-6",children:(0,a.jsx)(ep,{})}),(0,a.jsx)(h,{value:"system",className:"mt-6",children:(0,a.jsxs)(f.Zp,{className:"shadow-md",children:[(0,a.jsxs)(f.aR,{className:"p-5",children:[(0,a.jsx)(f.ZB,{className:"text-xl font-semibold text-primary",children:"System Status"}),(0,a.jsx)(f.BT,{children:"Overview of system components and services"})]}),(0,a.jsx)(f.Wu,{className:"p-5",children:(0,a.jsxs)("div",{className:"grid gap-6 md:grid-cols-2 lg:grid-cols-3",children:[(0,a.jsxs)(f.Zp,{className:"shadow-sm",children:[(0,a.jsxs)(f.aR,{className:"pb-2 p-4",children:[(0,a.jsx)(f.ZB,{className:"text-base font-semibold",children:"Backend API"}),(0,a.jsx)(f.BT,{className:"text-xs",children:"Node.js API Server"})]}),(0,a.jsxs)(f.Wu,{className:"p-4 pt-0",children:[(0,a.jsx)("div",{className:"text-xl font-bold text-green-500",children:"Online"}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Version: 1.0.0"})]})]}),(0,a.jsxs)(f.Zp,{className:"shadow-sm",children:[(0,a.jsxs)(f.aR,{className:"pb-2 p-4",children:[(0,a.jsx)(f.ZB,{className:"text-base font-semibold",children:"Frontend"}),(0,a.jsx)(f.BT,{className:"text-xs",children:"Next.js Application"})]}),(0,a.jsxs)(f.Wu,{className:"p-4 pt-0",children:[(0,a.jsx)("div",{className:"text-xl font-bold text-green-500",children:"Online"}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Version: 1.0.0"})]})]}),(0,a.jsxs)(f.Zp,{className:"shadow-sm",children:[(0,a.jsxs)(f.aR,{className:"pb-2 p-4",children:[(0,a.jsx)(f.ZB,{className:"text-base font-semibold",children:"Socket Service"}),(0,a.jsx)(f.BT,{className:"text-xs",children:"Real-time Updates"})]}),(0,a.jsxs)(f.Wu,{className:"p-4 pt-0",children:[(0,a.jsx)("div",{className:"text-xl font-bold text-green-500",children:"Online"}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Active Connections: 3"})]})]})]})})]})}),(0,a.jsx)(h,{value:"docs",className:"mt-6",children:(0,a.jsxs)(f.Zp,{className:"shadow-md",children:[(0,a.jsxs)(f.aR,{className:"p-5",children:[(0,a.jsx)(f.ZB,{className:"text-xl font-semibold text-primary",children:"Admin Documentation"}),(0,a.jsx)(f.BT,{children:"How to extend and customize the admin dashboard"})]}),(0,a.jsxs)(f.Wu,{className:"space-y-4 p-5",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Extending the Admin Dashboard"}),(0,a.jsx)("p",{className:"text-muted-foreground text-sm",children:"The admin dashboard is designed to be easily extensible. Follow these steps to add new features:"}),(0,a.jsxs)("ol",{className:"list-decimal list-inside mt-2 space-y-1 text-muted-foreground text-sm",children:[(0,a.jsxs)("li",{children:["Create new component(s) in the"," ",(0,a.jsx)("code",{className:"bg-muted px-1 rounded",children:"frontend/src/components/admin"})," ","directory"]}),(0,a.jsx)("li",{children:"Add new tab(s) to the main admin page or to specific diagnostic sections"}),(0,a.jsx)("li",{children:"Create new API endpoints in the backend if needed"}),(0,a.jsx)("li",{children:"Update the admin service with new functions to fetch data"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Supabase Integration"}),(0,a.jsx)("p",{className:"text-muted-foreground text-sm",children:"The Supabase diagnostics section communicates with the backend API, which then connects to Supabase. This follows the established pattern of not connecting directly to Supabase from the frontend."}),(0,a.jsx)("p",{className:"text-muted-foreground mt-2 text-sm",children:"To add more Supabase-related features:"}),(0,a.jsxs)("ol",{className:"list-decimal list-inside mt-2 space-y-1 text-muted-foreground text-sm",children:[(0,a.jsx)("li",{children:"Add new endpoints to the backend API"}),(0,a.jsxs)("li",{children:["Create corresponding service functions in"," ",(0,a.jsx)("code",{className:"bg-muted px-1 rounded",children:"adminService.ts"})]}),(0,a.jsx)("li",{children:"Build new UI components that consume these services"})]})]})]}),(0,a.jsx)(f.wL,{className:"p-5",children:(0,a.jsx)(b.r,{actionType:"tertiary",className:"w-full",icon:(0,a.jsx)(i.A,{className:"h-4 w-4"}),children:"Add New Feature (Placeholder)"})})]})})]})]})}},55365:(e,s,t)=>{"use strict";t.d(s,{Fc:()=>c,TN:()=>o,XL:()=>d});var a=t(95155),r=t(12115),l=t(74466),n=t(59434);let i=(0,l.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),c=r.forwardRef((e,s)=>{let{className:t,variant:r,...l}=e;return(0,a.jsx)("div",{ref:s,role:"alert",className:(0,n.cn)(i({variant:r}),t),...l})});c.displayName="Alert";let d=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("h5",{ref:s,className:(0,n.cn)("mb-1 font-medium leading-none tracking-tight",t),...r})});d.displayName="AlertTitle";let o=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:(0,n.cn)("text-sm [&_p]:leading-relaxed",t),...r})});o.displayName="AlertDescription"},59409:(e,s,t)=>{"use strict";t.d(s,{bq:()=>x,eb:()=>p,gC:()=>f,l6:()=>o,yv:()=>m});var a=t(95155),r=t(12115),l=t(31992),n=t(79556),i=t(77381),c=t(10518),d=t(59434);let o=l.bL;l.YJ;let m=l.WT,x=r.forwardRef((e,s)=>{let{className:t,children:r,...i}=e;return(0,a.jsxs)(l.l9,{ref:s,className:(0,d.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",t),...i,children:[r,(0,a.jsx)(l.In,{asChild:!0,children:(0,a.jsx)(n.A,{className:"h-4 w-4 opacity-50"})})]})});x.displayName=l.l9.displayName;let u=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(l.PP,{ref:s,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",t),...r,children:(0,a.jsx)(i.A,{className:"h-4 w-4"})})});u.displayName=l.PP.displayName;let h=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(l.wn,{ref:s,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",t),...r,children:(0,a.jsx)(n.A,{className:"h-4 w-4"})})});h.displayName=l.wn.displayName;let f=r.forwardRef((e,s)=>{let{className:t,children:r,position:n="popper",...i}=e;return(0,a.jsx)(l.ZL,{children:(0,a.jsxs)(l.UC,{ref:s,className:(0,d.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===n&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t),position:n,...i,children:[(0,a.jsx)(u,{}),(0,a.jsx)(l.LM,{className:(0,d.cn)("p-1","popper"===n&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:r}),(0,a.jsx)(h,{})]})})});f.displayName=l.UC.displayName,r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(l.JU,{ref:s,className:(0,d.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",t),...r})}).displayName=l.JU.displayName;let p=r.forwardRef((e,s)=>{let{className:t,children:r,...n}=e;return(0,a.jsxs)(l.q7,{ref:s,className:(0,d.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",t),...n,children:[(0,a.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,a.jsx)(l.VF,{children:(0,a.jsx)(c.A,{className:"h-4 w-4"})})}),(0,a.jsx)(l.p4,{children:r})]})});p.displayName=l.q7.displayName,r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(l.wv,{ref:s,className:(0,d.cn)("-mx-1 my-1 h-px bg-muted",t),...r})}).displayName=l.wv.displayName},66424:(e,s,t)=>{"use strict";t.d(s,{F:()=>i});var a=t(95155),r=t(12115),l=t(47655),n=t(59434);let i=r.forwardRef((e,s)=>{let{className:t,children:r,...i}=e;return(0,a.jsxs)(l.bL,{ref:s,className:(0,n.cn)("relative overflow-hidden",t),...i,children:[(0,a.jsx)(l.LM,{className:"h-full w-full rounded-[inherit]",children:r}),(0,a.jsx)(c,{}),(0,a.jsx)(l.OK,{})]})});i.displayName=l.bL.displayName;let c=r.forwardRef((e,s)=>{let{className:t,orientation:r="vertical",...i}=e;return(0,a.jsx)(l.VM,{ref:s,orientation:r,className:(0,n.cn)("flex touch-none select-none transition-colors","vertical"===r&&"h-full w-2.5 border-l border-l-transparent p-[1px]","horizontal"===r&&"h-2.5 flex-col border-t border-t-transparent p-[1px]",t),...i,children:(0,a.jsx)(l.lr,{className:"relative flex-1 rounded-full bg-border"})})});c.displayName=l.VM.displayName},66695:(e,s,t)=>{"use strict";t.d(s,{BT:()=>d,Wu:()=>o,ZB:()=>c,Zp:()=>n,aR:()=>i,wL:()=>m});var a=t(95155),r=t(12115),l=t(59434);let n=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:(0,l.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",t),...r})});n.displayName="Card";let i=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:(0,l.cn)("flex flex-col space-y-1.5 p-6",t),...r})});i.displayName="CardHeader";let c=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:(0,l.cn)("text-2xl font-semibold leading-none tracking-tight",t),...r})});c.displayName="CardTitle";let d=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:(0,l.cn)("text-sm text-muted-foreground",t),...r})});d.displayName="CardDescription";let o=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:(0,l.cn)("p-6 pt-0",t),...r})});o.displayName="CardContent";let m=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:(0,l.cn)("flex items-center p-6 pt-0",t),...r})});m.displayName="CardFooter"},68856:(e,s,t)=>{"use strict";t.d(s,{E:()=>l});var a=t(95155),r=t(59434);function l(e){let{className:s,...t}=e;return(0,a.jsx)("div",{className:(0,r.cn)("animate-pulse rounded-md bg-muted",s),...t})}},73616:(e,s,t)=>{Promise.resolve().then(t.bind(t,33066))},95647:(e,s,t)=>{"use strict";t.d(s,{z:()=>r});var a=t(95155);function r(e){let{title:s,description:t,icon:r,children:l}=e;return(0,a.jsxs)("div",{className:"mb-6 pb-4 border-b border-border/50 flex justify-between items-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[r&&(0,a.jsx)(r,{className:"h-8 w-8 text-primary"}),(0,a.jsx)("h1",{className:"text-3xl font-bold tracking-tight text-foreground",children:s})]}),t&&(0,a.jsx)("p",{className:"text-muted-foreground mt-1",children:t})]}),l&&(0,a.jsx)("div",{className:"flex items-center gap-2",children:l})]})}t(12115)}},e=>{var s=s=>e(e.s=s);e.O(0,[3796,6113,2688,2512,1859,3229,5433,8162,8441,1684,7358],()=>s(73616)),_N_E=e.O()}]);