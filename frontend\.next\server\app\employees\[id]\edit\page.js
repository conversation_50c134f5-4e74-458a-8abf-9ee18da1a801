(()=>{var e={};e.id=6359,e.ids=[6359],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},23029:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>c,pages:()=>p,routeModule:()=>u,tree:()=>l});var s=r(65239),i=r(48088),a=r(88170),o=r.n(a),n=r(30893),d={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);r.d(t,d);let l={children:["",{children:["employees",{children:["[id]",{children:["edit",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,41510)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\employees\\[id]\\edit\\page.tsx"]}]},{}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,p=["C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\employees\\[id]\\edit\\page.tsx"],c={require:r,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/employees/[id]/edit/page",pathname:"/employees/[id]/edit",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},41510:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\projects\\\\CarServiceTrackingSystem\\\\TrackerSystem\\\\frontend\\\\src\\\\app\\\\employees\\\\[id]\\\\edit\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\employees\\[id]\\edit\\page.tsx","default")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},72332:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>u});var s=r(60687),i=r(43210),a=r(16189),o=r(37716),n=r(28840),d=r(48041);let l=(0,r(82614).A)("UserCog",[["circle",{cx:"18",cy:"15",r:"3",key:"gjjjvw"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M10 15H6a4 4 0 0 0-4 4v2",key:"1nfge6"}],["path",{d:"m21.7 16.4-.9-.3",key:"12j9ji"}],["path",{d:"m15.2 13.9-.9-.3",key:"1fdjdi"}],["path",{d:"m16.6 18.7.3-.9",key:"heedtr"}],["path",{d:"m19.1 12.2.3-.9",key:"1af3ki"}],["path",{d:"m19.6 18.7-.4-1",key:"1x9vze"}],["path",{d:"m16.8 12.3-.4-1",key:"vqeiwj"}],["path",{d:"m14.3 16.6 1-.4",key:"1qlj63"}],["path",{d:"m20.7 13.8 1-.4",key:"1v5t8k"}]]);var p=r(29867),c=r(85726);function u(){let e=(0,a.useRouter)(),t=(0,a.useParams)(),{toast:r}=(0,p.dj)(),[u,m]=(0,i.useState)(null),[y,h]=(0,i.useState)(!0),[f,v]=(0,i.useState)(!1),x=t.id,g=async t=>{if(!x)return void r({title:"Error",description:"Invalid employee ID.",variant:"destructive"});v(!0);try{console.log(`Updating employee ID: ${x}`,t);let s={...t,statusChangeReason:t.statusChangeReason||void 0};await (0,n.updateEmployee)(Number(x),s),r({title:"Employee Updated Successfully",description:`${t.fullName||t.name} has been updated with the latest information.`,variant:"default"}),e.push(`/employees/${x}`)}catch(t){console.error("Failed to update employee:",t);let e="Failed to update employee. Please try again.";if(t.message?.includes("Network error"))e="Network error. Please check your connection and try again.";else if(t.message?.includes("404"))e="Employee not found. They may have been deleted.";else if(t.message?.includes("403"))e="You do not have permission to edit this employee.";else if(t.message?.includes("Validation failed"))e="Please check your input data. Some fields may be invalid.";else if(t.validationErrors){let r=t.validationErrors.map(e=>`${e.path}: ${e.message}`).join(", ");e=`Validation errors: ${r}`}else t.response?.data?.error?e=t.response.data.error:t.message&&(e=t.message);r({title:"Update Failed",description:e,variant:"destructive"})}finally{v(!1)}};return y?(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsx)(d.z,{title:"Loading...",icon:l}),(0,s.jsx)(c.E,{className:"h-[700px] w-full rounded-lg bg-card"})]}):u?(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsx)(d.z,{title:`Edit Employee: ${u.fullName}`,description:"Modify the details for this employee.",icon:l}),(0,s.jsx)(o.A,{onSubmit:g,initialData:u,isEditing:!0,isLoading:f})]}):(0,s.jsx)("p",{children:"Employee not found."})}},74075:e=>{"use strict";e.exports=require("zlib")},76653:(e,t,r)=>{Promise.resolve().then(r.bind(r,72332))},78861:(e,t,r)=>{Promise.resolve().then(r.bind(r,41510))},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},85726:(e,t,r)=>{"use strict";r.d(t,{E:()=>a});var s=r(60687),i=r(4780);function a({className:e,...t}){return(0,s.jsx)("div",{className:(0,i.cn)("animate-pulse rounded-md bg-muted",e),...t})}},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,3622,1658,5880,2729,3442,8141,3983,3860],()=>r(23029));module.exports=s})();