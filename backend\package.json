{"name": "backend", "version": "1.0.0", "main": "dist/server.js", "directories": {"test": "tests"}, "scripts": {"build": "tsc", "start": "node dist/server.js", "start:prod": "node dist/server.js", "dev": "node --loader ts-node/esm src/server.ts", "test": "node --experimental-vm-modules node_modules/jest/bin/jest.js --runInBand", "lint": "eslint --ext .ts .", "migrate:dev": "prisma migrate dev", "migrate:deploy": "prisma migrate deploy", "migrate:status": "prisma migrate status", "db:push": "prisma db push", "db:seed": "node --loader ts-node/esm prisma/seed.ts", "generate": "prisma generate", "studio": "prisma studio", "docker:build": "docker build -t workhub-backend .", "docker:run": "docker run -p 3001:3001 workhub-backend", "db:local": "node scripts/switch-database.js local", "db:supabase": "node scripts/switch-database.js supabase", "db:status": "node -e \"const fs=require('fs');const env=fs.readFileSync('.env','utf8');const useSupabase=/USE_SUPABASE=true/.test(env);console.log('Current database:',useSupabase?'Supabase':'Local PostgreSQL');\"", "db:migrate": "prisma migrate deploy", "db:seed:force": "node --loader ts-node/esm prisma/seed.ts", "supabase:login": "node scripts/supabase-cli.js login", "supabase:link": "node scripts/supabase-cli.js link", "supabase:pull": "node scripts/supabase-cli.js db:pull", "supabase:push": "node scripts/supabase-cli.js db:push", "supabase:migrate": "node scripts/supabase-cli.js db:migrate", "supabase:seed": "node scripts/supabase-cli.js db:seed", "supabase:config": "node scripts/supabase-cli.js config"}, "prisma": {"seed": "node --loader ts-node/esm prisma/seed.ts"}, "keywords": [], "author": "", "license": "ISC", "type": "module", "description": "", "dependencies": {"@prisma/client": "^6.7.0", "@supabase/supabase-js": "^2.49.4", "@types/cors": "^2.8.18", "axios": "^1.6.7", "cors": "^2.8.5", "dotenv": "^16.5.0", "ejs": "^3.1.9", "esm": "^3.2.25", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "node-fetch": "^3.3.2", "pg": "^8.15.6", "puppeteer": "^22.4.0", "socket.io": "^4.8.1", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "winston": "^3.17.0", "zod": "^3.24.4"}, "devDependencies": {"@types/ejs": "^3.1.5", "@types/express": "^5.0.1", "@types/jest": "^29.5.14", "@types/morgan": "^1.9.9", "@types/node": "^22.15.17", "@types/pg": "^8.15.1", "@types/supertest": "^6.0.3", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.8", "jest": "^29.7.0", "prisma": "^6.7.0", "supabase": "^2.22.12", "supertest": "^7.1.0", "ts-jest": "^29.3.2", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}