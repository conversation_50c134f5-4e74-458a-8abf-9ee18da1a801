(()=>{var e={};e.id=1011,e.ids=[1011],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5149:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>l,tree:()=>p});var s=r(65239),a=r(48088),i=r(88170),n=r.n(i),o=r(30893),d={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>o[e]);r.d(t,d);let p={children:["",{children:["tasks",{children:["add",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,15593)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\tasks\\add\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\tasks\\add\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},l=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/tasks/add/page",pathname:"/tasks/add",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:p}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},15593:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\projects\\\\CarServiceTrackingSystem\\\\TrackerSystem\\\\frontend\\\\src\\\\app\\\\tasks\\\\add\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\tasks\\add\\page.tsx","default")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},36064:(e,t,r)=>{Promise.resolve().then(r.bind(r,88551))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},72512:(e,t,r)=>{Promise.resolve().then(r.bind(r,15593))},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},88551:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c});var s=r(60687),a=r(91188),i=r(28840),n=r(16189),o=r(48041);let d=(0,r(82614).A)("ListPlus",[["path",{d:"M11 12H3",key:"51ecnj"}],["path",{d:"M16 6H3",key:"1wxfjs"}],["path",{d:"M16 18H3",key:"12xzn7"}],["path",{d:"M18 9v6",key:"1twb98"}],["path",{d:"M21 12h-6",key:"bt1uis"}]]);var p=r(29867);function c(){let e=(0,n.useRouter)(),{toast:t}=(0,p.dj)(),r=async r=>{try{await (0,i.addTask)(r),t({title:"Task Added",description:`The task "${r.description.substring(0,30)}..." has been successfully created.`,variant:"default"}),e.push("/tasks")}catch(e){console.error("Error adding task:",e),t({title:"Error",description:"Failed to add task. Please try again.",variant:"destructive"})}};return(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsx)(o.z,{title:"Add New Task",description:"Enter the details for the new task or job.",icon:d}),(0,s.jsx)(a.A,{onSubmit:r,isEditing:!1})]})}},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,3622,1658,5880,2729,3442,8141,3983,3348],()=>r(5149));module.exports=s})();