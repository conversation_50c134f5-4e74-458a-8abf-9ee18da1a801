(()=>{var e={};e.id=7530,e.ids=[7530],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8760:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(82614).A)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},15795:(e,r,s)=>{"use strict";function t(e){switch(e){case"In_Progress":return"In Progress";case"No_details":return"No Details";default:return e}}s.d(r,{fZ:()=>t})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26398:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(82614).A)("MapPin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},35265:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(82614).A)("CirclePlus",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M8 12h8",key:"1wcyev"}],["path",{d:"M12 8v8",key:"napkw2"}]])},35950:(e,r,s)=>{"use strict";s.d(r,{w:()=>n});var t=s(60687),a=s(43210),l=s(62369),i=s(4780);let n=a.forwardRef(({className:e,orientation:r="horizontal",decorative:s=!0,...a},n)=>(0,t.jsx)(l.b,{ref:n,decorative:s,orientation:r,className:(0,i.cn)("shrink-0 bg-border","horizontal"===r?"h-[1px] w-full":"h-full w-[1px]",e),...a}));n.displayName=l.b.displayName},36644:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(82614).A)("FileText",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},41936:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(82614).A)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},42364:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>R});var t=s(60687),a=s(43210),l=s(85814),i=s.n(l),n=s(16189),d=s(68752),c=s(33886),o=s(35265),m=s(41936),h=s(48041),x=s(89667),u=s(28840);function p({searchTerm:e="",children:r}){(0,n.useRouter)();let[s,l]=(0,a.useState)([]),[i,d]=(0,a.useState)([]),[c,o]=(0,a.useState)(!0),[m,h]=(0,a.useState)(null),[x,p]=(0,a.useState)(0),g=(0,a.useCallback)(async()=>{o(!0),h(null);try{let e=await (0,u.getDelegations)();e.sort((e,r)=>new Date(r.durationFrom).getTime()-new Date(e.durationFrom).getTime()),l(e)}catch(e){console.error("Error fetching delegations:",e),h("Failed to load delegations. Please try again later.")}finally{o(!1)}},[]);return(0,t.jsx)(t.Fragment,{children:r({delegations:i,loading:c,error:m,fetchDelegations:()=>(p(e=>e+1),g())})})}var g=s(52027),f=s(30474),y=s(44493),j=s(26398),v=s(92876),N=s(48206),b=s(52856),k=s(99196),w=s(8760),A=s(96834),M=s(35950),C=s(4780),E=s(76869),S=s(15795);let q=e=>{switch(e){case"Planned":return"bg-blue-500/20 text-blue-700 border-blue-500/30 dark:text-blue-400 dark:bg-blue-500/10 dark:border-blue-500/20";case"Confirmed":return"bg-green-500/20 text-green-700 border-green-500/30 dark:text-green-400 dark:bg-green-500/10 dark:border-green-500/20";case"In_Progress":return"bg-yellow-500/20 text-yellow-700 border-yellow-500/30 dark:text-yellow-400 dark:bg-yellow-500/10 dark:border-yellow-500/20";case"Completed":return"bg-purple-500/20 text-purple-700 border-purple-500/30 dark:text-purple-400 dark:bg-purple-500/10 dark:border-purple-500/20";case"Cancelled":return"bg-red-500/20 text-red-700 border-red-500/30 dark:text-red-400 dark:bg-red-500/10 dark:border-red-500/20";default:return"bg-gray-500/20 text-gray-700 border-gray-500/30 dark:text-gray-400 dark:bg-gray-500/10 dark:border-gray-500/20"}};function T({delegation:e}){let r=e=>(0,E.GP)(new Date(e),"MMM d, yyyy");return(0,t.jsxs)(y.Zp,{className:"overflow-hidden shadow-md flex flex-col h-full bg-card border-border/60",children:[(0,t.jsxs)(y.aR,{className:"p-0 relative",children:[(0,t.jsx)("div",{className:"aspect-[16/10] w-full relative",children:(0,t.jsx)(f.default,{src:e.imageUrl||`https://picsum.photos/seed/${e.id}/400/250`,alt:e.eventName,layout:"fill",objectFit:"cover",className:"bg-muted","data-ai-hint":"conference meeting",priority:!0})}),(0,t.jsx)(A.E,{className:(0,C.cn)("absolute top-2 right-2 text-xs py-1 px-2 font-semibold",q(e.status)),children:(0,S.fZ)(e.status)})]}),(0,t.jsxs)(y.Wu,{className:"p-5 flex-grow flex flex-col",children:[(0,t.jsx)(y.ZB,{className:"text-xl font-semibold mb-1 text-primary",children:e.eventName}),(0,t.jsxs)(y.BT,{className:"text-sm text-muted-foreground mb-3 flex items-center",children:[(0,t.jsx)(j.A,{className:"h-4 w-4 mr-1.5 flex-shrink-0 text-accent"}),e.location]}),(0,t.jsx)(M.w,{className:"my-3 bg-border/50"}),(0,t.jsxs)("div",{className:"space-y-2.5 text-sm text-foreground flex-grow",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(v.A,{className:"mr-2.5 h-4 w-4 text-accent flex-shrink-0"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-muted-foreground",children:"Duration: "}),(0,t.jsxs)("strong",{className:"font-semibold",children:[r(e.durationFrom)," -"," ",r(e.durationTo)]})]})]}),(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(N.A,{className:"mr-2.5 h-4 w-4 text-accent flex-shrink-0"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-muted-foreground",children:"Delegates: "}),(0,t.jsx)("strong",{className:"font-semibold",children:e.delegates.length})]})]}),(e.flightArrivalDetails||e.flightDepartureDetails)&&(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(b.A,{className:"mr-2.5 h-4 w-4 text-accent flex-shrink-0"}),(0,t.jsx)("span",{className:"font-semibold text-muted-foreground",children:"Flight details logged"})]})]}),e.notes&&(0,t.jsxs)("p",{className:"mt-3 text-xs text-muted-foreground line-clamp-2 pt-2 border-t border-dashed border-border/50",title:e.notes,children:[(0,t.jsx)(k.A,{size:12,className:"inline mr-1 text-accent"}),e.notes]})]}),(0,t.jsx)(y.wL,{className:"p-4 border-t border-border/60 bg-muted/20",children:(0,t.jsx)(d.r,{actionType:"tertiary",className:"w-full",icon:(0,t.jsx)(w.A,{className:"h-4 w-4"}),asChild:!0,children:(0,t.jsx)(i(),{href:`/delegations/${e.id}`,children:"View Details"})})})]})}var P=s(95758),D=s(69981);function R(){(0,n.useRouter)();let[e,r]=(0,a.useState)("");return(0,t.jsx)(P.A,{children:(0,t.jsxs)("div",{className:"space-y-8",children:[(0,t.jsx)(h.z,{title:"Manage Delegations",description:"Track and manage all your events, trips, and delegate information.",icon:c.A,children:(0,t.jsxs)("div",{className:"flex gap-2 items-center",children:[(0,t.jsx)(d.r,{actionType:"primary",icon:(0,t.jsx)(o.A,{className:"mr-2 h-4 w-4"}),asChild:!0,children:(0,t.jsx)(i(),{href:"/delegations/add",children:"Add New Delegation"})}),(0,t.jsx)(D.M,{getReportUrl:()=>{let r=new URLSearchParams({searchTerm:e}).toString();return`/delegations/report/list?${r}`},isList:!0})]})}),(0,t.jsx)("div",{className:"mb-6 p-4 bg-card rounded-lg shadow-md",children:(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(m.A,{className:"absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-muted-foreground"}),(0,t.jsx)(x.p,{type:"text",placeholder:"Search delegations (Event, Location, Delegate, Status...)",value:e,onChange:e=>r(e.target.value),className:"pl-10 w-full"})]})}),(0,t.jsx)(p,{searchTerm:e,children:({delegations:r,loading:s,error:a,fetchDelegations:l})=>(0,t.jsx)(g.gO,{isLoading:s,error:a,data:r,onRetry:l,loadingComponent:(0,t.jsx)(g.jt,{variant:"card",count:3}),emptyComponent:(0,t.jsxs)("div",{className:"text-center py-12 bg-card rounded-lg shadow-md",children:[(0,t.jsx)(c.A,{className:"mx-auto h-16 w-16 text-muted-foreground mb-6"}),(0,t.jsx)("h3",{className:"text-2xl font-semibold text-foreground mb-2",children:e?"No Delegations Match Your Search":"No Delegations Yet!"}),(0,t.jsx)("p",{className:"text-muted-foreground mt-2 mb-6 max-w-md mx-auto",children:e?"Try adjusting your search terms or add a new delegation.":"It looks like you haven't added any delegations yet. Get started by adding one."}),!e&&(0,t.jsx)(d.r,{actionType:"primary",size:"lg",icon:(0,t.jsx)(o.A,{className:"h-4 w-4"}),asChild:!0,children:(0,t.jsx)(i(),{href:"/delegations/add",children:"Add Your First Delegation"})})]}),children:e=>(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:e.map(e=>(0,t.jsx)(T,{delegation:e},e.id))})})})]})})}},48041:(e,r,s)=>{"use strict";s.d(r,{z:()=>a});var t=s(60687);function a({title:e,description:r,icon:s,children:a}){return(0,t.jsxs)("div",{className:"mb-6 pb-4 border-b border-border/50 flex justify-between items-center",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[s&&(0,t.jsx)(s,{className:"h-8 w-8 text-primary"}),(0,t.jsx)("h1",{className:"text-3xl font-bold tracking-tight text-foreground",children:e})]}),r&&(0,t.jsx)("p",{className:"text-muted-foreground mt-1",children:r})]}),a&&(0,t.jsx)("div",{className:"flex items-center gap-2",children:a})]})}s(43210)},48206:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(82614).A)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},49712:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>t});let t=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\projects\\\\CarServiceTrackingSystem\\\\TrackerSystem\\\\frontend\\\\src\\\\app\\\\delegations\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\delegations\\page.tsx","default")},52027:(e,r,s)=>{"use strict";s.d(r,{gO:()=>p,jt:()=>x});var t=s(60687);s(43210);var a=s(11516),l=s(72963),i=s(4780),n=s(85726),d=s(91821),c=s(68752);let o={sm:"h-4 w-4",md:"h-6 w-6",lg:"h-8 w-8",xl:"h-12 w-12"},m={sm:"text-xs",md:"text-sm",lg:"text-base",xl:"text-lg"};function h({size:e="md",className:r,text:s,fullPage:l=!1}){return(0,t.jsx)("div",{className:(0,i.cn)("flex items-center justify-center",l&&"fixed inset-0 bg-background/80 backdrop-blur-sm z-50",r),children:(0,t.jsxs)("div",{className:"flex flex-col items-center",children:[(0,t.jsx)(a.A,{className:(0,i.cn)("animate-spin text-primary",o[e])}),s&&(0,t.jsx)("span",{className:(0,i.cn)("mt-2 text-muted-foreground",m[e]),children:s})]})})}function x({variant:e="default",count:r=1,className:s,testId:a="loading-skeleton"}){return"card"===e?(0,t.jsx)("div",{className:(0,i.cn)("grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",s),"data-testid":a,children:Array(r).fill(0).map((e,r)=>(0,t.jsxs)("div",{className:"overflow-hidden shadow-md rounded-lg border bg-card",children:[(0,t.jsx)(n.E,{className:"aspect-[16/10] w-full"}),(0,t.jsxs)("div",{className:"p-5",children:[(0,t.jsx)(n.E,{className:"h-7 w-3/4 mb-1"}),(0,t.jsx)(n.E,{className:"h-4 w-1/2 mb-3"}),(0,t.jsx)(n.E,{className:"h-px w-full my-3"}),(0,t.jsx)("div",{className:"space-y-2.5",children:[,,,].fill(0).map((e,r)=>(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(n.E,{className:"mr-2.5 h-5 w-5 rounded-full"}),(0,t.jsx)(n.E,{className:"h-5 w-2/3"})]},r))})]})]},r))}):"table"===e?(0,t.jsxs)("div",{className:(0,i.cn)("space-y-3",s),"data-testid":a,children:[(0,t.jsx)("div",{className:"flex gap-4",children:[,,,].fill(0).map((e,r)=>(0,t.jsx)(n.E,{className:"h-8 flex-1"},r))}),Array(r).fill(0).map((e,r)=>(0,t.jsx)("div",{className:"flex gap-4",children:[,,,].fill(0).map((e,r)=>(0,t.jsx)(n.E,{className:"h-6 flex-1"},r))},r))]}):"list"===e?(0,t.jsx)("div",{className:(0,i.cn)("space-y-3",s),"data-testid":a,children:Array(r).fill(0).map((e,r)=>(0,t.jsxs)("div",{className:"flex items-center gap-4",children:[(0,t.jsx)(n.E,{className:"h-12 w-12 rounded-full"}),(0,t.jsxs)("div",{className:"space-y-2 flex-1",children:[(0,t.jsx)(n.E,{className:"h-4 w-1/3"}),(0,t.jsx)(n.E,{className:"h-4 w-full"})]})]},r))}):"stats"===e?(0,t.jsx)("div",{className:(0,i.cn)("grid gap-4 md:grid-cols-2 lg:grid-cols-3",s),"data-testid":a,children:Array(r).fill(0).map((e,r)=>(0,t.jsxs)("div",{className:"rounded-lg border bg-card p-5 shadow-sm",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)(n.E,{className:"h-5 w-1/3"}),(0,t.jsx)(n.E,{className:"h-5 w-5 rounded-full"})]}),(0,t.jsx)(n.E,{className:"h-8 w-1/2 mt-3"}),(0,t.jsx)(n.E,{className:"h-4 w-2/3 mt-2"})]},r))}):(0,t.jsx)("div",{className:(0,i.cn)("space-y-2",s),"data-testid":a,children:Array(r).fill(0).map((e,r)=>(0,t.jsx)(n.E,{className:"w-full h-5"},r))})}function u({message:e,onRetry:r,className:s}){return(0,t.jsxs)(d.Fc,{variant:"destructive",className:(0,i.cn)("my-4",s),children:[(0,t.jsx)(l.A,{className:"h-4 w-4"}),(0,t.jsx)(d.XL,{children:"Error"}),(0,t.jsx)(d.TN,{children:(0,t.jsxs)("div",{className:"mt-2",children:[(0,t.jsx)("p",{className:"text-sm text-muted-foreground mb-4",children:e}),r&&(0,t.jsx)(c.r,{actionType:"tertiary",size:"sm",onClick:r,icon:(0,t.jsx)(a.A,{className:"h-4 w-4"}),children:"Try Again"})]})})]})}function p({isLoading:e,error:r,data:s,onRetry:a,children:l,loadingComponent:n,errorComponent:d,emptyComponent:c,className:o}){return e?n||(0,t.jsx)(h,{className:o,text:"Loading..."}):r?d||(0,t.jsx)(u,{message:r,onRetry:a,className:o}):!s||Array.isArray(s)&&0===s.length?c||(0,t.jsx)("div",{className:(0,i.cn)("text-center py-8",o),children:(0,t.jsx)("p",{className:"text-muted-foreground",children:"No data available"})}):(0,t.jsx)("div",{className:o,children:l(s)})}},52856:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(82614).A)("Plane",[["path",{d:"M17.8 19.2 16 11l3.5-3.5C21 6 21.5 4 21 3c-1-.5-3 0-4.5 1.5L13 8 4.8 6.2c-.5-.1-.9.1-1.1.5l-.3.5c-.2.5-.1 1 .3 1.3L9 12l-2 3H4l-1 1 3 2 2 3 1-1v-3l3-2 3.5 5.3c.3.4.8.5 1.3.3l.5-.2c.4-.3.6-.7.5-1.2z",key:"1v9wt8"}]])},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},59844:(e,r,s)=>{Promise.resolve().then(s.bind(s,42364))},60368:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(82614).A)("ExternalLink",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])},62369:(e,r,s)=>{"use strict";s.d(r,{b:()=>c});var t=s(43210),a=s(14163),l=s(60687),i="horizontal",n=["horizontal","vertical"],d=t.forwardRef((e,r)=>{var s;let{decorative:t,orientation:d=i,...c}=e,o=(s=d,n.includes(s))?d:i;return(0,l.jsx)(a.sG.div,{"data-orientation":o,...t?{role:"none"}:{"aria-orientation":"vertical"===o?o:void 0,role:"separator"},...c,ref:r})});d.displayName="Separator";var c=d},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67549:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>o,routeModule:()=>h,tree:()=>c});var t=s(65239),a=s(48088),l=s(88170),i=s.n(l),n=s(30893),d={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);s.d(r,d);let c={children:["",{children:["delegations",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,49712)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\delegations\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\delegations\\page.tsx"],m={require:s,loadChunk:()=>Promise.resolve()},h=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/delegations/page",pathname:"/delegations",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},68752:(e,r,s)=>{"use strict";s.d(r,{r:()=>c});var t=s(60687),a=s(43210),l=s.n(a),i=s(29523),n=s(11516),d=s(4780);let c=l().forwardRef(({actionType:e="primary",icon:r,isLoading:s=!1,loadingText:a,className:l,children:c,disabled:o,asChild:m=!1,...h},x)=>{let{variant:u,className:p}={primary:{variant:"default",className:"shadow-md"},secondary:{variant:"secondary",className:""},tertiary:{variant:"outline",className:""},danger:{variant:"destructive",className:"shadow-md"}}[e];return(0,t.jsx)(i.$,{ref:x,variant:u,className:(0,d.cn)(p,l),disabled:s||o,asChild:m,...h,children:s?(0,t.jsxs)("span",{className:"inline-flex items-center",children:[" ",(0,t.jsx)(n.A,{className:"mr-2 h-4 w-4 animate-spin"}),a||c]}):(0,t.jsxs)("span",{className:"inline-flex items-center",children:[" ",r&&(0,t.jsx)("span",{className:"mr-2",children:r}),c]})})});c.displayName="ActionButton"},69981:(e,r,s)=>{"use strict";s.d(r,{M:()=>c});var t=s(60687);s(43210);var a=s(85814),l=s.n(a),i=s(36644),n=s(60368),d=s(68752);function c({href:e,getReportUrl:r,isList:s=!1,className:a}){if(!e&&!r)return console.error("ViewReportButton requires either href or getReportUrl prop"),null;let c=s?"View List Report":"View Report";return e?(0,t.jsx)(d.r,{actionType:"secondary",asChild:!0,icon:(0,t.jsx)(i.A,{className:"h-4 w-4"}),className:a,children:(0,t.jsxs)(l(),{href:e,target:"_blank",rel:"noopener noreferrer",children:[c,(0,t.jsx)(n.A,{className:"h-3 w-3 ml-1.5 inline-block","aria-hidden":"true"}),(0,t.jsx)("span",{className:"sr-only",children:"(opens in new tab)"})]})}):(0,t.jsxs)(d.r,{actionType:"secondary",onClick:()=>{if(r){let e=r();window.open(e,"_blank","noopener,noreferrer")}},icon:(0,t.jsx)(i.A,{className:"h-4 w-4"}),className:a,children:[c,(0,t.jsx)(n.A,{className:"h-3 w-3 ml-1.5 inline-block","aria-hidden":"true"})]})}},74075:e=>{"use strict";e.exports=require("zlib")},77368:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(82614).A)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},78404:(e,r,s)=>{Promise.resolve().then(s.bind(s,49712))},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},85726:(e,r,s)=>{"use strict";s.d(r,{E:()=>l});var t=s(60687),a=s(4780);function l({className:e,...r}){return(0,t.jsx)("div",{className:(0,a.cn)("animate-pulse rounded-md bg-muted",e),...r})}},91645:e=>{"use strict";e.exports=require("net")},92876:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(82614).A)("CalendarDays",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}],["path",{d:"M8 14h.01",key:"6423bh"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 18h.01",key:"lrp35t"}],["path",{d:"M12 18h.01",key:"mhygvu"}],["path",{d:"M16 18h.01",key:"kzsmim"}]])},94735:e=>{"use strict";e.exports=require("events")},95758:(e,r,s)=>{"use strict";s.d(r,{A:()=>c});var t=s(60687),a=s(43210),l=s(91821),i=s(29523),n=s(77368);class d extends a.Component{constructor(e){super(e),this.handleRetry=()=>{this.setState({hasError:!1,error:null,errorInfo:null})},this.state={hasError:!1,error:null,errorInfo:null}}static getDerivedStateFromError(e){return{hasError:!0,error:e,errorInfo:null}}componentDidCatch(e,r){console.error("Error caught by ErrorBoundary:",e),console.error("Component stack:",r.componentStack),this.setState({errorInfo:r})}render(){return this.state.hasError?this.props.fallback?this.props.fallback:(0,t.jsxs)(l.Fc,{variant:"destructive",className:"my-4",children:[(0,t.jsx)(l.XL,{className:"text-lg font-semibold",children:"Something went wrong"}),(0,t.jsxs)(l.TN,{className:"mt-2",children:[(0,t.jsx)("p",{className:"mb-2",children:this.state.error?.message||"An unexpected error occurred"}),this.state.error?.stack&&(0,t.jsxs)("details",{className:"mt-2 text-xs",children:[(0,t.jsx)("summary",{children:"Error details"}),(0,t.jsx)("pre",{className:"mt-2 whitespace-pre-wrap overflow-auto max-h-[200px] p-2 bg-slate-100 dark:bg-slate-900 rounded",children:this.state.error.stack})]}),(0,t.jsxs)(i.$,{variant:"outline",size:"sm",onClick:this.handleRetry,className:"mt-4",children:[(0,t.jsx)(n.A,{className:"mr-2 h-4 w-4"}),"Try Again"]})]})]}):this.props.children}}let c=d},99196:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(82614).A)("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]])}};var r=require("../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[4447,3622,1658,5880,474,8141,3983],()=>s(67549));module.exports=t})();