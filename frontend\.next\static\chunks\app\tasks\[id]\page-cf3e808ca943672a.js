(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6043],{22346:(e,s,a)=>{"use strict";a.d(s,{w:()=>i});var t=a(95155),r=a(12115),l=a(87489),d=a(59434);let i=r.forwardRef((e,s)=>{let{className:a,orientation:r="horizontal",decorative:i=!0,...n}=e;return(0,t.jsx)(l.b,{ref:s,decorative:i,orientation:r,className:(0,d.cn)("shrink-0 bg-border","horizontal"===r?"h-[1px] w-full":"h-full w-[1px]",a),...n})});i.displayName=l.b.displayName},59409:(e,s,a)=>{"use strict";a.d(s,{bq:()=>x,eb:()=>g,gC:()=>p,l6:()=>o,yv:()=>m});var t=a(95155),r=a(12115),l=a(31992),d=a(79556),i=a(77381),n=a(10518),c=a(59434);let o=l.bL;l.YJ;let m=l.WT,x=r.forwardRef((e,s)=>{let{className:a,children:r,...i}=e;return(0,t.jsxs)(l.l9,{ref:s,className:(0,c.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",a),...i,children:[r,(0,t.jsx)(l.In,{asChild:!0,children:(0,t.jsx)(d.A,{className:"h-4 w-4 opacity-50"})})]})});x.displayName=l.l9.displayName;let u=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)(l.PP,{ref:s,className:(0,c.cn)("flex cursor-default items-center justify-center py-1",a),...r,children:(0,t.jsx)(i.A,{className:"h-4 w-4"})})});u.displayName=l.PP.displayName;let h=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)(l.wn,{ref:s,className:(0,c.cn)("flex cursor-default items-center justify-center py-1",a),...r,children:(0,t.jsx)(d.A,{className:"h-4 w-4"})})});h.displayName=l.wn.displayName;let p=r.forwardRef((e,s)=>{let{className:a,children:r,position:d="popper",...i}=e;return(0,t.jsx)(l.ZL,{children:(0,t.jsxs)(l.UC,{ref:s,className:(0,c.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===d&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",a),position:d,...i,children:[(0,t.jsx)(u,{}),(0,t.jsx)(l.LM,{className:(0,c.cn)("p-1","popper"===d&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:r}),(0,t.jsx)(h,{})]})})});p.displayName=l.UC.displayName,r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)(l.JU,{ref:s,className:(0,c.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",a),...r})}).displayName=l.JU.displayName;let g=r.forwardRef((e,s)=>{let{className:a,children:r,...d}=e;return(0,t.jsxs)(l.q7,{ref:s,className:(0,c.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",a),...d,children:[(0,t.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,t.jsx)(l.VF,{children:(0,t.jsx)(n.A,{className:"h-4 w-4"})})}),(0,t.jsx)(l.p4,{children:r})]})});g.displayName=l.q7.displayName,r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)(l.wv,{ref:s,className:(0,c.cn)("-mx-1 my-1 h-px bg-muted",a),...r})}).displayName=l.wv.displayName},78297:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>V});var t=a(95155),r=a(12115),l=a(35695),d=a(6874),i=a.n(d),n=a(66695),c=a(22346),o=a(91394),m=a(30285),x=a(50286),u=a(40320),h=a(35079),p=a(31949),g=a(12543),f=a(18763),j=a(77223),N=a(83662),y=a(98328),b=a(37648),v=a(3235),k=a(28328),w=a(61840),A=a(8376),T=a(13896),C=a(24371),E=a(2730),S=a(95647),R=a(87481),P=a(26126),q=a(59434),z=a(73168),F=a(83343),L=a(90010),U=a(59409),_=a(85057),B=a(77023);let D=e=>{switch(e){case"Pending":return"bg-yellow-100 text-yellow-800 border-yellow-200 dark:bg-yellow-900/20 dark:text-yellow-400 dark:border-yellow-800/30";case"Assigned":return"bg-blue-100 text-blue-800 border-blue-200 dark:bg-blue-900/20 dark:text-blue-400 dark:border-blue-800/30";case"In Progress":return"bg-indigo-100 text-indigo-800 border-indigo-200 dark:bg-indigo-900/20 dark:text-indigo-400 dark:border-indigo-800/30";case"Completed":return"bg-green-100 text-green-800 border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800/30";case"Cancelled":return"bg-red-100 text-red-800 border-red-200 dark:bg-red-900/20 dark:text-red-400 dark:border-red-800/30";default:return"bg-gray-100 text-gray-800 border-gray-200 dark:bg-gray-900/20 dark:text-gray-400 dark:border-gray-800/30"}},M=e=>{switch(e){case"Low":return"bg-green-100 text-green-800 border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800/30";case"Medium":return"bg-yellow-100 text-yellow-800 border-yellow-200 dark:bg-yellow-900/20 dark:text-yellow-400 dark:border-yellow-800/30";case"High":return"bg-red-100 text-red-800 border-red-200 dark:bg-red-900/20 dark:text-red-400 dark:border-red-800/30";default:return"bg-gray-100 text-gray-800 border-gray-200 dark:bg-gray-900/20 dark:text-gray-400 dark:border-gray-800/30"}},$=function(e){let s=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(!e)return"N/A";try{return(0,z.GP)((0,F.H)(e),s?"MMM d, yyyy, HH:mm":"MMM d, yyyy")}catch(e){return"Invalid Date"}};function I(e){let{icon:s,label:a,value:r,children:l,className:d}=e;return r||l?(0,t.jsxs)("div",{className:(0,q.cn)("flex items-start gap-3",d),children:[(0,t.jsx)(s,{className:"h-4 w-4 text-muted-foreground mt-1 shrink-0"}),(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:a}),r&&(0,t.jsx)("p",{className:"text-base font-medium text-foreground",children:r}),l]})]}):null}function H(e){let{assignedEmployees:s,onUnassign:a,isTaskCompleted:r=!1}=e;return 0===s.length?null:(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(x.A,{className:"h-4 w-4 text-muted-foreground"}),(0,t.jsx)("span",{className:"text-sm text-muted-foreground",children:"Assigned To"})]}),(0,t.jsx)("div",{className:"flex flex-wrap gap-3",children:s.map(e=>(0,t.jsxs)("div",{className:"flex items-center gap-2 p-2 rounded-lg border bg-muted/30",children:[(0,t.jsx)(o.eu,{className:"h-8 w-8",children:(0,t.jsx)(o.q5,{className:"text-xs font-medium",children:e.fullName.split(" ").map(e=>e[0]).join("").toUpperCase().slice(0,2)})}),(0,t.jsxs)("div",{className:"flex flex-col",children:[(0,t.jsx)(i(),{href:"/employees/".concat(e.id),className:"text-sm font-medium hover:text-primary",children:e.fullName}),(0,t.jsx)("span",{className:"text-xs text-muted-foreground capitalize",children:e.role.replace("_"," ")})]}),!r&&a&&(0,t.jsx)(m.$,{variant:"ghost",size:"sm",onClick:()=>a(e.id),className:"h-6 w-6 p-0 text-muted-foreground hover:text-destructive",children:(0,t.jsx)(u.A,{className:"h-3 w-3"})})]},e.id))})]})}function V(){let e=(0,l.useParams)(),s=(0,l.useRouter)(),{toast:a}=(0,R.dj)(),[d,z]=(0,r.useState)(null),[F,V]=(0,r.useState)([]),[Z,J]=(0,r.useState)(void 0),[O,W]=(0,r.useState)([]),[G,K]=(0,r.useState)(""),[Y,Q]=(0,r.useState)(!0),[X,ee]=(0,r.useState)(null),es=e.id,ea=(0,r.useCallback)(async()=>{if(es){Q(!0),ee(null);try{let e=await (0,E.getTaskById)(es);if(e){if(z(e),e.assignedTo&&e.assignedTo.length>0){let s=await Promise.all(e.assignedTo.map(e=>(0,E.getEmployeeById)(Number(e))));V(s.filter(e=>void 0!==e))}else V([]);if(e.vehicleId){let s=await (0,E.getVehicleById)(Number(e.vehicleId));J(s)}else J(null);let s=await (0,E.getEmployees)();W(s.filter(s=>{var a;return"Active"===s.status&&!(null==(a=e.assignedTo)?void 0:a.includes(String(s.id)))}))}else ee("Task not found."),a({title:"Error",description:"Task not found.",variant:"destructive"})}catch(s){console.error("Error fetching task data:",s);let e=s instanceof Error?s.message:"Failed to load task data.";ee(e),a({title:"Error",description:e,variant:"destructive"})}finally{Q(!1)}}},[es,a]);(0,r.useEffect)(()=>{ea()},[ea]);let et=async()=>{if(d)try{await (0,E.deleteTask)(d.id),a({title:"Task Deleted",description:'Task "'.concat(d.description.substring(0,30),'..." has been deleted.')}),s.push("/tasks")}catch(e){console.error("Error deleting task:",e),a({title:"Error",description:"Failed to delete task. Please try again.",variant:"destructive"})}},er=async()=>{if(d&&G)try{let e=await (0,E.assignTaskToEmployee)(d.id,G);e.task&&e.employee?(a({title:"Task Assigned",description:"Task assigned to employee ".concat(e.employee.fullName,".")}),await ea(),K("")):a({title:"Error",description:"Failed to assign task.",variant:"destructive"})}catch(e){console.error("Error assigning task:",e),a({title:"Error",description:"Failed to assign task. Please try again.",variant:"destructive"})}},el=async e=>{if(d)try{let s=await (0,E.unassignTaskFromEmployee)(d.id,e?String(e):void 0);(null==s?void 0:s.task)?(a({title:"Task Unassigned",description:e?"Employee unassigned from task.":"All employees unassigned from task."}),await ea()):a({title:"Error",description:"Failed to unassign task.",variant:"destructive"})}catch(e){console.error("Error unassigning task:",e),a({title:"Error",description:"Failed to unassign task. Please try again.",variant:"destructive"})}},ed=(null==d?void 0:d.status)==="Completed"||(null==d?void 0:d.status)==="Cancelled";return(0,t.jsx)("div",{className:"space-y-6",children:(0,t.jsx)(B.gO,{isLoading:Y,error:X,data:d,onRetry:ea,loadingComponent:(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)(S.z,{title:"Loading Task...",icon:h.A}),(0,t.jsx)(B.jt,{variant:"card",count:1}),(0,t.jsxs)("div",{className:"grid lg:grid-cols-3 gap-6",children:[(0,t.jsx)(B.jt,{variant:"card",count:1,className:"lg:col-span-2"}),(0,t.jsx)(B.jt,{variant:"card",count:1})]})]}),emptyComponent:(0,t.jsxs)("div",{className:"text-center py-10",children:[(0,t.jsx)(S.z,{title:"Task Not Found",icon:p.A}),(0,t.jsx)("p",{className:"mb-4",children:"The requested task could not be found."}),(0,t.jsxs)(m.$,{onClick:()=>s.push("/tasks"),variant:"outline",className:"gap-2",children:[(0,t.jsx)(g.A,{className:"h-4 w-4"}),"Back to Tasks"]})]}),children:e=>(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(S.z,{title:e.description,icon:h.A,description:"Manage details and assignment for this task.",children:(0,t.jsxs)("div",{className:"flex gap-2 items-center flex-wrap",children:[(0,t.jsxs)(m.$,{variant:"outline",onClick:()=>s.push("/tasks"),className:"gap-2",children:[(0,t.jsx)(g.A,{className:"h-4 w-4"}),"Back to List"]}),(0,t.jsx)(m.$,{variant:"default",asChild:!0,className:"gap-2",children:(0,t.jsxs)(i(),{href:"/tasks/".concat(e.id,"/edit"),children:[(0,t.jsx)(f.A,{className:"h-4 w-4"}),"Edit"]})}),(0,t.jsxs)(L.Lt,{children:[(0,t.jsx)(L.tv,{asChild:!0,children:(0,t.jsxs)(m.$,{variant:"destructive",className:"gap-2",children:[(0,t.jsx)(j.A,{className:"h-4 w-4"}),"Delete Task"]})}),(0,t.jsxs)(L.EO,{children:[(0,t.jsxs)(L.wd,{children:[(0,t.jsx)(L.r7,{children:"Are you sure?"}),(0,t.jsx)(L.$v,{children:"This action cannot be undone. This will permanently delete the task."})]}),(0,t.jsxs)(L.ck,{children:[(0,t.jsx)(L.Zr,{children:"Cancel"}),(0,t.jsx)(L.Rx,{onClick:et,className:"bg-destructive hover:bg-destructive/90",children:"Delete"})]})]})]})]})}),(0,t.jsxs)("div",{className:"grid lg:grid-cols-3 gap-6",children:[(0,t.jsxs)(n.Zp,{className:"lg:col-span-2 shadow-sm",children:[(0,t.jsx)(n.aR,{className:"border-b",children:(0,t.jsxs)("div",{className:"flex justify-between items-start gap-4",children:[(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsx)(n.ZB,{className:"text-2xl font-bold leading-tight",children:e.description}),(0,t.jsx)(n.BT,{children:"Detailed overview of the task"})]}),(0,t.jsxs)("div",{className:"flex flex-col gap-2",children:[(0,t.jsx)(P.E,{className:(0,q.cn)("justify-center",D(e.status)),children:e.status}),(0,t.jsxs)(P.E,{variant:"outline",className:(0,q.cn)("justify-center",M(e.priority)),children:[e.priority," Priority"]})]})]})}),(0,t.jsxs)(n.Wu,{className:"p-6 space-y-6",children:[(0,t.jsxs)("div",{className:"grid md:grid-cols-2 gap-4",children:[(0,t.jsx)(I,{icon:N.A,label:"Location",value:e.location}),(0,t.jsx)(I,{icon:y.A,label:"Start Date & Time",value:$(e.dateTime,!0)}),e.deadline&&(0,t.jsx)(I,{icon:b.A,label:"Deadline",value:$(e.deadline,!0)}),(0,t.jsx)(I,{icon:b.A,label:"Estimated Duration",value:"".concat(e.estimatedDuration," minutes")})]}),e.requiredSkills&&e.requiredSkills.length>0&&(0,t.jsx)(I,{icon:v.A,label:"Required Skills",children:(0,t.jsx)("div",{className:"flex flex-wrap gap-1",children:e.requiredSkills.map(e=>(0,t.jsx)(P.E,{variant:"secondary",className:"text-xs",children:e},e))})}),Z&&(0,t.jsx)(I,{icon:k.A,label:"Associated Vehicle",children:(0,t.jsxs)(i(),{href:"/vehicles/".concat(Z.id),className:"text-primary hover:underline flex items-center gap-1",children:[Z.make," ",Z.model," (",Z.year,")",(0,t.jsx)(w.A,{className:"h-3 w-3"})]})}),F.length>0&&(0,t.jsx)(H,{assignedEmployees:F,onUnassign:el,isTaskCompleted:ed}),e.notes&&(0,t.jsx)(I,{icon:h.A,label:"Notes",className:"md:col-span-2",children:(0,t.jsx)("p",{className:"text-base font-medium whitespace-pre-wrap",children:e.notes})}),e.subTasks&&e.subTasks.length>0&&(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)(c.w,{}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("h3",{className:"text-lg font-semibold mb-3 flex items-center gap-2",children:[(0,t.jsx)(h.A,{className:"h-4 w-4"}),"Sub-Tasks"]}),(0,t.jsx)("ul",{className:"space-y-2",children:e.subTasks.map(e=>(0,t.jsxs)("li",{className:(0,q.cn)("flex items-center gap-2 text-sm",e.completed&&"line-through text-muted-foreground"),children:[e.completed?(0,t.jsx)(A.A,{className:"h-4 w-4 text-green-500"}):(0,t.jsx)("div",{className:"h-4 w-4 border rounded-sm"}),e.title]},e.id))})]})]})]}),(0,t.jsxs)(n.wL,{className:"border-t text-xs text-muted-foreground",children:["Created: ",$(e.createdAt,!0)," | Updated:"," ",$(e.updatedAt,!0)]})]}),(0,t.jsxs)(n.Zp,{className:"shadow-sm h-fit",children:[(0,t.jsx)(n.aR,{children:(0,t.jsxs)(n.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(x.A,{className:"h-5 w-5 text-primary"}),"Task Assignment"]})}),(0,t.jsxs)(n.Wu,{className:"space-y-4",children:[F.length>0?(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Currently assigned to ",F.length," ","employee(s):"]}),F.map(e=>(0,t.jsxs)("div",{className:"flex items-center justify-between p-3 rounded-lg border bg-muted/30",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(o.eu,{className:"h-8 w-8",children:(0,t.jsx)(o.q5,{className:"text-xs",children:e.fullName.split(" ").map(e=>e[0]).join("").toUpperCase().slice(0,2)})}),(0,t.jsxs)("div",{children:[(0,t.jsx)(i(),{href:"/employees/".concat(e.id),className:"font-medium hover:text-primary text-sm",children:e.fullName}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground capitalize",children:e.role.replace("_"," ")})]})]}),!ed&&(0,t.jsxs)(m.$,{variant:"ghost",size:"sm",onClick:()=>el(e.id),className:"gap-1",children:[(0,t.jsx)(u.A,{className:"h-4 w-4"}),"Unassign"]})]},e.id))]}),!ed&&F.length>1&&(0,t.jsxs)(m.$,{variant:"outline",onClick:()=>el(),className:"w-full gap-2",children:[(0,t.jsx)(u.A,{className:"h-4 w-4"}),"Unassign All"]})]}):!ed&&(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"This task is currently unassigned."}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)(_.J,{htmlFor:"assignee-select",className:"text-sm font-medium",children:"Select Employee to Assign"}),(0,t.jsxs)(U.l6,{value:G,onValueChange:K,children:[(0,t.jsx)(U.bq,{children:(0,t.jsx)(U.yv,{placeholder:"Select an employee"})}),(0,t.jsx)(U.gC,{children:O.length>0?O.map(e=>(0,t.jsxs)(U.eb,{value:String(e.id),children:[e.fullName," (",e.role.charAt(0).toUpperCase()+e.role.slice(1).replace("_"," "),")"]},e.id)):(0,t.jsx)("div",{className:"p-2 text-sm text-muted-foreground",children:"No available employees."})})]}),(0,t.jsxs)(m.$,{onClick:er,disabled:!G,className:"w-full gap-2",children:[(0,t.jsx)(T.A,{className:"h-4 w-4"}),"Assign to Selected Employee"]})]})]}),"Completed"===e.status&&(0,t.jsxs)("div",{className:"flex items-center gap-2 p-3 rounded-lg bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800",children:[(0,t.jsx)(A.A,{className:"h-4 w-4 text-green-600"}),(0,t.jsxs)("p",{className:"text-sm text-green-700 dark:text-green-400",children:["Task completed",F.length>0?" by ".concat(F.map(e=>e.fullName).join(", ")):"","."]})]}),"Cancelled"===e.status&&(0,t.jsxs)("div",{className:"flex items-center gap-2 p-3 rounded-lg bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800",children:[(0,t.jsx)(C.A,{className:"h-4 w-4 text-red-600"}),(0,t.jsx)("p",{className:"text-sm text-red-700 dark:text-red-400",children:"Task cancelled."})]})]})]})]})]})})})}},85057:(e,s,a)=>{"use strict";a.d(s,{J:()=>c});var t=a(95155),r=a(12115),l=a(40968),d=a(74466),i=a(59434);let n=(0,d.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)(l.b,{ref:s,className:(0,i.cn)(n(),a),...r})});c.displayName=l.b.displayName},91394:(e,s,a)=>{"use strict";a.d(s,{BK:()=>n,eu:()=>i,q5:()=>c});var t=a(95155),r=a(12115),l=a(54011),d=a(59434);let i=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)(l.bL,{ref:s,className:(0,d.cn)("relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full",a),...r})});i.displayName=l.bL.displayName;let n=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)(l._V,{ref:s,className:(0,d.cn)("aspect-square h-full w-full",a),...r})});n.displayName=l._V.displayName;let c=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)(l.H4,{ref:s,className:(0,d.cn)("flex h-full w-full items-center justify-center rounded-full bg-muted",a),...r})});c.displayName=l.H4.displayName},91402:(e,s,a)=>{Promise.resolve().then(a.bind(a,78297))}},e=>{var s=s=>e(e.s=s);e.O(0,[3796,6113,832,2688,2512,1859,7529,8241,5540,8162,2730,4765,8441,1684,7358],()=>s(91402)),_N_E=e.O()}]);