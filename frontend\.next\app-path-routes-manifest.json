{"/_not-found/page": "/_not-found", "/favicon.ico/route": "/favicon.ico", "/auth-test/page": "/auth-test", "/delegations/[id]/edit/page": "/delegations/[id]/edit", "/add-vehicle/page": "/add-vehicle", "/delegations/[id]/page": "/delegations/[id]", "/delegations/add/page": "/delegations/add", "/employees/[id]/edit/page": "/employees/[id]/edit", "/delegations/page": "/delegations", "/employees/edit/[id]/page": "/employees/edit/[id]", "/employees/[id]/page": "/employees/[id]", "/employees/add/page": "/employees/add", "/page": "/", "/employees/new/page": "/employees/new", "/service-history/page": "/service-history", "/employees/page": "/employees", "/tasks/[id]/edit/page": "/tasks/[id]/edit", "/tasks/[id]/page": "/tasks/[id]", "/tasks/add/page": "/tasks/add", "/tasks/page": "/tasks", "/vehicles/edit/[id]/page": "/vehicles/edit/[id]", "/vehicles/[id]/page": "/vehicles/[id]", "/vehicles/new/page": "/vehicles/new", "/vehicles/page": "/vehicles", "/delegations/report/list/page": "/delegations/report/list", "/tasks/report/page": "/tasks/report", "/delegations/[id]/report/page": "/delegations/[id]/report", "/vehicles/[id]/report/service-history/page": "/vehicles/[id]/report/service-history", "/vehicles/[id]/report/page": "/vehicles/[id]/report", "/admin/page": "/admin"}