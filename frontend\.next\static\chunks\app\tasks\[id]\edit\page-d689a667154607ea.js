(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4344],{15748:(t,a,e)=>{Promise.resolve().then(e.bind(e,33569))},33569:(t,a,e)=>{"use strict";e.r(a),e.d(a,{default:()=>p});var s=e(95155),i=e(12115),r=e(35695),n=e(79732),d=e(2730),c=e(95647);let l=(0,e(40157).A)("FilePen",[["path",{d:"M12.5 22H18a2 2 0 0 0 2-2V7l-5-5H6a2 2 0 0 0-2 2v9.5",key:"1couwa"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M13.378 15.626a1 1 0 1 0-3.004-3.004l-5.01 5.012a2 2 0 0 0-.506.854l-.837 2.87a.5.5 0 0 0 .62.62l2.87-.837a2 2 0 0 0 .854-.506z",key:"1y4qbx"}]]);var o=e(87481),u=e(68856);function p(){let t=(0,r.useRouter)(),a=(0,r.useParams)(),{toast:e}=(0,o.dj)(),[p,h]=(0,i.useState)(null),[k,v]=(0,i.useState)(!0),f=a.id;(0,i.useEffect)(()=>{(async()=>{if(f)try{let a=await (0,d.getTaskById)(f);a?h(a):(e({title:"Error",description:"Task not found.",variant:"destructive"}),t.push("/tasks"))}catch(a){console.error("Error fetching task:",a),e({title:"Error",description:"Failed to load task data.",variant:"destructive"}),t.push("/tasks")}finally{v(!1)}})()},[f,t,e]);let y=async a=>{if(f&&p)try{let s=await (0,d.updateTask)(f,a);s?(e({title:"Task Updated",description:'The task "'.concat(s.description.substring(0,30),'..." has been successfully updated.'),variant:"default"}),t.push("/tasks/".concat(f))):e({title:"Error",description:"Failed to update task.",variant:"destructive"})}catch(t){console.error("Error updating task:",t),e({title:"Error",description:"Failed to update task. Please try again.",variant:"destructive"})}};return k?(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsx)(c.z,{title:"Loading...",icon:l}),(0,s.jsx)(u.E,{className:"h-[600px] w-full rounded-lg bg-card"})]}):p?(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsx)(c.z,{title:"Edit Task: ".concat(p.description.substring(0,50)).concat(p.description.length>50?"...":""),description:"Modify the details for this task.",icon:l}),(0,s.jsx)(n.A,{onSubmit:y,initialData:p,isEditing:!0})]}):(0,s.jsx)("p",{children:"Task not found."})}},68856:(t,a,e)=>{"use strict";e.d(a,{E:()=>r});var s=e(95155),i=e(59434);function r(t){let{className:a,...e}=t;return(0,s.jsx)("div",{className:(0,i.cn)("animate-pulse rounded-md bg-muted",a),...e})}}},t=>{var a=a=>t(t.s=a);t.O(0,[3796,6113,832,2688,2512,1859,4066,8162,2730,6174,8441,1684,7358],()=>a(15748)),_N_E=t.O()}]);