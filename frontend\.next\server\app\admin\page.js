(()=>{var e={};e.id=3698,e.ids=[3698],e.modules={22:(e,t,r)=>{var n=r(75254),o=r(20623),i=r(48169),a=r(40542),c=r(45058);e.exports=function(e){return"function"==typeof e?e:null==e?i:"object"==typeof e?a(e)?o(e[0],e[1]):n(e):c(e)}},658:(e,t,r)=>{e.exports=r(41547)(r(85718),"Map")},1132:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});let n=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\projects\\\\CarServiceTrackingSystem\\\\TrackerSystem\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\admin\\page.tsx","default")},1566:(e,t,r)=>{var n=r(89167),o=r(658),i=r(30401),a=r(34772),c=r(17830),s=r(29395),l=r(12290),u="[object Map]",f="[object Promise]",p="[object Set]",d="[object WeakMap]",h="[object DataView]",y=l(n),m=l(o),v=l(i),b=l(a),g=l(c),x=s;(n&&x(new n(new ArrayBuffer(1)))!=h||o&&x(new o)!=u||i&&x(i.resolve())!=f||a&&x(new a)!=p||c&&x(new c)!=d)&&(x=function(e){var t=s(e),r="[object Object]"==t?e.constructor:void 0,n=r?l(r):"";if(n)switch(n){case y:return h;case m:return u;case v:return f;case b:return p;case g:return d}return t}),e.exports=x},1707:(e,t,r)=>{var n=r(35142),o=r(46436);e.exports=function(e,t){t=n(t,e);for(var r=0,i=t.length;null!=e&&r<i;)e=e[o(t[r++])];return r&&r==i?e:void 0}},1944:e=>{e.exports=function(){return!1}},2408:e=>{e.exports=function(e){var t=-1,r=Array(e.size);return e.forEach(function(e){r[++t]=e}),r}},2896:(e,t,r)=>{var n=r(81488),o=r(59467);e.exports=function(e,t){return null!=e&&o(e,t,n)}},2984:(e,t,r)=>{var n=r(49227);e.exports=function(e,t,r){for(var o=-1,i=e.length;++o<i;){var a=e[o],c=t(a);if(null!=c&&(void 0===s?c==c&&!n(c):r(c,s)))var s=c,l=a}return l}},3105:e=>{e.exports=function(e){return e.split("")}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3662:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(82614).A)("CircleCheckBig",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},4999:(e,t,r)=>{e.exports=r(85718).Uint8Array},5231:(e,t,r)=>{var n=r(29395),o=r(55048);e.exports=function(e){if(!o(e))return!1;var t=n(e);return"[object Function]"==t||"[object GeneratorFunction]"==t||"[object AsyncFunction]"==t||"[object Proxy]"==t}},5359:e=>{e.exports=function(e){var t=null==e?0:e.length;return t?e[t-1]:void 0}},5566:(e,t,r)=>{var n=r(41011),o=r(34117),i=r(66713),a=r(42403);e.exports=function(e){return function(t){var r=o(t=a(t))?i(t):void 0,c=r?r[0]:t.charAt(0),s=r?n(r,1).join(""):t.slice(1);return c[e]()+s}}},6053:e=>{var t=/\s/;e.exports=function(e){for(var r=e.length;r--&&t.test(e.charAt(r)););return r}},6330:e=>{e.exports=function(){return[]}},6788:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>y2});var n={};r.r(n),r.d(n,{scaleBand:()=>oR,scaleDiverging:()=>function e(){var t=aS(sF()(aa));return t.copy=function(){return sR(t,e())},oM.apply(t,arguments)},scaleDivergingLog:()=>function e(){var t=aC(sF()).domain([.1,1,10]);return t.copy=function(){return sR(t,e()).base(t.base())},oM.apply(t,arguments)},scaleDivergingPow:()=>sU,scaleDivergingSqrt:()=>s$,scaleDivergingSymlog:()=>function e(){var t=aB(sF());return t.copy=function(){return sR(t,e()).constant(t.constant())},oM.apply(t,arguments)},scaleIdentity:()=>function e(t){var r;function n(e){return null==e||isNaN(e*=1)?r:e}return n.invert=n,n.domain=n.range=function(e){return arguments.length?(t=Array.from(e,ao),n):t.slice()},n.unknown=function(e){return arguments.length?(r=e,n):r},n.copy=function(){return e(t).unknown(r)},t=arguments.length?Array.from(t,ao):[0,1],aS(n)},scaleImplicit:()=>oI,scaleLinear:()=>aP,scaleLog:()=>function e(){let t=aC(af()).domain([1,10]);return t.copy=()=>au(t,e()).base(t.base()),oT.apply(t,arguments),t},scaleOrdinal:()=>oB,scalePoint:()=>oL,scalePow:()=>aU,scaleQuantile:()=>function e(){var t,r=[],n=[],o=[];function i(){var e=0,t=Math.max(1,n.length);for(o=Array(t-1);++e<t;)o[e-1]=function(e,t,r=iO){if(!(!(n=e.length)||isNaN(t*=1))){if(t<=0||n<2)return+r(e[0],0,e);if(t>=1)return+r(e[n-1],n-1,e);var n,o=(n-1)*t,i=Math.floor(o),a=+r(e[i],i,e);return a+(r(e[i+1],i+1,e)-a)*(o-i)}}(r,e/t);return a}function a(e){return null==e||isNaN(e*=1)?t:n[iP(o,e)]}return a.invertExtent=function(e){var t=n.indexOf(e);return t<0?[NaN,NaN]:[t>0?o[t-1]:r[0],t<o.length?o[t]:r[r.length-1]]},a.domain=function(e){if(!arguments.length)return r.slice();for(let t of(r=[],e))null==t||isNaN(t*=1)||r.push(t);return r.sort(ig),i()},a.range=function(e){return arguments.length?(n=Array.from(e),i()):n.slice()},a.unknown=function(e){return arguments.length?(t=e,a):t},a.quantiles=function(){return o.slice()},a.copy=function(){return e().domain(r).range(n).unknown(t)},oT.apply(a,arguments)},scaleQuantize:()=>function e(){var t,r=0,n=1,o=1,i=[.5],a=[0,1];function c(e){return null!=e&&e<=e?a[iP(i,e,0,o)]:t}function s(){var e=-1;for(i=Array(o);++e<o;)i[e]=((e+1)*n-(e-o)*r)/(o+1);return c}return c.domain=function(e){return arguments.length?([r,n]=e,r*=1,n*=1,s()):[r,n]},c.range=function(e){return arguments.length?(o=(a=Array.from(e)).length-1,s()):a.slice()},c.invertExtent=function(e){var t=a.indexOf(e);return t<0?[NaN,NaN]:t<1?[r,i[0]]:t>=o?[i[o-1],n]:[i[t-1],i[t]]},c.unknown=function(e){return arguments.length&&(t=e),c},c.thresholds=function(){return i.slice()},c.copy=function(){return e().domain([r,n]).range(a).unknown(t)},oT.apply(aS(c),arguments)},scaleRadial:()=>function e(){var t,r=ap(),n=[0,1],o=!1;function i(e){var n,i=Math.sign(n=r(e))*Math.sqrt(Math.abs(n));return isNaN(i)?t:o?Math.round(i):i}return i.invert=function(e){return r.invert(aq(e))},i.domain=function(e){return arguments.length?(r.domain(e),i):r.domain()},i.range=function(e){return arguments.length?(r.range((n=Array.from(e,ao)).map(aq)),i):n.slice()},i.rangeRound=function(e){return i.range(e).round(!0)},i.round=function(e){return arguments.length?(o=!!e,i):o},i.clamp=function(e){return arguments.length?(r.clamp(e),i):r.clamp()},i.unknown=function(e){return arguments.length?(t=e,i):t},i.copy=function(){return e(r.domain(),n).round(o).clamp(r.clamp()).unknown(t)},oT.apply(i,arguments),aS(i)},scaleSequential:()=>function e(){var t=aS(sB()(aa));return t.copy=function(){return sR(t,e())},oM.apply(t,arguments)},scaleSequentialLog:()=>function e(){var t=aC(sB()).domain([1,10]);return t.copy=function(){return sR(t,e()).base(t.base())},oM.apply(t,arguments)},scaleSequentialPow:()=>sL,scaleSequentialQuantile:()=>function e(){var t=[],r=aa;function n(e){if(null!=e&&!isNaN(e*=1))return r((iP(t,e,1)-1)/(t.length-1))}return n.domain=function(e){if(!arguments.length)return t.slice();for(let r of(t=[],e))null==r||isNaN(r*=1)||t.push(r);return t.sort(ig),n},n.interpolator=function(e){return arguments.length?(r=e,n):r},n.range=function(){return t.map((e,n)=>r(n/(t.length-1)))},n.quantiles=function(e){return Array.from({length:e+1},(r,n)=>(function(e,t,r){if(!(!(n=(e=Float64Array.from(function*(e,t){if(void 0===t)for(let t of e)null!=t&&(t*=1)>=t&&(yield t);else{let r=-1;for(let n of e)null!=(n=t(n,++r,e))&&(n*=1)>=n&&(yield n)}}(e,void 0))).length)||isNaN(t*=1))){if(t<=0||n<2)return aH(e);if(t>=1)return aW(e);var n,o=(n-1)*t,i=Math.floor(o),a=aW((function e(t,r,n=0,o=1/0,i){if(r=Math.floor(r),n=Math.floor(Math.max(0,n)),o=Math.floor(Math.min(t.length-1,o)),!(n<=r&&r<=o))return t;for(i=void 0===i?aX:function(e=ig){if(e===ig)return aX;if("function"!=typeof e)throw TypeError("compare is not a function");return(t,r)=>{let n=e(t,r);return n||0===n?n:(0===e(r,r))-(0===e(t,t))}}(i);o>n;){if(o-n>600){let a=o-n+1,c=r-n+1,s=Math.log(a),l=.5*Math.exp(2*s/3),u=.5*Math.sqrt(s*l*(a-l)/a)*(c-a/2<0?-1:1),f=Math.max(n,Math.floor(r-c*l/a+u)),p=Math.min(o,Math.floor(r+(a-c)*l/a+u));e(t,r,f,p,i)}let a=t[r],c=n,s=o;for(aV(t,n,r),i(t[o],a)>0&&aV(t,n,o);c<s;){for(aV(t,c,s),++c,--s;0>i(t[c],a);)++c;for(;i(t[s],a)>0;)--s}0===i(t[n],a)?aV(t,n,s):aV(t,++s,o),s<=r&&(n=s+1),r<=s&&(o=s-1)}return t})(e,i).subarray(0,i+1));return a+(aH(e.subarray(i+1))-a)*(o-i)}})(t,n/e))},n.copy=function(){return e(r).domain(t)},oM.apply(n,arguments)},scaleSequentialSqrt:()=>sz,scaleSequentialSymlog:()=>function e(){var t=aB(sB());return t.copy=function(){return sR(t,e()).constant(t.constant())},oM.apply(t,arguments)},scaleSqrt:()=>a$,scaleSymlog:()=>function e(){var t=aB(af());return t.copy=function(){return au(t,e()).constant(t.constant())},oT.apply(t,arguments)},scaleThreshold:()=>function e(){var t,r=[.5],n=[0,1],o=1;function i(e){return null!=e&&e<=e?n[iP(r,e,0,o)]:t}return i.domain=function(e){return arguments.length?(o=Math.min((r=Array.from(e)).length,n.length-1),i):r.slice()},i.range=function(e){return arguments.length?(n=Array.from(e),o=Math.min(r.length,n.length-1),i):n.slice()},i.invertExtent=function(e){var t=n.indexOf(e);return[r[t-1],r[t]]},i.unknown=function(e){return arguments.length?(t=e,i):t},i.copy=function(){return e().domain(r).range(n).unknown(t)},oT.apply(i,arguments)},scaleTime:()=>sD,scaleUtc:()=>sI,tickFormat:()=>aO});var o=r(60687),i=r(48041),a=r(58369),c=r(99196),s=r(35265),l=r(43210),u=r.n(l),f=r(70569),p=r(11273),d=r(72942),h=r(46059),y=r(14163),m=r(43),v=r(65551),b=r(96963),g="Tabs",[x,w]=(0,p.A)(g,[d.RG]),j=(0,d.RG)(),[O,S]=x(g),P=l.forwardRef((e,t)=>{let{__scopeTabs:r,value:n,onValueChange:i,defaultValue:a,orientation:c="horizontal",dir:s,activationMode:l="automatic",...u}=e,f=(0,m.jH)(s),[p,d]=(0,v.i)({prop:n,onChange:i,defaultProp:a??"",caller:g});return(0,o.jsx)(O,{scope:r,baseId:(0,b.B)(),value:p,onValueChange:d,orientation:c,dir:f,activationMode:l,children:(0,o.jsx)(y.sG.div,{dir:f,"data-orientation":c,...u,ref:t})})});P.displayName=g;var A="TabsList",k=l.forwardRef((e,t)=>{let{__scopeTabs:r,loop:n=!0,...i}=e,a=S(A,r),c=j(r);return(0,o.jsx)(d.bL,{asChild:!0,...c,orientation:a.orientation,dir:a.dir,loop:n,children:(0,o.jsx)(y.sG.div,{role:"tablist","aria-orientation":a.orientation,...i,ref:t})})});k.displayName=A;var E="TabsTrigger",N=l.forwardRef((e,t)=>{let{__scopeTabs:r,value:n,disabled:i=!1,...a}=e,c=S(E,r),s=j(r),l=_(c.baseId,n),u=C(c.baseId,n),p=n===c.value;return(0,o.jsx)(d.q7,{asChild:!0,...s,focusable:!i,active:p,children:(0,o.jsx)(y.sG.button,{type:"button",role:"tab","aria-selected":p,"aria-controls":u,"data-state":p?"active":"inactive","data-disabled":i?"":void 0,disabled:i,id:l,...a,ref:t,onMouseDown:(0,f.m)(e.onMouseDown,e=>{i||0!==e.button||!1!==e.ctrlKey?e.preventDefault():c.onValueChange(n)}),onKeyDown:(0,f.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&c.onValueChange(n)}),onFocus:(0,f.m)(e.onFocus,()=>{let e="manual"!==c.activationMode;p||i||!e||c.onValueChange(n)})})})});N.displayName=E;var T="TabsContent",M=l.forwardRef((e,t)=>{let{__scopeTabs:r,value:n,forceMount:i,children:a,...c}=e,s=S(T,r),u=_(s.baseId,n),f=C(s.baseId,n),p=n===s.value,d=l.useRef(p);return l.useEffect(()=>{let e=requestAnimationFrame(()=>d.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,o.jsx)(h.C,{present:i||p,children:({present:r})=>(0,o.jsx)(y.sG.div,{"data-state":p?"active":"inactive","data-orientation":s.orientation,role:"tabpanel","aria-labelledby":u,hidden:!r,id:f,tabIndex:0,...c,ref:t,style:{...e.style,animationDuration:d.current?"0s":void 0},children:r&&a})})});function _(e,t){return`${e}-trigger-${t}`}function C(e,t){return`${e}-content-${t}`}M.displayName=T;var D=r(4780);let I=l.forwardRef(({className:e,...t},r)=>(0,o.jsx)(k,{ref:r,className:(0,D.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...t}));I.displayName=k.displayName;let B=l.forwardRef(({className:e,...t},r)=>(0,o.jsx)(N,{ref:r,className:(0,D.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",e),...t}));B.displayName=N.displayName;let R=l.forwardRef(({className:e,...t},r)=>(0,o.jsx)(M,{ref:r,className:(0,D.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...t}));R.displayName=M.displayName;var L=r(44493),z=r(96834),F=r(3662),U=r(97025),$=r(72963),q=r(77368),W=r(68752),H=r(49254);let X={maxRetries:3,initialDelay:300,maxDelay:5e3,backoffFactor:2,shouldRetry:e=>!e.response||e.response.status>=500&&e.response.status<600},V=e=>new Promise(t=>setTimeout(t,e)),G=(e,t=X)=>{let{initialDelay:r=300,backoffFactor:n=2,maxDelay:o=5e3}=t;return Math.min(r*Math.pow(n,e)+100*Math.random(),o)};async function K(e,t=X){let{maxRetries:r=3,shouldRetry:n=X.shouldRetry}=t,o=0;for(;;)try{return await e()}catch(i){if(++o>=r||!n(i))throw i;let e=G(o,t);console.warn(`API call failed, retrying (${o}/${r}) after ${e}ms`,i),await V(e)}}function Y(e){return async(...t)=>{var r=await e(...t);if(null==r)return r;if(r&&"object"==typeof r&&"status"in r){if("success"===r.status&&"data"in r)return r.data;else if("error"===r.status)throw Error(r.message||"Unknown error")}return r}}let Z=async()=>Y(()=>K(()=>(0,H.Fd)("/admin/health")))(),J=async()=>Y(()=>K(()=>(0,H.Fd)("/admin/performance")))(),Q=async(e={})=>{let{page:t=1,limit:r=10,level:n}=e,o=`/admin/errors?page=${t}&limit=${r}`;n&&(o+=`&level=${n}`);let i=await K(()=>(0,H.Fd)(o));return i&&"object"==typeof i&&"data"in i&&"pagination"in i?i:i&&"object"==typeof i&&"status"in i&&"success"===i.status?{data:i.data,pagination:i.pagination}:i};var ee=r(85726);function et(e,t={}){let{initialData:r=null,autoFetch:n=!0,deps:o=[],onSuccess:i,onError:a,...c}=t,[s,u]=(0,l.useState)(r),[f,p]=(0,l.useState)(n),[d,h]=(0,l.useState)(null),y=(0,l.useCallback)(async()=>{p(!0),h(null);try{let t=await K(()=>e(),c);u(t),i&&i(t)}catch(e){h(e.message||"An unexpected error occurred"),a&&a(e),console.error("API call failed:",e)}finally{p(!1)}},[e,...o]);return{data:s,isLoading:f,error:d,refetch:y}}var er=r(11516),en=r(91821),eo=r(29523);function ei({message:e,onRetry:t,className:r}){return(0,o.jsxs)(en.Fc,{variant:"destructive",className:(0,D.cn)("my-4",r),children:[(0,o.jsx)($.A,{className:"h-4 w-4"}),(0,o.jsx)(en.XL,{children:"Error"}),(0,o.jsx)(en.TN,{children:(0,o.jsxs)("div",{className:"mt-2",children:[(0,o.jsx)("p",{className:"text-sm text-muted-foreground mb-4",children:e}),t&&(0,o.jsxs)(eo.$,{variant:"outline",size:"sm",onClick:t,className:"flex items-center",children:[(0,o.jsx)(er.A,{className:"mr-2 h-4 w-4"}),"Retry"]})]})})]})}var ea=r(14975);class ec extends l.Component{constructor(e){super(e),this.resetErrorBoundary=()=>{this.props.onReset&&this.props.onReset(),this.setState({hasError:!1,error:null,errorInfo:null})},this.state={hasError:!1,error:null,errorInfo:null}}static getDerivedStateFromError(e){return{hasError:!0,error:e,errorInfo:null}}componentDidCatch(e,t){console.error("Error caught by ErrorBoundary:",e,t),this.setState({errorInfo:t})}render(){return this.state.hasError?this.props.fallback?this.props.fallback:(0,o.jsxs)(en.Fc,{variant:"destructive",className:"my-4",children:[(0,o.jsx)(ea.A,{className:"h-4 w-4"}),(0,o.jsx)(en.XL,{children:"Something went wrong"}),(0,o.jsx)(en.TN,{children:(0,o.jsxs)("div",{className:"mt-2",children:[(0,o.jsx)("p",{className:"text-sm text-muted-foreground mb-4",children:this.state.error?.message||"An unexpected error occurred"}),(0,o.jsxs)(eo.$,{variant:"outline",size:"sm",onClick:this.resetErrorBoundary,className:"flex items-center",children:[(0,o.jsx)(q.A,{className:"mr-2 h-4 w-4"}),"Try again"]})]})})]}):this.props.children}}function es(){let{data:e,isLoading:t,error:r,refetch:n}=et(()=>Z(),{maxRetries:3,initialDelay:500,deps:[Math.floor(Date.now()/3e4)]}),[i,a]=(0,l.useState)(!1),c=async()=>{a(!0),await n(),a(!1)},s=e=>"UP"===e?(0,o.jsx)(F.A,{className:"h-5 w-5 text-green-500"}):"DOWN"===e?(0,o.jsx)(U.A,{className:"h-5 w-5 text-red-500"}):(0,o.jsx)($.A,{className:"h-5 w-5 text-yellow-500"}),u=e=>"UP"===e?(0,o.jsx)(z.E,{variant:"outline",className:"bg-green-500/20 text-green-700 border-green-500/30",children:"Connected"}):"DOWN"===e?(0,o.jsx)(z.E,{variant:"outline",className:"bg-red-500/20 text-red-700 border-red-500/30",children:"Disconnected"}):(0,o.jsx)(z.E,{variant:"outline",className:"bg-yellow-500/20 text-yellow-700 border-yellow-500/30",children:"Unknown"});return(0,o.jsx)(ec,{children:(0,o.jsxs)(L.Zp,{className:"shadow-md",children:[(0,o.jsxs)(L.aR,{className:"pb-2 p-5",children:[(0,o.jsx)(L.ZB,{className:"text-xl font-semibold text-primary",children:"Connection Status"}),(0,o.jsx)(L.BT,{children:"Current status of database connections"})]}),(0,o.jsx)(L.Wu,{className:"p-5",children:t||i?(0,o.jsxs)("div",{className:"space-y-3",children:[(0,o.jsx)(ee.E,{className:"h-6 w-full"}),(0,o.jsx)(ee.E,{className:"h-6 w-full"}),(0,o.jsx)(ee.E,{className:"h-6 w-full"})]}):r?(0,o.jsx)(ei,{message:r,onRetry:n}):e?(0,o.jsxs)("div",{className:"space-y-4",children:[(0,o.jsxs)("div",{className:"flex items-center justify-between",children:[(0,o.jsx)("div",{className:"flex items-center space-x-2",children:(0,o.jsx)("span",{className:"font-medium",children:"Overall Status:"})}),(0,o.jsxs)("div",{className:"flex items-center space-x-2",children:[s(e.status),u(e.status)]})]}),(0,o.jsxs)("div",{className:"flex items-center justify-between",children:[(0,o.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,o.jsx)("span",{className:"font-medium",children:"Database:"}),(0,o.jsx)("span",{className:"text-sm text-muted-foreground",children:e.components.database.type})]}),(0,o.jsxs)("div",{className:"flex items-center space-x-2",children:[s(e.components.database.status),u(e.components.database.status)]})]}),e.components.database.error&&(0,o.jsxs)("div",{className:"ml-6 p-2 text-sm border-l-2 border-red-300 bg-red-500/10 text-red-700 dark:text-red-400",children:["Error: ",e.components.database.error.message||String(e.components.database.error)]}),e.components.supabase&&(0,o.jsxs)(o.Fragment,{children:[(0,o.jsxs)("div",{className:"flex items-center justify-between",children:[(0,o.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,o.jsx)("span",{className:"font-medium",children:"Supabase:"}),(0,o.jsx)("span",{className:"text-sm text-muted-foreground",children:e.components.supabase.url})]}),(0,o.jsxs)("div",{className:"flex items-center space-x-2",children:[s(e.components.supabase.status),u(e.components.supabase.status)]})]}),e.components.supabase.error&&(0,o.jsxs)("div",{className:"ml-6 p-2 text-sm border-l-2 border-red-300 bg-red-500/10 text-red-700 dark:text-red-400",children:["Error: ",e.components.supabase.error.message||String(e.components.supabase.error)]})]}),e.version&&(0,o.jsxs)("div",{className:"flex items-center justify-between",children:[(0,o.jsx)("span",{className:"font-medium",children:"Version:"}),(0,o.jsx)("span",{children:e.version})]}),void 0!==e.uptime&&(0,o.jsxs)("div",{className:"flex items-center justify-between",children:[(0,o.jsx)("span",{className:"font-medium",children:"Uptime:"}),(0,o.jsxs)("span",{children:[Math.floor(e.uptime/3600),"h"," ",Math.floor(e.uptime%3600/60),"m"]})]}),(0,o.jsxs)("div",{className:"text-xs text-muted-foreground",children:["Last updated: ",new Date(e.timestamp).toLocaleString()]})]}):null}),(0,o.jsx)(L.wL,{className:"p-5",children:(0,o.jsx)(W.r,{actionType:"tertiary",size:"sm",className:"w-full",onClick:c,isLoading:i||t,loadingText:"Refreshing...",icon:(0,o.jsx)(q.A,{className:"h-4 w-4"}),children:"Refresh Status"})})]})})}var el="Progress",[eu,ef]=(0,p.A)(el),[ep,ed]=eu(el),eh=l.forwardRef((e,t)=>{var r,n;let{__scopeProgress:i,value:a=null,max:c,getValueLabel:s=ev,...l}=e;(c||0===c)&&!ex(c)&&console.error((r=`${c}`,`Invalid prop \`max\` of value \`${r}\` supplied to \`Progress\`. Only numbers greater than 0 are valid max values. Defaulting to \`100\`.`));let u=ex(c)?c:100;null===a||ew(a,u)||console.error((n=`${a}`,`Invalid prop \`value\` of value \`${n}\` supplied to \`Progress\`. The \`value\` prop must be:
  - a positive number
  - less than the value passed to \`max\` (or 100 if no \`max\` prop is set)
  - \`null\` or \`undefined\` if the progress is indeterminate.

Defaulting to \`null\`.`));let f=ew(a,u)?a:null,p=eg(f)?s(f,u):void 0;return(0,o.jsx)(ep,{scope:i,value:f,max:u,children:(0,o.jsx)(y.sG.div,{"aria-valuemax":u,"aria-valuemin":0,"aria-valuenow":eg(f)?f:void 0,"aria-valuetext":p,role:"progressbar","data-state":eb(f,u),"data-value":f??void 0,"data-max":u,...l,ref:t})})});eh.displayName=el;var ey="ProgressIndicator",em=l.forwardRef((e,t)=>{let{__scopeProgress:r,...n}=e,i=ed(ey,r);return(0,o.jsx)(y.sG.div,{"data-state":eb(i.value,i.max),"data-value":i.value??void 0,"data-max":i.max,...n,ref:t})});function ev(e,t){return`${Math.round(e/t*100)}%`}function eb(e,t){return null==e?"indeterminate":e===t?"complete":"loading"}function eg(e){return"number"==typeof e}function ex(e){return eg(e)&&!isNaN(e)&&e>0}function ew(e,t){return eg(e)&&!isNaN(e)&&e<=t&&e>=0}em.displayName=ey;let ej=l.forwardRef(({className:e,value:t,...r},n)=>(0,o.jsx)(eh,{ref:n,className:(0,D.cn)("relative h-4 w-full overflow-hidden rounded-full bg-secondary",e),...r,children:(0,o.jsx)(em,{className:"h-full w-full flex-1 bg-primary transition-all",style:{transform:`translateX(-${100-(t||0)}%)`}})}));function eO(){let{data:e,isLoading:t,error:r,refetch:n}=et(()=>J(),{maxRetries:3,initialDelay:500,deps:[Math.floor(Date.now()/6e4)]}),[i,a]=(0,l.useState)(!1),c=async()=>{a(!0),await n(),a(!1)},s=e=>e>=90?"bg-green-500":e>=70?"bg-yellow-500":"bg-red-500";return(0,o.jsx)(ec,{children:(0,o.jsxs)(L.Zp,{className:"shadow-md",children:[(0,o.jsxs)(L.aR,{className:"pb-2 p-5",children:[(0,o.jsx)(L.ZB,{className:"text-xl font-semibold text-primary",children:"Database Health"}),(0,o.jsx)(L.BT,{children:"Performance metrics and health indicators"})]}),(0,o.jsx)(L.Wu,{className:"p-5",children:t||i?(0,o.jsxs)("div",{className:"space-y-3",children:[(0,o.jsx)(ee.E,{className:"h-6 w-full"}),(0,o.jsx)(ee.E,{className:"h-6 w-full"}),(0,o.jsx)(ee.E,{className:"h-6 w-full"}),(0,o.jsx)(ee.E,{className:"h-6 w-full"})]}):r?(0,o.jsx)(ei,{message:r,onRetry:n}):e?(0,o.jsxs)("div",{className:"space-y-4",children:[(0,o.jsxs)("div",{children:[(0,o.jsxs)("div",{className:"flex justify-between mb-1",children:[(0,o.jsx)("span",{className:"text-sm font-medium",children:"Index Cache Hit Rate"}),(0,o.jsxs)("span",{className:"text-sm font-medium",children:[e.cacheHitRate.indexHitRate.toFixed(1),"%"]})]}),(0,o.jsx)(ej,{value:e.cacheHitRate.indexHitRate,className:s(e.cacheHitRate.indexHitRate)}),(0,o.jsx)("p",{className:"text-xs text-muted-foreground mt-1",children:"Percentage of index lookups served from cache"})]}),(0,o.jsxs)("div",{children:[(0,o.jsxs)("div",{className:"flex justify-between mb-1",children:[(0,o.jsx)("span",{className:"text-sm font-medium",children:"Table Cache Hit Rate"}),(0,o.jsxs)("span",{className:"text-sm font-medium",children:[e.cacheHitRate.tableHitRate.toFixed(1),"%"]})]}),(0,o.jsx)(ej,{value:e.cacheHitRate.tableHitRate,className:s(e.cacheHitRate.tableHitRate)}),(0,o.jsx)("p",{className:"text-xs text-muted-foreground mt-1",children:"Percentage of table lookups served from cache"})]}),(0,o.jsxs)("div",{className:"grid grid-cols-2 gap-4 pt-2",children:[(0,o.jsxs)("div",{className:"border rounded-md p-3",children:[(0,o.jsx)("div",{className:"text-2xl font-bold",children:e.connectionCount}),(0,o.jsx)("div",{className:"text-xs text-muted-foreground",children:"Active Connections"})]}),(0,o.jsxs)("div",{className:"border rounded-md p-3",children:[(0,o.jsx)("div",{className:"text-2xl font-bold",children:e.activeQueries}),(0,o.jsx)("div",{className:"text-xs text-muted-foreground",children:"Running Queries"})]}),(0,o.jsxs)("div",{className:"border rounded-md p-3 col-span-2",children:[(0,o.jsxs)("div",{className:"text-2xl font-bold",children:[e.avgQueryTime.toFixed(2),"ms"]}),(0,o.jsx)("div",{className:"text-xs text-muted-foreground",children:"Average Query Time"})]})]}),e.timestamp&&(0,o.jsxs)("div",{className:"text-xs text-muted-foreground",children:["Last updated: ",new Date(e.timestamp).toLocaleString()]})]}):null}),(0,o.jsx)(L.wL,{className:"p-5",children:(0,o.jsx)(W.r,{actionType:"tertiary",size:"sm",className:"w-full",onClick:c,isLoading:i||t,loadingText:"Refreshing...",icon:(0,o.jsx)(q.A,{className:"h-4 w-4"}),children:"Refresh Metrics"})})]})})}ej.displayName=eh.displayName;var eS=r(82614);let eP=(0,eS.A)("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]]);var eA=r(42692),ek=r(15079);function eE(){let[e,t]=(0,l.useState)(void 0),[r,n]=(0,l.useState)(!1),{data:i,isLoading:a,error:s,refetch:u,page:f,totalPages:p,nextPage:d,prevPage:h,hasNextPage:y,hasPrevPage:m}=function(e,t={}){let{initialPage:r=1,pageSize:n=10,...o}=t,[i,a]=(0,l.useState)(r),[c,s]=(0,l.useState)(1),[u,f]=(0,l.useState)(0),p=et((0,l.useCallback)(()=>e(i,n).then(e=>(s(e.pagination.totalPages),f(e.pagination.total),e.data)),[e,i,n]),{...o,deps:[...o.deps||[],i,n]}),d=(0,l.useCallback)(()=>{i<c&&a(e=>e+1)},[i,c]),h=(0,l.useCallback)(()=>{i>1&&a(e=>e-1)},[i]),y=(0,l.useCallback)(e=>{e>=1&&e<=c&&a(e)},[c]);return{...p,page:i,totalPages:c,totalItems:u,nextPage:d,prevPage:h,goToPage:y,hasNextPage:i<c,hasPrevPage:i>1}}((t,r)=>Q({page:t,limit:r,level:e}),{maxRetries:3,initialDelay:500,pageSize:10,deps:[e]}),v=async()=>{n(!0),await u(),n(!1)},b=e=>"ERROR"===e?(0,o.jsx)(U.A,{className:"h-4 w-4 text-red-500"}):"WARNING"===e?(0,o.jsx)(ea.A,{className:"h-4 w-4 text-yellow-500"}):(0,o.jsx)(c.A,{className:"h-4 w-4 text-blue-500"}),g=e=>"ERROR"===e?(0,o.jsx)(z.E,{variant:"outline",className:"bg-red-500/20 text-red-700 border-red-500/30",children:"Error"}):"WARNING"===e?(0,o.jsx)(z.E,{variant:"outline",className:"bg-yellow-500/20 text-yellow-700 border-yellow-500/30",children:"Warning"}):(0,o.jsx)(z.E,{variant:"outline",className:"bg-blue-500/20 text-blue-700 border-blue-500/30",children:"Info"});return(0,o.jsx)(ec,{children:(0,o.jsxs)(L.Zp,{className:"shadow-md",children:[(0,o.jsxs)(L.aR,{className:"pb-2 p-5",children:[(0,o.jsx)(L.ZB,{className:"text-xl font-semibold text-primary",children:"Recent Errors & Warnings"}),(0,o.jsx)(L.BT,{children:"Latest system errors and warnings"})]}),(0,o.jsx)("div",{className:"px-5 pb-2",children:(0,o.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,o.jsx)(eP,{className:"h-4 w-4 text-muted-foreground"}),(0,o.jsx)("span",{className:"text-sm font-medium",children:"Filter by level:"}),(0,o.jsxs)(ek.l6,{value:e||"all",onValueChange:e=>{t("all"===e?void 0:e)},children:[(0,o.jsx)(ek.bq,{className:"w-[140px]",children:(0,o.jsx)(ek.yv,{placeholder:"All levels"})}),(0,o.jsxs)(ek.gC,{children:[(0,o.jsx)(ek.eb,{value:"all",children:"All levels"}),(0,o.jsx)(ek.eb,{value:"ERROR",children:"Errors only"}),(0,o.jsx)(ek.eb,{value:"WARNING",children:"Warnings only"}),(0,o.jsx)(ek.eb,{value:"INFO",children:"Info only"})]})]})]})}),(0,o.jsx)(L.Wu,{className:"p-5",children:a||r?(0,o.jsxs)("div",{className:"space-y-3",children:[(0,o.jsx)(ee.E,{className:"h-6 w-full"}),(0,o.jsx)(ee.E,{className:"h-6 w-full"}),(0,o.jsx)(ee.E,{className:"h-6 w-full"}),(0,o.jsx)(ee.E,{className:"h-6 w-full"}),(0,o.jsx)(ee.E,{className:"h-6 w-full"})]}):s?(0,o.jsx)(ei,{message:s,onRetry:u}):i&&i.length>0?(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(eA.F,{className:"h-[300px] pr-4",children:(0,o.jsx)("div",{className:"space-y-3",children:i.map(e=>(0,o.jsxs)("div",{className:"p-3 border rounded-md hover:bg-accent/50 transition-colors",children:[(0,o.jsxs)("div",{className:"flex items-center justify-between mb-1",children:[(0,o.jsxs)("div",{className:"flex items-center space-x-2",children:[b(e.level),(0,o.jsx)("span",{className:"font-medium",children:e.message})]}),g(e.level)]}),(0,o.jsxs)("div",{className:"flex items-center justify-between",children:[(0,o.jsx)("div",{className:"text-xs text-muted-foreground",children:new Date(e.timestamp).toLocaleString()}),e.source&&(0,o.jsxs)("div",{className:"text-xs text-muted-foreground",children:["Source: ",e.source]})]}),e.details&&Object.keys(e.details).length>0&&(0,o.jsx)("div",{className:"mt-2 text-xs p-2 bg-muted rounded",children:(0,o.jsx)("pre",{className:"whitespace-pre-wrap",children:JSON.stringify(e.details,null,2)})})]},e.id))})}),p>1&&(0,o.jsxs)("div",{className:"flex items-center justify-between mt-4",children:[(0,o.jsx)(W.r,{actionType:"tertiary",size:"sm",onClick:h,disabled:!m||a||r,children:"Previous"}),(0,o.jsxs)("span",{className:"text-sm text-muted-foreground",children:["Page ",f," of ",p]}),(0,o.jsx)(W.r,{actionType:"tertiary",size:"sm",onClick:d,disabled:!y||a||r,children:"Next"})]})]}):(0,o.jsxs)("div",{className:"p-8 text-center text-muted-foreground",children:[(0,o.jsx)(ea.A,{className:"mx-auto h-8 w-8 mb-2 text-muted-foreground/50"}),(0,o.jsx)("p",{children:"No errors or warnings found for the selected filter."}),e&&(0,o.jsx)("p",{className:"text-sm mt-2",children:"Try changing the filter to see more results."})]})}),(0,o.jsx)(L.wL,{className:"p-5",children:(0,o.jsx)(W.r,{actionType:"tertiary",size:"sm",className:"w-full",onClick:v,isLoading:r||a,loadingText:"Refreshing...",icon:(0,o.jsx)(q.A,{className:"h-4 w-4"}),children:"Refresh Logs"})})]})})}var eN=r(27805),eT=r(49384),eM=r(45603),e_=r.n(eM),eC=r(63866),eD=r.n(eC),eI=r(77822),eB=r.n(eI),eR=r(40491),eL=r.n(eR),ez=r(93490),eF=r.n(ez),eU=function(e){return 0===e?0:e>0?1:-1},e$=function(e){return eD()(e)&&e.indexOf("%")===e.length-1},eq=function(e){return eF()(e)&&!eB()(e)},eW=function(e){return eq(e)||eD()(e)},eH=0,eX=function(e){var t=++eH;return"".concat(e||"").concat(t)},eV=function(e,t){var r,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,o=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if(!eq(e)&&!eD()(e))return n;if(e$(e)){var i=e.indexOf("%");r=t*parseFloat(e.slice(0,i))/100}else r=+e;return eB()(r)&&(r=n),o&&r>t&&(r=t),r},eG=function(e){if(!e)return null;var t=Object.keys(e);return t&&t.length?e[t[0]]:null},eK=function(e){if(!Array.isArray(e))return!1;for(var t=e.length,r={},n=0;n<t;n++)if(r[e[n]])return!0;else r[e[n]]=!0;return!1},eY=function(e,t){return eq(e)&&eq(t)?function(r){return e+r*(t-e)}:function(){return t}};function eZ(e,t,r){return e&&e.length?e.find(function(e){return e&&("function"==typeof t?t(e):eL()(e,t))===r}):null}var eJ=function(e,t){return eq(e)&&eq(t)?e-t:eD()(e)&&eD()(t)?e.localeCompare(t):e instanceof Date&&t instanceof Date?e.getTime()-t.getTime():String(e).localeCompare(String(t))},eQ=function(e,t){for(var r=arguments.length,n=Array(r>2?r-2:0),o=2;o<r;o++)n[o-2]=arguments[o]},e0=r(37456),e1=r.n(e0),e2=r(5231),e5=r.n(e2),e4=r(55048),e3=r.n(e4),e6=r(29632);function e8(e,t){for(var r in e)if(({}).hasOwnProperty.call(e,r)&&(!({}).hasOwnProperty.call(t,r)||e[r]!==t[r]))return!1;for(var n in t)if(({}).hasOwnProperty.call(t,n)&&!({}).hasOwnProperty.call(e,n))return!1;return!0}function e7(e){return(e7="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var e9=["aria-activedescendant","aria-atomic","aria-autocomplete","aria-busy","aria-checked","aria-colcount","aria-colindex","aria-colspan","aria-controls","aria-current","aria-describedby","aria-details","aria-disabled","aria-errormessage","aria-expanded","aria-flowto","aria-haspopup","aria-hidden","aria-invalid","aria-keyshortcuts","aria-label","aria-labelledby","aria-level","aria-live","aria-modal","aria-multiline","aria-multiselectable","aria-orientation","aria-owns","aria-placeholder","aria-posinset","aria-pressed","aria-readonly","aria-relevant","aria-required","aria-roledescription","aria-rowcount","aria-rowindex","aria-rowspan","aria-selected","aria-setsize","aria-sort","aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext","className","color","height","id","lang","max","media","method","min","name","style","target","width","role","tabIndex","accentHeight","accumulate","additive","alignmentBaseline","allowReorder","alphabetic","amplitude","arabicForm","ascent","attributeName","attributeType","autoReverse","azimuth","baseFrequency","baselineShift","baseProfile","bbox","begin","bias","by","calcMode","capHeight","clip","clipPath","clipPathUnits","clipRule","colorInterpolation","colorInterpolationFilters","colorProfile","colorRendering","contentScriptType","contentStyleType","cursor","cx","cy","d","decelerate","descent","diffuseConstant","direction","display","divisor","dominantBaseline","dur","dx","dy","edgeMode","elevation","enableBackground","end","exponent","externalResourcesRequired","fill","fillOpacity","fillRule","filter","filterRes","filterUnits","floodColor","floodOpacity","focusable","fontFamily","fontSize","fontSizeAdjust","fontStretch","fontStyle","fontVariant","fontWeight","format","from","fx","fy","g1","g2","glyphName","glyphOrientationHorizontal","glyphOrientationVertical","glyphRef","gradientTransform","gradientUnits","hanging","horizAdvX","horizOriginX","href","ideographic","imageRendering","in2","in","intercept","k1","k2","k3","k4","k","kernelMatrix","kernelUnitLength","kerning","keyPoints","keySplines","keyTimes","lengthAdjust","letterSpacing","lightingColor","limitingConeAngle","local","markerEnd","markerHeight","markerMid","markerStart","markerUnits","markerWidth","mask","maskContentUnits","maskUnits","mathematical","mode","numOctaves","offset","opacity","operator","order","orient","orientation","origin","overflow","overlinePosition","overlineThickness","paintOrder","panose1","pathLength","patternContentUnits","patternTransform","patternUnits","pointerEvents","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","r","radius","refX","refY","renderingIntent","repeatCount","repeatDur","requiredExtensions","requiredFeatures","restart","result","rotate","rx","ry","seed","shapeRendering","slope","spacing","specularConstant","specularExponent","speed","spreadMethod","startOffset","stdDeviation","stemh","stemv","stitchTiles","stopColor","stopOpacity","strikethroughPosition","strikethroughThickness","string","stroke","strokeDasharray","strokeDashoffset","strokeLinecap","strokeLinejoin","strokeMiterlimit","strokeOpacity","strokeWidth","surfaceScale","systemLanguage","tableValues","targetX","targetY","textAnchor","textDecoration","textLength","textRendering","to","transform","u1","u2","underlinePosition","underlineThickness","unicode","unicodeBidi","unicodeRange","unitsPerEm","vAlphabetic","values","vectorEffect","version","vertAdvY","vertOriginX","vertOriginY","vHanging","vIdeographic","viewTarget","visibility","vMathematical","widths","wordSpacing","writingMode","x1","x2","x","xChannelSelector","xHeight","xlinkActuate","xlinkArcrole","xlinkHref","xlinkRole","xlinkShow","xlinkTitle","xlinkType","xmlBase","xmlLang","xmlns","xmlnsXlink","xmlSpace","y1","y2","y","yChannelSelector","z","zoomAndPan","ref","key","angle"],te=["points","pathLength"],tt={svg:["viewBox","children"],polygon:te,polyline:te},tr=["dangerouslySetInnerHTML","onCopy","onCopyCapture","onCut","onCutCapture","onPaste","onPasteCapture","onCompositionEnd","onCompositionEndCapture","onCompositionStart","onCompositionStartCapture","onCompositionUpdate","onCompositionUpdateCapture","onFocus","onFocusCapture","onBlur","onBlurCapture","onChange","onChangeCapture","onBeforeInput","onBeforeInputCapture","onInput","onInputCapture","onReset","onResetCapture","onSubmit","onSubmitCapture","onInvalid","onInvalidCapture","onLoad","onLoadCapture","onError","onErrorCapture","onKeyDown","onKeyDownCapture","onKeyPress","onKeyPressCapture","onKeyUp","onKeyUpCapture","onAbort","onAbortCapture","onCanPlay","onCanPlayCapture","onCanPlayThrough","onCanPlayThroughCapture","onDurationChange","onDurationChangeCapture","onEmptied","onEmptiedCapture","onEncrypted","onEncryptedCapture","onEnded","onEndedCapture","onLoadedData","onLoadedDataCapture","onLoadedMetadata","onLoadedMetadataCapture","onLoadStart","onLoadStartCapture","onPause","onPauseCapture","onPlay","onPlayCapture","onPlaying","onPlayingCapture","onProgress","onProgressCapture","onRateChange","onRateChangeCapture","onSeeked","onSeekedCapture","onSeeking","onSeekingCapture","onStalled","onStalledCapture","onSuspend","onSuspendCapture","onTimeUpdate","onTimeUpdateCapture","onVolumeChange","onVolumeChangeCapture","onWaiting","onWaitingCapture","onAuxClick","onAuxClickCapture","onClick","onClickCapture","onContextMenu","onContextMenuCapture","onDoubleClick","onDoubleClickCapture","onDrag","onDragCapture","onDragEnd","onDragEndCapture","onDragEnter","onDragEnterCapture","onDragExit","onDragExitCapture","onDragLeave","onDragLeaveCapture","onDragOver","onDragOverCapture","onDragStart","onDragStartCapture","onDrop","onDropCapture","onMouseDown","onMouseDownCapture","onMouseEnter","onMouseLeave","onMouseMove","onMouseMoveCapture","onMouseOut","onMouseOutCapture","onMouseOver","onMouseOverCapture","onMouseUp","onMouseUpCapture","onSelect","onSelectCapture","onTouchCancel","onTouchCancelCapture","onTouchEnd","onTouchEndCapture","onTouchMove","onTouchMoveCapture","onTouchStart","onTouchStartCapture","onPointerDown","onPointerDownCapture","onPointerMove","onPointerMoveCapture","onPointerUp","onPointerUpCapture","onPointerCancel","onPointerCancelCapture","onPointerEnter","onPointerEnterCapture","onPointerLeave","onPointerLeaveCapture","onPointerOver","onPointerOverCapture","onPointerOut","onPointerOutCapture","onGotPointerCapture","onGotPointerCaptureCapture","onLostPointerCapture","onLostPointerCaptureCapture","onScroll","onScrollCapture","onWheel","onWheelCapture","onAnimationStart","onAnimationStartCapture","onAnimationEnd","onAnimationEndCapture","onAnimationIteration","onAnimationIterationCapture","onTransitionEnd","onTransitionEndCapture"],tn=function(e,t){if(!e||"function"==typeof e||"boolean"==typeof e)return null;var r=e;if((0,l.isValidElement)(e)&&(r=e.props),!e3()(r))return null;var n={};return Object.keys(r).forEach(function(e){tr.includes(e)&&(n[e]=t||function(t){return r[e](r,t)})}),n},to=function(e,t,r){if(!e3()(e)||"object"!==e7(e))return null;var n=null;return Object.keys(e).forEach(function(o){var i=e[o];tr.includes(o)&&"function"==typeof i&&(n||(n={}),n[o]=function(e){return i(t,r,e),null})}),n},ti=["children"],ta=["children"];function tc(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}function ts(e){return(ts="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var tl={click:"onClick",mousedown:"onMouseDown",mouseup:"onMouseUp",mouseover:"onMouseOver",mousemove:"onMouseMove",mouseout:"onMouseOut",mouseenter:"onMouseEnter",mouseleave:"onMouseLeave",touchcancel:"onTouchCancel",touchend:"onTouchEnd",touchmove:"onTouchMove",touchstart:"onTouchStart",contextmenu:"onContextMenu",dblclick:"onDoubleClick"},tu=function(e){return"string"==typeof e?e:e?e.displayName||e.name||"Component":""},tf=null,tp=null,td=function e(t){if(t===tf&&Array.isArray(tp))return tp;var r=[];return l.Children.forEach(t,function(t){e1()(t)||((0,e6.isFragment)(t)?r=r.concat(e(t.props.children)):r.push(t))}),tp=r,tf=t,r};function th(e,t){var r=[],n=[];return n=Array.isArray(t)?t.map(function(e){return tu(e)}):[tu(t)],td(e).forEach(function(e){var t=eL()(e,"type.displayName")||eL()(e,"type.name");-1!==n.indexOf(t)&&r.push(e)}),r}function ty(e,t){var r=th(e,t);return r&&r[0]}var tm=function(e){if(!e||!e.props)return!1;var t=e.props,r=t.width,n=t.height;return!!eq(r)&&!(r<=0)&&!!eq(n)&&!(n<=0)},tv=["a","altGlyph","altGlyphDef","altGlyphItem","animate","animateColor","animateMotion","animateTransform","circle","clipPath","color-profile","cursor","defs","desc","ellipse","feBlend","feColormatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence","filter","font","font-face","font-face-format","font-face-name","font-face-url","foreignObject","g","glyph","glyphRef","hkern","image","line","lineGradient","marker","mask","metadata","missing-glyph","mpath","path","pattern","polygon","polyline","radialGradient","rect","script","set","stop","style","svg","switch","symbol","text","textPath","title","tref","tspan","use","view","vkern"],tb=function(e,t,r,n){var o,i=null!=(o=null==tt?void 0:tt[n])?o:[];return t.startsWith("data-")||!e5()(e)&&(n&&i.includes(t)||e9.includes(t))||r&&tr.includes(t)},tg=function(e,t,r){if(!e||"function"==typeof e||"boolean"==typeof e)return null;var n=e;if((0,l.isValidElement)(e)&&(n=e.props),!e3()(n))return null;var o={};return Object.keys(n).forEach(function(e){var i;tb(null==(i=n)?void 0:i[e],e,t,r)&&(o[e]=n[e])}),o},tx=function e(t,r){if(t===r)return!0;var n=l.Children.count(t);if(n!==l.Children.count(r))return!1;if(0===n)return!0;if(1===n)return tw(Array.isArray(t)?t[0]:t,Array.isArray(r)?r[0]:r);for(var o=0;o<n;o++){var i=t[o],a=r[o];if(Array.isArray(i)||Array.isArray(a)){if(!e(i,a))return!1}else if(!tw(i,a))return!1}return!0},tw=function(e,t){if(e1()(e)&&e1()(t))return!0;if(!e1()(e)&&!e1()(t)){var r=e.props||{},n=r.children,o=tc(r,ti),i=t.props||{},a=i.children,c=tc(i,ta);if(n&&a)return e8(o,c)&&tx(n,a);if(!n&&!a)return e8(o,c)}return!1},tj=function(e,t){var r=[],n={};return td(e).forEach(function(e,o){var i;if((i=e)&&i.type&&eD()(i.type)&&tv.indexOf(i.type)>=0)r.push(e);else if(e){var a=tu(e.type),c=t[a]||{},s=c.handler,l=c.once;if(s&&(!l||!n[a])){var u=s(e,a,o);r.push(u),n[a]=!0}}}),r},tO=function(e){var t=e&&e.type;return t&&tl[t]?tl[t]:null};function tS(e){return(tS="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function tP(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function tA(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?tP(Object(r),!0).forEach(function(t){var n,o,i;n=e,o=t,i=r[t],(o=function(e){var t=function(e,t){if("object"!=tS(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=tS(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==tS(t)?t:t+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):tP(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function tk(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}var tE=(0,l.forwardRef)(function(e,t){var r,n=e.aspect,o=e.initialDimension,i=void 0===o?{width:-1,height:-1}:o,a=e.width,c=void 0===a?"100%":a,s=e.height,f=void 0===s?"100%":s,p=e.minWidth,d=void 0===p?0:p,h=e.minHeight,y=e.maxHeight,m=e.children,v=e.debounce,b=void 0===v?0:v,g=e.id,x=e.className,w=e.onResize,j=e.style,O=(0,l.useRef)(null),S=(0,l.useRef)();S.current=w,(0,l.useImperativeHandle)(t,function(){return Object.defineProperty(O.current,"current",{get:function(){return console.warn("The usage of ref.current.current is deprecated and will no longer be supported."),O.current},configurable:!0})});var P=function(e){if(Array.isArray(e))return e}(r=(0,l.useState)({containerWidth:i.width,containerHeight:i.height}))||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,c=[],s=!0,l=!1;try{i=(r=r.call(e)).next,!1;for(;!(s=(n=i.call(r)).done)&&(c.push(n.value),c.length!==t);s=!0);}catch(e){l=!0,o=e}finally{try{if(!s&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(r,2)||function(e,t){if(e){if("string"==typeof e)return tk(e,2);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return tk(e,t)}}(r,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),A=P[0],k=P[1],E=(0,l.useCallback)(function(e,t){k(function(r){var n=Math.round(e),o=Math.round(t);return r.containerWidth===n&&r.containerHeight===o?r:{containerWidth:n,containerHeight:o}})},[]);(0,l.useEffect)(function(){var e=function(e){var t,r=e[0].contentRect,n=r.width,o=r.height;E(n,o),null==(t=S.current)||t.call(S,n,o)};b>0&&(e=e_()(e,b,{trailing:!0,leading:!1}));var t=new ResizeObserver(e),r=O.current.getBoundingClientRect();return E(r.width,r.height),t.observe(O.current),function(){t.disconnect()}},[E,b]);var N=(0,l.useMemo)(function(){var e=A.containerWidth,t=A.containerHeight;if(e<0||t<0)return null;eQ(e$(c)||e$(f),"The width(%s) and height(%s) are both fixed numbers,\n       maybe you don't need to use a ResponsiveContainer.",c,f),eQ(!n||n>0,"The aspect(%s) must be greater than zero.",n);var r=e$(c)?e:c,o=e$(f)?t:f;n&&n>0&&(r?o=r/n:o&&(r=o*n),y&&o>y&&(o=y)),eQ(r>0||o>0,"The width(%s) and height(%s) of chart should be greater than 0,\n       please check the style of container, or the props width(%s) and height(%s),\n       or add a minWidth(%s) or minHeight(%s) or use aspect(%s) to control the\n       height and width.",r,o,c,f,d,h,n);var i=!Array.isArray(m)&&tu(m.type).endsWith("Chart");return u().Children.map(m,function(e){return u().isValidElement(e)?(0,l.cloneElement)(e,tA({width:r,height:o},i?{style:tA({height:"100%",width:"100%",maxHeight:o,maxWidth:r},e.props.style)}:{})):e})},[n,m,f,y,h,d,A,c]);return u().createElement("div",{id:g?"".concat(g):void 0,className:(0,eT.A)("recharts-responsive-container",x),style:tA(tA({},void 0===j?{}:j),{},{width:c,height:f,minWidth:d,minHeight:h,maxHeight:y}),ref:O},N)}),tN=r(85938),tT=r.n(tN);function tM(e){return(tM="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function t_(){return(t_=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function tC(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function tD(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function tI(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?tD(Object(r),!0).forEach(function(t){var n,o,i;n=e,o=t,i=r[t],(o=function(e){var t=function(e,t){if("object"!=tM(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=tM(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==tM(t)?t:t+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):tD(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function tB(e){return Array.isArray(e)&&eW(e[0])&&eW(e[1])?e.join(" ~ "):e}var tR=function(e){var t=e.separator,r=void 0===t?" : ":t,n=e.contentStyle,o=e.itemStyle,i=void 0===o?{}:o,a=e.labelStyle,c=e.payload,s=e.formatter,l=e.itemSorter,f=e.wrapperClassName,p=e.labelClassName,d=e.label,h=e.labelFormatter,y=e.accessibilityLayer,m=tI({margin:0,padding:10,backgroundColor:"#fff",border:"1px solid #ccc",whiteSpace:"nowrap"},void 0===n?{}:n),v=tI({margin:0},void 0===a?{}:a),b=!e1()(d),g=b?d:"",x=(0,eT.A)("recharts-default-tooltip",f),w=(0,eT.A)("recharts-tooltip-label",p);return b&&h&&null!=c&&(g=h(d,c)),u().createElement("div",t_({className:x,style:m},void 0!==y&&y?{role:"status","aria-live":"assertive"}:{}),u().createElement("p",{className:w,style:v},u().isValidElement(g)?g:"".concat(g)),function(){if(c&&c.length){var e=(l?tT()(c,l):c).map(function(e,t){if("none"===e.type)return null;var n=tI({display:"block",paddingTop:4,paddingBottom:4,color:e.color||"#000"},i),o=e.formatter||s||tB,a=e.value,l=e.name,f=a,p=l;if(o&&null!=f&&null!=p){var d=o(a,l,e,t,c);if(Array.isArray(d)){var h=function(e){if(Array.isArray(e))return e}(d)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,c=[],s=!0,l=!1;try{i=(r=r.call(e)).next,!1;for(;!(s=(n=i.call(r)).done)&&(c.push(n.value),c.length!==t);s=!0);}catch(e){l=!0,o=e}finally{try{if(!s&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(d,2)||function(e,t){if(e){if("string"==typeof e)return tC(e,2);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return tC(e,t)}}(d,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}();f=h[0],p=h[1]}else f=d}return u().createElement("li",{className:"recharts-tooltip-item",key:"tooltip-item-".concat(t),style:n},eW(p)?u().createElement("span",{className:"recharts-tooltip-item-name"},p):null,eW(p)?u().createElement("span",{className:"recharts-tooltip-item-separator"},r):null,u().createElement("span",{className:"recharts-tooltip-item-value"},f),u().createElement("span",{className:"recharts-tooltip-item-unit"},e.unit||""))});return u().createElement("ul",{className:"recharts-tooltip-item-list",style:{padding:0,margin:0}},e)}return null}())};function tL(e){return(tL="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function tz(e,t,r){var n;return(n=function(e,t){if("object"!=tL(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=tL(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"),(t="symbol"==tL(n)?n:n+"")in e)?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var tF="recharts-tooltip-wrapper",tU={visibility:"hidden"};function t$(e){var t=e.allowEscapeViewBox,r=e.coordinate,n=e.key,o=e.offsetTopLeft,i=e.position,a=e.reverseDirection,c=e.tooltipDimension,s=e.viewBox,l=e.viewBoxDimension;if(i&&eq(i[n]))return i[n];var u=r[n]-c-o,f=r[n]+o;return t[n]?a[n]?u:f:a[n]?u<s[n]?Math.max(f,s[n]):Math.max(u,s[n]):f+c>s[n]+l?Math.max(u,s[n]):Math.max(f,s[n])}function tq(e){return(tq="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function tW(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function tH(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?tW(Object(r),!0).forEach(function(t){tK(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):tW(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function tX(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(tX=function(){return!!e})()}function tV(e){return(tV=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function tG(e,t){return(tG=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function tK(e,t,r){return(t=tY(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function tY(e){var t=function(e,t){if("object"!=tq(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=tq(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==tq(t)?t:t+""}var tZ=function(e){var t;function r(){var e,t,n;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");for(var o=arguments.length,i=Array(o),a=0;a<o;a++)i[a]=arguments[a];return t=r,n=[].concat(i),t=tV(t),tK(e=function(e,t){if(t&&("object"===tq(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");var r=e;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,tX()?Reflect.construct(t,n||[],tV(this).constructor):t.apply(this,n)),"state",{dismissed:!1,dismissedAtCoordinate:{x:0,y:0},lastBoundingBox:{width:-1,height:-1}}),tK(e,"handleKeyDown",function(t){if("Escape"===t.key){var r,n,o,i;e.setState({dismissed:!0,dismissedAtCoordinate:{x:null!=(r=null==(n=e.props.coordinate)?void 0:n.x)?r:0,y:null!=(o=null==(i=e.props.coordinate)?void 0:i.y)?o:0}})}}),e}if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(e&&e.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),e&&tG(r,e),t=[{key:"updateBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var e=this.wrapperNode.getBoundingClientRect();(Math.abs(e.width-this.state.lastBoundingBox.width)>1||Math.abs(e.height-this.state.lastBoundingBox.height)>1)&&this.setState({lastBoundingBox:{width:e.width,height:e.height}})}else(-1!==this.state.lastBoundingBox.width||-1!==this.state.lastBoundingBox.height)&&this.setState({lastBoundingBox:{width:-1,height:-1}})}},{key:"componentDidMount",value:function(){document.addEventListener("keydown",this.handleKeyDown),this.updateBBox()}},{key:"componentWillUnmount",value:function(){document.removeEventListener("keydown",this.handleKeyDown)}},{key:"componentDidUpdate",value:function(){var e,t;this.props.active&&this.updateBBox(),this.state.dismissed&&((null==(e=this.props.coordinate)?void 0:e.x)!==this.state.dismissedAtCoordinate.x||(null==(t=this.props.coordinate)?void 0:t.y)!==this.state.dismissedAtCoordinate.y)&&(this.state.dismissed=!1)}},{key:"render",value:function(){var e,t,r,n,o,i,a,c,s,l,f,p,d,h,y,m,v,b,g,x=this,w=this.props,j=w.active,O=w.allowEscapeViewBox,S=w.animationDuration,P=w.animationEasing,A=w.children,k=w.coordinate,E=w.hasPayload,N=w.isAnimationActive,T=w.offset,M=w.position,_=w.reverseDirection,C=w.useTranslate3d,D=w.viewBox,I=w.wrapperStyle,B=(p=(e={allowEscapeViewBox:O,coordinate:k,offsetTopLeft:T,position:M,reverseDirection:_,tooltipBox:this.state.lastBoundingBox,useTranslate3d:C,viewBox:D}).allowEscapeViewBox,d=e.coordinate,h=e.offsetTopLeft,y=e.position,m=e.reverseDirection,v=e.tooltipBox,b=e.useTranslate3d,g=e.viewBox,v.height>0&&v.width>0&&d?(r=(t={translateX:l=t$({allowEscapeViewBox:p,coordinate:d,key:"x",offsetTopLeft:h,position:y,reverseDirection:m,tooltipDimension:v.width,viewBox:g,viewBoxDimension:g.width}),translateY:f=t$({allowEscapeViewBox:p,coordinate:d,key:"y",offsetTopLeft:h,position:y,reverseDirection:m,tooltipDimension:v.height,viewBox:g,viewBoxDimension:g.height}),useTranslate3d:b}).translateX,n=t.translateY,s={transform:t.useTranslate3d?"translate3d(".concat(r,"px, ").concat(n,"px, 0)"):"translate(".concat(r,"px, ").concat(n,"px)")}):s=tU,{cssProperties:s,cssClasses:(i=(o={translateX:l,translateY:f,coordinate:d}).coordinate,a=o.translateX,c=o.translateY,(0,eT.A)(tF,tz(tz(tz(tz({},"".concat(tF,"-right"),eq(a)&&i&&eq(i.x)&&a>=i.x),"".concat(tF,"-left"),eq(a)&&i&&eq(i.x)&&a<i.x),"".concat(tF,"-bottom"),eq(c)&&i&&eq(i.y)&&c>=i.y),"".concat(tF,"-top"),eq(c)&&i&&eq(i.y)&&c<i.y)))}),R=B.cssClasses,L=B.cssProperties,z=tH(tH({transition:N&&j?"transform ".concat(S,"ms ").concat(P):void 0},L),{},{pointerEvents:"none",visibility:!this.state.dismissed&&j&&E?"visible":"hidden",position:"absolute",top:0,left:0},I);return u().createElement("div",{tabIndex:-1,className:R,style:z,ref:function(e){x.wrapperNode=e}},A)}}],function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,tY(n.key),n)}}(r.prototype,t),Object.defineProperty(r,"prototype",{writable:!1}),r}(l.PureComponent),tJ={isSsr:!0,get:function(e){return tJ[e]},set:function(e,t){if("string"==typeof e)tJ[e]=t;else{var r=Object.keys(e);r&&r.length&&r.forEach(function(t){tJ[t]=e[t]})}}},tQ=r(36315),t0=r.n(tQ);function t1(e,t,r){return!0===t?t0()(e,r):e5()(t)?t0()(e,t):e}function t2(e){return(t2="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function t5(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function t4(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?t5(Object(r),!0).forEach(function(t){t7(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):t5(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function t3(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(t3=function(){return!!e})()}function t6(e){return(t6=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function t8(e,t){return(t8=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function t7(e,t,r){return(t=t9(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function t9(e){var t=function(e,t){if("object"!=t2(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=t2(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==t2(t)?t:t+""}function re(e){return e.dataKey}var rt=function(e){var t;function r(){var e,t;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return e=r,t=arguments,e=t6(e),function(e,t){if(t&&("object"===t2(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");var r=e;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,t3()?Reflect.construct(e,t||[],t6(this).constructor):e.apply(this,t))}if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(e&&e.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),e&&t8(r,e),t=[{key:"render",value:function(){var e,t=this,r=this.props,n=r.active,o=r.allowEscapeViewBox,i=r.animationDuration,a=r.animationEasing,c=r.content,s=r.coordinate,l=r.filterNull,f=r.isAnimationActive,p=r.offset,d=r.payload,h=r.payloadUniqBy,y=r.position,m=r.reverseDirection,v=r.useTranslate3d,b=r.viewBox,g=r.wrapperStyle,x=null!=d?d:[];l&&x.length&&(x=t1(d.filter(function(e){return null!=e.value&&(!0!==e.hide||t.props.includeHidden)}),h,re));var w=x.length>0;return u().createElement(tZ,{allowEscapeViewBox:o,animationDuration:i,animationEasing:a,isAnimationActive:f,active:n,coordinate:s,hasPayload:w,offset:p,position:y,reverseDirection:m,useTranslate3d:v,viewBox:b,wrapperStyle:g},(e=t4(t4({},this.props),{},{payload:x}),u().isValidElement(c)?u().cloneElement(c,e):"function"==typeof c?u().createElement(c,e):u().createElement(tR,e)))}}],function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,t9(n.key),n)}}(r.prototype,t),Object.defineProperty(r,"prototype",{writable:!1}),r}(l.PureComponent);t7(rt,"displayName","Tooltip"),t7(rt,"defaultProps",{accessibilityLayer:!1,allowEscapeViewBox:{x:!1,y:!1},animationDuration:400,animationEasing:"ease",contentStyle:{},coordinate:{x:0,y:0},cursor:!0,cursorStyle:{},filterNull:!0,isAnimationActive:!tJ.isSsr,itemStyle:{},labelStyle:{},offset:10,reverseDirection:{x:!1,y:!1},separator:" : ",trigger:"hover",useTranslate3d:!1,viewBox:{x:0,y:0,height:0,width:0},wrapperStyle:{}});var rr=["children","width","height","viewBox","className","style","title","desc"];function rn(){return(rn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function ro(e){var t=e.children,r=e.width,n=e.height,o=e.viewBox,i=e.className,a=e.style,c=e.title,s=e.desc,l=function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,rr),f=o||{width:r,height:n,x:0,y:0},p=(0,eT.A)("recharts-surface",i);return u().createElement("svg",rn({},tg(l,!0,"svg"),{className:p,width:r,height:n,style:a,viewBox:"".concat(f.x," ").concat(f.y," ").concat(f.width," ").concat(f.height)}),u().createElement("title",null,c),u().createElement("desc",null,s),t)}var ri=r(69433),ra=r.n(ri);let rc=Math.cos,rs=Math.sin,rl=Math.sqrt,ru=Math.PI,rf=2*ru,rp={draw(e,t){let r=rl(t/ru);e.moveTo(r,0),e.arc(0,0,r,0,rf)}},rd=rl(1/3),rh=2*rd,ry=rs(ru/10)/rs(7*ru/10),rm=rs(rf/10)*ry,rv=-rc(rf/10)*ry,rb=rl(3),rg=rl(3)/2,rx=1/rl(12),rw=(rx/2+1)*3;function rj(e){return function(){return e}}let rO=Math.PI,rS=2*rO,rP=rS-1e-6;function rA(e){this._+=e[0];for(let t=1,r=e.length;t<r;++t)this._+=arguments[t]+e[t]}class rk{constructor(e){this._x0=this._y0=this._x1=this._y1=null,this._="",this._append=null==e?rA:function(e){let t=Math.floor(e);if(!(t>=0))throw Error(`invalid digits: ${e}`);if(t>15)return rA;let r=10**t;return function(e){this._+=e[0];for(let t=1,n=e.length;t<n;++t)this._+=Math.round(arguments[t]*r)/r+e[t]}}(e)}moveTo(e,t){this._append`M${this._x0=this._x1=+e},${this._y0=this._y1=+t}`}closePath(){null!==this._x1&&(this._x1=this._x0,this._y1=this._y0,this._append`Z`)}lineTo(e,t){this._append`L${this._x1=+e},${this._y1=+t}`}quadraticCurveTo(e,t,r,n){this._append`Q${+e},${+t},${this._x1=+r},${this._y1=+n}`}bezierCurveTo(e,t,r,n,o,i){this._append`C${+e},${+t},${+r},${+n},${this._x1=+o},${this._y1=+i}`}arcTo(e,t,r,n,o){if(e*=1,t*=1,r*=1,n*=1,(o*=1)<0)throw Error(`negative radius: ${o}`);let i=this._x1,a=this._y1,c=r-e,s=n-t,l=i-e,u=a-t,f=l*l+u*u;if(null===this._x1)this._append`M${this._x1=e},${this._y1=t}`;else if(f>1e-6)if(Math.abs(u*c-s*l)>1e-6&&o){let p=r-i,d=n-a,h=c*c+s*s,y=Math.sqrt(h),m=Math.sqrt(f),v=o*Math.tan((rO-Math.acos((h+f-(p*p+d*d))/(2*y*m)))/2),b=v/m,g=v/y;Math.abs(b-1)>1e-6&&this._append`L${e+b*l},${t+b*u}`,this._append`A${o},${o},0,0,${+(u*p>l*d)},${this._x1=e+g*c},${this._y1=t+g*s}`}else this._append`L${this._x1=e},${this._y1=t}`}arc(e,t,r,n,o,i){if(e*=1,t*=1,r*=1,i=!!i,r<0)throw Error(`negative radius: ${r}`);let a=r*Math.cos(n),c=r*Math.sin(n),s=e+a,l=t+c,u=1^i,f=i?n-o:o-n;null===this._x1?this._append`M${s},${l}`:(Math.abs(this._x1-s)>1e-6||Math.abs(this._y1-l)>1e-6)&&this._append`L${s},${l}`,r&&(f<0&&(f=f%rS+rS),f>rP?this._append`A${r},${r},0,1,${u},${e-a},${t-c}A${r},${r},0,1,${u},${this._x1=s},${this._y1=l}`:f>1e-6&&this._append`A${r},${r},0,${+(f>=rO)},${u},${this._x1=e+r*Math.cos(o)},${this._y1=t+r*Math.sin(o)}`)}rect(e,t,r,n){this._append`M${this._x0=this._x1=+e},${this._y0=this._y1=+t}h${r*=1}v${+n}h${-r}Z`}toString(){return this._}}function rE(e){let t=3;return e.digits=function(r){if(!arguments.length)return t;if(null==r)t=null;else{let e=Math.floor(r);if(!(e>=0))throw RangeError(`invalid digits: ${r}`);t=e}return e},()=>new rk(t)}function rN(e){return(rN="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}rk.prototype,rl(3),rl(3);var rT=["type","size","sizeType"];function rM(){return(rM=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function r_(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function rC(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?r_(Object(r),!0).forEach(function(t){var n,o,i;n=e,o=t,i=r[t],(o=function(e){var t=function(e,t){if("object"!=rN(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=rN(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==rN(t)?t:t+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):r_(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var rD={symbolCircle:rp,symbolCross:{draw(e,t){let r=rl(t/5)/2;e.moveTo(-3*r,-r),e.lineTo(-r,-r),e.lineTo(-r,-3*r),e.lineTo(r,-3*r),e.lineTo(r,-r),e.lineTo(3*r,-r),e.lineTo(3*r,r),e.lineTo(r,r),e.lineTo(r,3*r),e.lineTo(-r,3*r),e.lineTo(-r,r),e.lineTo(-3*r,r),e.closePath()}},symbolDiamond:{draw(e,t){let r=rl(t/rh),n=r*rd;e.moveTo(0,-r),e.lineTo(n,0),e.lineTo(0,r),e.lineTo(-n,0),e.closePath()}},symbolSquare:{draw(e,t){let r=rl(t),n=-r/2;e.rect(n,n,r,r)}},symbolStar:{draw(e,t){let r=rl(.8908130915292852*t),n=rm*r,o=rv*r;e.moveTo(0,-r),e.lineTo(n,o);for(let t=1;t<5;++t){let i=rf*t/5,a=rc(i),c=rs(i);e.lineTo(c*r,-a*r),e.lineTo(a*n-c*o,c*n+a*o)}e.closePath()}},symbolTriangle:{draw(e,t){let r=-rl(t/(3*rb));e.moveTo(0,2*r),e.lineTo(-rb*r,-r),e.lineTo(rb*r,-r),e.closePath()}},symbolWye:{draw(e,t){let r=rl(t/rw),n=r/2,o=r*rx,i=r*rx+r,a=-n;e.moveTo(n,o),e.lineTo(n,i),e.lineTo(a,i),e.lineTo(-.5*n-rg*o,rg*n+-.5*o),e.lineTo(-.5*n-rg*i,rg*n+-.5*i),e.lineTo(-.5*a-rg*i,rg*a+-.5*i),e.lineTo(-.5*n+rg*o,-.5*o-rg*n),e.lineTo(-.5*n+rg*i,-.5*i-rg*n),e.lineTo(-.5*a+rg*i,-.5*i-rg*a),e.closePath()}}},rI=Math.PI/180,rB=function(e,t,r){if("area"===t)return e;switch(r){case"cross":return 5*e*e/9;case"diamond":return .5*e*e/Math.sqrt(3);case"square":return e*e;case"star":var n=18*rI;return 1.25*e*e*(Math.tan(n)-Math.tan(2*n)*Math.pow(Math.tan(n),2));case"triangle":return Math.sqrt(3)*e*e/4;case"wye":return(21-10*Math.sqrt(3))*e*e/8;default:return Math.PI*e*e/4}},rR=function(e){var t,r=e.type,n=void 0===r?"circle":r,o=e.size,i=void 0===o?64:o,a=e.sizeType,c=void 0===a?"area":a,s=rC(rC({},function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,rT)),{},{type:n,size:i,sizeType:c}),l=s.className,f=s.cx,p=s.cy,d=tg(s,!0);return f===+f&&p===+p&&i===+i?u().createElement("path",rM({},d,{className:(0,eT.A)("recharts-symbols",l),transform:"translate(".concat(f,", ").concat(p,")"),d:(t=rD["symbol".concat(ra()(n))]||rp,(function(e,t){let r=null,n=rE(o);function o(){let o;if(r||(r=o=n()),e.apply(this,arguments).draw(r,+t.apply(this,arguments)),o)return r=null,o+""||null}return e="function"==typeof e?e:rj(e||rp),t="function"==typeof t?t:rj(void 0===t?64:+t),o.type=function(t){return arguments.length?(e="function"==typeof t?t:rj(t),o):e},o.size=function(e){return arguments.length?(t="function"==typeof e?e:rj(+e),o):t},o.context=function(e){return arguments.length?(r=null==e?null:e,o):r},o})().type(t).size(rB(i,c,n))())})):null};function rL(e){return(rL="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function rz(){return(rz=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function rF(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}rR.registerSymbol=function(e,t){rD["symbol".concat(ra()(e))]=t};function rU(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(rU=function(){return!!e})()}function r$(e){return(r$=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function rq(e,t){return(rq=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function rW(e,t,r){return(t=rH(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function rH(e){var t=function(e,t){if("object"!=rL(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=rL(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==rL(t)?t:t+""}var rX=function(e){var t;function r(){var e,t;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return e=r,t=arguments,e=r$(e),function(e,t){if(t&&("object"===rL(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");var r=e;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,rU()?Reflect.construct(e,t||[],r$(this).constructor):e.apply(this,t))}if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(e&&e.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),e&&rq(r,e),t=[{key:"renderIcon",value:function(e){var t=this.props.inactiveColor,r=32/6,n=32/3,o=e.inactive?t:e.color;if("plainline"===e.type)return u().createElement("line",{strokeWidth:4,fill:"none",stroke:o,strokeDasharray:e.payload.strokeDasharray,x1:0,y1:16,x2:32,y2:16,className:"recharts-legend-icon"});if("line"===e.type)return u().createElement("path",{strokeWidth:4,fill:"none",stroke:o,d:"M0,".concat(16,"h").concat(n,"\n            A").concat(r,",").concat(r,",0,1,1,").concat(2*n,",").concat(16,"\n            H").concat(32,"M").concat(2*n,",").concat(16,"\n            A").concat(r,",").concat(r,",0,1,1,").concat(n,",").concat(16),className:"recharts-legend-icon"});if("rect"===e.type)return u().createElement("path",{stroke:"none",fill:o,d:"M0,".concat(4,"h").concat(32,"v").concat(24,"h").concat(-32,"z"),className:"recharts-legend-icon"});if(u().isValidElement(e.legendIcon)){var i=function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?rF(Object(r),!0).forEach(function(t){rW(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):rF(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}({},e);return delete i.legendIcon,u().cloneElement(e.legendIcon,i)}return u().createElement(rR,{fill:o,cx:16,cy:16,size:32,sizeType:"diameter",type:e.type})}},{key:"renderItems",value:function(){var e=this,t=this.props,r=t.payload,n=t.iconSize,o=t.layout,i=t.formatter,a=t.inactiveColor,c={x:0,y:0,width:32,height:32},s={display:"horizontal"===o?"inline-block":"block",marginRight:10},l={display:"inline-block",verticalAlign:"middle",marginRight:4};return r.map(function(t,r){var o=t.formatter||i,f=(0,eT.A)(rW(rW({"recharts-legend-item":!0},"legend-item-".concat(r),!0),"inactive",t.inactive));if("none"===t.type)return null;var p=e5()(t.value)?null:t.value;eQ(!e5()(t.value),'The name property is also required when using a function for the dataKey of a chart\'s cartesian components. Ex: <Bar name="Name of my Data"/>');var d=t.inactive?a:t.color;return u().createElement("li",rz({className:f,style:s,key:"legend-item-".concat(r)},to(e.props,t,r)),u().createElement(ro,{width:n,height:n,viewBox:c,style:l},e.renderIcon(t)),u().createElement("span",{className:"recharts-legend-item-text",style:{color:d}},o?o(p,t,r):p))})}},{key:"render",value:function(){var e=this.props,t=e.payload,r=e.layout,n=e.align;return t&&t.length?u().createElement("ul",{className:"recharts-default-legend",style:{padding:0,margin:0,textAlign:"horizontal"===r?n:"left"}},this.renderItems()):null}}],function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,rH(n.key),n)}}(r.prototype,t),Object.defineProperty(r,"prototype",{writable:!1}),r}(l.PureComponent);function rV(e){return(rV="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}rW(rX,"displayName","Legend"),rW(rX,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"middle",inactiveColor:"#ccc"});var rG=["ref"];function rK(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function rY(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?rK(Object(r),!0).forEach(function(t){r1(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):rK(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function rZ(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,r2(n.key),n)}}function rJ(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(rJ=function(){return!!e})()}function rQ(e){return(rQ=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function r0(e,t){return(r0=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function r1(e,t,r){return(t=r2(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function r2(e){var t=function(e,t){if("object"!=rV(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=rV(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==rV(t)?t:t+""}function r5(e){return e.value}var r4=function(e){var t,r;function n(){var e,t,r;if(!(this instanceof n))throw TypeError("Cannot call a class as a function");for(var o=arguments.length,i=Array(o),a=0;a<o;a++)i[a]=arguments[a];return t=n,r=[].concat(i),t=rQ(t),r1(e=function(e,t){if(t&&("object"===rV(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");var r=e;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,rJ()?Reflect.construct(t,r||[],rQ(this).constructor):t.apply(this,r)),"lastBoundingBox",{width:-1,height:-1}),e}if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");return n.prototype=Object.create(e&&e.prototype,{constructor:{value:n,writable:!0,configurable:!0}}),Object.defineProperty(n,"prototype",{writable:!1}),e&&r0(n,e),t=[{key:"componentDidMount",value:function(){this.updateBBox()}},{key:"componentDidUpdate",value:function(){this.updateBBox()}},{key:"getBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var e=this.wrapperNode.getBoundingClientRect();return e.height=this.wrapperNode.offsetHeight,e.width=this.wrapperNode.offsetWidth,e}return null}},{key:"updateBBox",value:function(){var e=this.props.onBBoxUpdate,t=this.getBBox();t?(Math.abs(t.width-this.lastBoundingBox.width)>1||Math.abs(t.height-this.lastBoundingBox.height)>1)&&(this.lastBoundingBox.width=t.width,this.lastBoundingBox.height=t.height,e&&e(t)):(-1!==this.lastBoundingBox.width||-1!==this.lastBoundingBox.height)&&(this.lastBoundingBox.width=-1,this.lastBoundingBox.height=-1,e&&e(null))}},{key:"getBBoxSnapshot",value:function(){return this.lastBoundingBox.width>=0&&this.lastBoundingBox.height>=0?rY({},this.lastBoundingBox):{width:0,height:0}}},{key:"getDefaultPosition",value:function(e){var t,r,n=this.props,o=n.layout,i=n.align,a=n.verticalAlign,c=n.margin,s=n.chartWidth,l=n.chartHeight;return e&&(void 0!==e.left&&null!==e.left||void 0!==e.right&&null!==e.right)||(t="center"===i&&"vertical"===o?{left:((s||0)-this.getBBoxSnapshot().width)/2}:"right"===i?{right:c&&c.right||0}:{left:c&&c.left||0}),e&&(void 0!==e.top&&null!==e.top||void 0!==e.bottom&&null!==e.bottom)||(r="middle"===a?{top:((l||0)-this.getBBoxSnapshot().height)/2}:"bottom"===a?{bottom:c&&c.bottom||0}:{top:c&&c.top||0}),rY(rY({},t),r)}},{key:"render",value:function(){var e=this,t=this.props,r=t.content,n=t.width,o=t.height,i=t.wrapperStyle,a=t.payloadUniqBy,c=t.payload,s=rY(rY({position:"absolute",width:n||"auto",height:o||"auto"},this.getDefaultPosition(i)),i);return u().createElement("div",{className:"recharts-legend-wrapper",style:s,ref:function(t){e.wrapperNode=t}},function(e,t){if(u().isValidElement(e))return u().cloneElement(e,t);if("function"==typeof e)return u().createElement(e,t);t.ref;var r=function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(t,rG);return u().createElement(rX,r)}(r,rY(rY({},this.props),{},{payload:t1(c,a,r5)})))}}],r=[{key:"getWithHeight",value:function(e,t){var r=rY(rY({},this.defaultProps),e.props).layout;return"vertical"===r&&eq(e.props.height)?{height:e.props.height}:"horizontal"===r?{width:e.props.width||t}:null}}],t&&rZ(n.prototype,t),r&&rZ(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(l.PureComponent);r1(r4,"displayName","Legend"),r1(r4,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"bottom"});let r3={light:"",dark:".dark"},r6=l.createContext(null);function r8(){let e=l.useContext(r6);if(!e)throw Error("useChart must be used within a <ChartContainer />");return e}let r7=l.forwardRef(({id:e,className:t,children:r,config:n,...i},a)=>{let c=l.useId(),s=`chart-${e||c.replace(/:/g,"")}`;return(0,o.jsx)(r6.Provider,{value:{config:n},children:(0,o.jsxs)("div",{"data-chart":s,ref:a,className:(0,D.cn)("flex aspect-video justify-center text-xs [&_.recharts-cartesian-axis-tick_text]:fill-muted-foreground [&_.recharts-cartesian-grid_line[stroke='#ccc']]:stroke-border/50 [&_.recharts-curve.recharts-tooltip-cursor]:stroke-border [&_.recharts-dot[stroke='#fff']]:stroke-transparent [&_.recharts-layer]:outline-none [&_.recharts-polar-grid_[stroke='#ccc']]:stroke-border [&_.recharts-radial-bar-background-sector]:fill-muted [&_.recharts-rectangle.recharts-tooltip-cursor]:fill-muted [&_.recharts-reference-line_[stroke='#ccc']]:stroke-border [&_.recharts-sector[stroke='#fff']]:stroke-transparent [&_.recharts-sector]:outline-none [&_.recharts-surface]:outline-none",t),...i,children:[(0,o.jsx)(r9,{id:s,config:n}),(0,o.jsx)(tE,{children:r})]})})});r7.displayName="Chart";let r9=({id:e,config:t})=>{let r=Object.entries(t).filter(([,e])=>e.theme||e.color);return r.length?(0,o.jsx)("style",{dangerouslySetInnerHTML:{__html:Object.entries(r3).map(([t,n])=>`
${n} [data-chart=${e}] {
${r.map(([e,r])=>{let n=r.theme?.[t]||r.color;return n?`  --color-${e}: ${n};`:null}).join("\n")}
}
`).join("\n")}}):null},ne=l.forwardRef(({active:e,payload:t,className:r,indicator:n="dot",hideLabel:i=!1,hideIndicator:a=!1,label:c,labelFormatter:s,labelClassName:u,formatter:f,color:p,nameKey:d,labelKey:h},y)=>{let{config:m}=r8(),v=l.useMemo(()=>{if(i||!t?.length)return null;let[e]=t,r=`${h||e.dataKey||e.name||"value"}`,n=nr(m,e,r),a=h||"string"!=typeof c?n?.label:m[c]?.label||c;return s?(0,o.jsx)("div",{className:(0,D.cn)("font-medium",u),children:s(a,t)}):a?(0,o.jsx)("div",{className:(0,D.cn)("font-medium",u),children:a}):null},[c,s,t,i,u,m,h]);if(!e||!t?.length)return null;let b=1===t.length&&"dot"!==n;return(0,o.jsxs)("div",{ref:y,className:(0,D.cn)("grid min-w-[8rem] items-start gap-1.5 rounded-lg border border-border/50 bg-background px-2.5 py-1.5 text-xs shadow-xl",r),children:[b?null:v,(0,o.jsx)("div",{className:"grid gap-1.5",children:t.map((e,t)=>{let r=`${d||e.name||e.dataKey||"value"}`,i=nr(m,e,r),c=p||e.payload.fill||e.color;return(0,o.jsx)("div",{className:(0,D.cn)("flex w-full flex-wrap items-stretch gap-2 [&>svg]:h-2.5 [&>svg]:w-2.5 [&>svg]:text-muted-foreground","dot"===n&&"items-center"),children:f&&e?.value!==void 0&&e.name?f(e.value,e.name,e,t,e.payload):(0,o.jsxs)(o.Fragment,{children:[i?.icon?(0,o.jsx)(i.icon,{}):!a&&(0,o.jsx)("div",{className:(0,D.cn)("shrink-0 rounded-[2px] border-[--color-border] bg-[--color-bg]",{"h-2.5 w-2.5":"dot"===n,"w-1":"line"===n,"w-0 border-[1.5px] border-dashed bg-transparent":"dashed"===n,"my-0.5":b&&"dashed"===n}),style:{"--color-bg":c,"--color-border":c}}),(0,o.jsxs)("div",{className:(0,D.cn)("flex flex-1 justify-between leading-none",b?"items-end":"items-center"),children:[(0,o.jsxs)("div",{className:"grid gap-1.5",children:[b?v:null,(0,o.jsx)("span",{className:"text-muted-foreground",children:i?.label||e.name})]}),e.value&&(0,o.jsx)("span",{className:"font-mono font-medium tabular-nums text-foreground",children:e.value.toLocaleString()})]})]})},e.dataKey)})})]})});ne.displayName="ChartTooltip";let nt=l.forwardRef(({className:e,hideIcon:t=!1,payload:r,verticalAlign:n="bottom",nameKey:i},a)=>{let{config:c}=r8();return r?.length?(0,o.jsx)("div",{ref:a,className:(0,D.cn)("flex items-center justify-center gap-4","top"===n?"pb-3":"pt-3",e),children:r.map(e=>{let r=`${i||e.dataKey||"value"}`,n=nr(c,e,r);return(0,o.jsxs)("div",{className:(0,D.cn)("flex items-center gap-1.5 [&>svg]:h-3 [&>svg]:w-3 [&>svg]:text-muted-foreground"),children:[n?.icon&&!t?(0,o.jsx)(n.icon,{}):(0,o.jsx)("div",{className:"h-2 w-2 shrink-0 rounded-[2px]",style:{backgroundColor:e.color}}),n?.label]},e.value)})}):null});function nr(e,t,r){if("object"!=typeof t||null===t)return;let n="payload"in t&&"object"==typeof t.payload&&null!==t.payload?t.payload:void 0,o=r;return r in t&&"string"==typeof t[r]?o=t[r]:n&&r in n&&"string"==typeof n[r]&&(o=n[r]),o in e?e[o]:e[r]}nt.displayName="ChartLegend";var nn=r(34990),no=r.n(nn);function ni(e,t){if(!e)throw Error("Invariant failed")}var na=["children","className"];function nc(){return(nc=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}var ns=u().forwardRef(function(e,t){var r=e.children,n=e.className,o=function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,na),i=(0,eT.A)("recharts-layer",n);return u().createElement("g",nc({className:i},tg(o,!0),{ref:t}),r)});function nl(){return(nl=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}var nu=function(e){var t=e.cx,r=e.cy,n=e.r,o=e.className,i=(0,eT.A)("recharts-dot",o);return t===+t&&r===+r&&n===+n?u().createElement("circle",nl({},tg(e,!1),tn(e),{className:i,cx:t,cy:r,r:n})):null},nf=r(87955),np=r.n(nf),nd=Object.getOwnPropertyNames,nh=Object.getOwnPropertySymbols,ny=Object.prototype.hasOwnProperty;function nm(e,t){return function(r,n,o){return e(r,n,o)&&t(r,n,o)}}function nv(e){return function(t,r,n){if(!t||!r||"object"!=typeof t||"object"!=typeof r)return e(t,r,n);var o=n.cache,i=o.get(t),a=o.get(r);if(i&&a)return i===r&&a===t;o.set(t,r),o.set(r,t);var c=e(t,r,n);return o.delete(t),o.delete(r),c}}function nb(e){return nd(e).concat(nh(e))}var ng=Object.hasOwn||function(e,t){return ny.call(e,t)};function nx(e,t){return e===t||!e&&!t&&e!=e&&t!=t}var nw=Object.getOwnPropertyDescriptor,nj=Object.keys;function nO(e,t,r){var n=e.length;if(t.length!==n)return!1;for(;n-- >0;)if(!r.equals(e[n],t[n],n,n,e,t,r))return!1;return!0}function nS(e,t){return nx(e.getTime(),t.getTime())}function nP(e,t){return e.name===t.name&&e.message===t.message&&e.cause===t.cause&&e.stack===t.stack}function nA(e,t){return e===t}function nk(e,t,r){var n,o,i=e.size;if(i!==t.size)return!1;if(!i)return!0;for(var a=Array(i),c=e.entries(),s=0;(n=c.next())&&!n.done;){for(var l=t.entries(),u=!1,f=0;(o=l.next())&&!o.done;){if(a[f]){f++;continue}var p=n.value,d=o.value;if(r.equals(p[0],d[0],s,f,e,t,r)&&r.equals(p[1],d[1],p[0],d[0],e,t,r)){u=a[f]=!0;break}f++}if(!u)return!1;s++}return!0}function nE(e,t,r){var n=nj(e),o=n.length;if(nj(t).length!==o)return!1;for(;o-- >0;)if(!nI(e,t,r,n[o]))return!1;return!0}function nN(e,t,r){var n,o,i,a=nb(e),c=a.length;if(nb(t).length!==c)return!1;for(;c-- >0;)if(!nI(e,t,r,n=a[c])||(o=nw(e,n),i=nw(t,n),(o||i)&&(!o||!i||o.configurable!==i.configurable||o.enumerable!==i.enumerable||o.writable!==i.writable)))return!1;return!0}function nT(e,t){return nx(e.valueOf(),t.valueOf())}function nM(e,t){return e.source===t.source&&e.flags===t.flags}function n_(e,t,r){var n,o,i=e.size;if(i!==t.size)return!1;if(!i)return!0;for(var a=Array(i),c=e.values();(n=c.next())&&!n.done;){for(var s=t.values(),l=!1,u=0;(o=s.next())&&!o.done;){if(!a[u]&&r.equals(n.value,o.value,n.value,o.value,e,t,r)){l=a[u]=!0;break}u++}if(!l)return!1}return!0}function nC(e,t){var r=e.length;if(t.length!==r)return!1;for(;r-- >0;)if(e[r]!==t[r])return!1;return!0}function nD(e,t){return e.hostname===t.hostname&&e.pathname===t.pathname&&e.protocol===t.protocol&&e.port===t.port&&e.hash===t.hash&&e.username===t.username&&e.password===t.password}function nI(e,t,r,n){return("_owner"===n||"__o"===n||"__v"===n)&&(!!e.$$typeof||!!t.$$typeof)||ng(t,n)&&r.equals(e[n],t[n],n,n,e,t,r)}var nB=Array.isArray,nR="function"==typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView:null,nL=Object.assign,nz=Object.prototype.toString.call.bind(Object.prototype.toString),nF=nU();function nU(e){void 0===e&&(e={});var t,r,n,o,i,a,c,s,l,u,f,p,d,h=e.circular,y=e.createInternalComparator,m=e.createState,v=e.strict,b=(r=(t=function(e){var t=e.circular,r=e.createCustomConfig,n=e.strict,o={areArraysEqual:n?nN:nO,areDatesEqual:nS,areErrorsEqual:nP,areFunctionsEqual:nA,areMapsEqual:n?nm(nk,nN):nk,areNumbersEqual:nx,areObjectsEqual:n?nN:nE,arePrimitiveWrappersEqual:nT,areRegExpsEqual:nM,areSetsEqual:n?nm(n_,nN):n_,areTypedArraysEqual:n?nN:nC,areUrlsEqual:nD};if(r&&(o=nL({},o,r(o))),t){var i=nv(o.areArraysEqual),a=nv(o.areMapsEqual),c=nv(o.areObjectsEqual),s=nv(o.areSetsEqual);o=nL({},o,{areArraysEqual:i,areMapsEqual:a,areObjectsEqual:c,areSetsEqual:s})}return o}(e)).areArraysEqual,n=t.areDatesEqual,o=t.areErrorsEqual,i=t.areFunctionsEqual,a=t.areMapsEqual,c=t.areNumbersEqual,s=t.areObjectsEqual,l=t.arePrimitiveWrappersEqual,u=t.areRegExpsEqual,f=t.areSetsEqual,p=t.areTypedArraysEqual,d=t.areUrlsEqual,function(e,t,h){if(e===t)return!0;if(null==e||null==t)return!1;var y=typeof e;if(y!==typeof t)return!1;if("object"!==y)return"number"===y?c(e,t,h):"function"===y&&i(e,t,h);var m=e.constructor;if(m!==t.constructor)return!1;if(m===Object)return s(e,t,h);if(nB(e))return r(e,t,h);if(null!=nR&&nR(e))return p(e,t,h);if(m===Date)return n(e,t,h);if(m===RegExp)return u(e,t,h);if(m===Map)return a(e,t,h);if(m===Set)return f(e,t,h);var v=nz(e);return"[object Date]"===v?n(e,t,h):"[object RegExp]"===v?u(e,t,h):"[object Map]"===v?a(e,t,h):"[object Set]"===v?f(e,t,h):"[object Object]"===v?"function"!=typeof e.then&&"function"!=typeof t.then&&s(e,t,h):"[object URL]"===v?d(e,t,h):"[object Error]"===v?o(e,t,h):"[object Arguments]"===v?s(e,t,h):("[object Boolean]"===v||"[object Number]"===v||"[object String]"===v)&&l(e,t,h)}),g=y?y(b):function(e,t,r,n,o,i,a){return b(e,t,a)};return function(e){var t=e.circular,r=e.comparator,n=e.createState,o=e.equals,i=e.strict;if(n)return function(e,a){var c=n(),s=c.cache;return r(e,a,{cache:void 0===s?t?new WeakMap:void 0:s,equals:o,meta:c.meta,strict:i})};if(t)return function(e,t){return r(e,t,{cache:new WeakMap,equals:o,meta:void 0,strict:i})};var a={cache:void 0,equals:o,meta:void 0,strict:i};return function(e,t){return r(e,t,a)}}({circular:void 0!==h&&h,comparator:b,createState:m,equals:g,strict:void 0!==v&&v})}function n$(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=-1;requestAnimationFrame(function n(o){if(r<0&&(r=o),o-r>t)e(o),r=-1;else{var i;i=n,"undefined"!=typeof requestAnimationFrame&&requestAnimationFrame(i)}})}function nq(e){return(nq="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function nW(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function nH(e){return(nH="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function nX(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function nV(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?nX(Object(r),!0).forEach(function(t){nG(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):nX(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function nG(e,t,r){var n;return(n=function(e,t){if("object"!==nH(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!==nH(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"),(t="symbol"===nH(n)?n:String(n))in e)?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}nU({strict:!0}),nU({circular:!0}),nU({circular:!0,strict:!0}),nU({createInternalComparator:function(){return nx}}),nU({strict:!0,createInternalComparator:function(){return nx}}),nU({circular:!0,createInternalComparator:function(){return nx}}),nU({circular:!0,createInternalComparator:function(){return nx},strict:!0});var nK=function(e){return e},nY=function(e,t){return Object.keys(t).reduce(function(r,n){return nV(nV({},r),{},nG({},n,e(n,t[n])))},{})},nZ=function(e,t,r){return e.map(function(e){return"".concat(e.replace(/([A-Z])/g,function(e){return"-".concat(e.toLowerCase())})," ").concat(t,"ms ").concat(r)}).join(",")},nJ=function(e,t,r,n,o,i,a,c){};function nQ(e,t){if(e){if("string"==typeof e)return n0(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return n0(e,t)}}function n0(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}var n1=function(e,t){return[0,3*e,3*t-6*e,3*e-3*t+1]},n2=function(e,t){return e.map(function(e,r){return e*Math.pow(t,r)}).reduce(function(e,t){return e+t})},n5=function(e,t){return function(r){return n2(n1(e,t),r)}},n4=function(){for(var e,t,r=arguments.length,n=Array(r),o=0;o<r;o++)n[o]=arguments[o];var i=n[0],a=n[1],c=n[2],s=n[3];if(1===n.length)switch(n[0]){case"linear":i=0,a=0,c=1,s=1;break;case"ease":i=.25,a=.1,c=.25,s=1;break;case"ease-in":i=.42,a=0,c=1,s=1;break;case"ease-out":i=.42,a=0,c=.58,s=1;break;case"ease-in-out":i=0,a=0,c=.58,s=1;break;default:var l=n[0].split("(");if("cubic-bezier"===l[0]&&4===l[1].split(")")[0].split(",").length){var u,f=function(e){if(Array.isArray(e))return e}(u=l[1].split(")")[0].split(",").map(function(e){return parseFloat(e)}))||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,c=[],s=!0,l=!1;try{i=(r=r.call(e)).next,!1;for(;!(s=(n=i.call(r)).done)&&(c.push(n.value),c.length!==t);s=!0);}catch(e){l=!0,o=e}finally{try{if(!s&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(u,4)||nQ(u,4)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}();i=f[0],a=f[1],c=f[2],s=f[3]}else nJ(!1,"[configBezier]: arguments should be one of oneOf 'linear', 'ease', 'ease-in', 'ease-out', 'ease-in-out','cubic-bezier(x1,y1,x2,y2)', instead received %s",n)}nJ([i,c,a,s].every(function(e){return"number"==typeof e&&e>=0&&e<=1}),"[configBezier]: arguments should be x1, y1, x2, y2 of [0, 1] instead received %s",n);var p=n5(i,c),d=n5(a,s),h=(e=i,t=c,function(r){var n;return n2([].concat(function(e){if(Array.isArray(e))return n0(e)}(n=n1(e,t).map(function(e,t){return e*t}).slice(1))||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(n)||nQ(n)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),[0]),r)}),y=function(e){for(var t=e>1?1:e,r=t,n=0;n<8;++n){var o,i=p(r)-t,a=h(r);if(1e-4>Math.abs(i-t)||a<1e-4)break;r=(o=r-i/a)>1?1:o<0?0:o}return d(r)};return y.isStepper=!1,y},n3=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.stiff,r=void 0===t?100:t,n=e.damping,o=void 0===n?8:n,i=e.dt,a=void 0===i?17:i,c=function(e,t,n){var i=n+(-(e-t)*r-n*o)*a/1e3,c=n*a/1e3+e;return 1e-4>Math.abs(c-t)&&1e-4>Math.abs(i)?[t,0]:[c,i]};return c.isStepper=!0,c.dt=a,c},n6=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];var n=t[0];if("string"==typeof n)switch(n){case"ease":case"ease-in-out":case"ease-out":case"ease-in":case"linear":return n4(n);case"spring":return n3();default:if("cubic-bezier"===n.split("(")[0])return n4(n);nJ(!1,"[configEasing]: first argument should be one of 'ease', 'ease-in', 'ease-out', 'ease-in-out','cubic-bezier(x1,y1,x2,y2)', 'linear' and 'spring', instead  received %s",t)}return"function"==typeof n?n:(nJ(!1,"[configEasing]: first argument type should be function or string, instead received %s",t),null)};function n8(e){return(n8="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function n7(e){return function(e){if(Array.isArray(e))return on(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||or(e)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function n9(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function oe(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?n9(Object(r),!0).forEach(function(t){ot(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):n9(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function ot(e,t,r){var n;return(n=function(e,t){if("object"!==n8(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!==n8(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"),(t="symbol"===n8(n)?n:String(n))in e)?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function or(e,t){if(e){if("string"==typeof e)return on(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return on(e,t)}}function on(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}var oo=function(e,t,r){return e+(t-e)*r},oi=function(e){return e.from!==e.to},oa=function e(t,r,n){var o=nY(function(e,r){if(oi(r)){var n,o=function(e){if(Array.isArray(e))return e}(n=t(r.from,r.to,r.velocity))||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,c=[],s=!0,l=!1;try{i=(r=r.call(e)).next,!1;for(;!(s=(n=i.call(r)).done)&&(c.push(n.value),c.length!==t);s=!0);}catch(e){l=!0,o=e}finally{try{if(!s&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(n,2)||or(n,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),i=o[0],a=o[1];return oe(oe({},r),{},{from:i,velocity:a})}return r},r);return n<1?nY(function(e,t){return oi(t)?oe(oe({},t),{},{velocity:oo(t.velocity,o[e].velocity,n),from:oo(t.from,o[e].from,n)}):t},r):e(t,o,n-1)};let oc=function(e,t,r,n,o){var i,a,c=[Object.keys(e),Object.keys(t)].reduce(function(e,t){return e.filter(function(e){return t.includes(e)})}),s=c.reduce(function(r,n){return oe(oe({},r),{},ot({},n,[e[n],t[n]]))},{}),l=c.reduce(function(r,n){return oe(oe({},r),{},ot({},n,{from:e[n],velocity:0,to:t[n]}))},{}),u=-1,f=function(){return null};return f=r.isStepper?function(n){i||(i=n);var a=(n-i)/r.dt;l=oa(r,l,a),o(oe(oe(oe({},e),t),nY(function(e,t){return t.from},l))),i=n,Object.values(l).filter(oi).length&&(u=requestAnimationFrame(f))}:function(i){a||(a=i);var c=(i-a)/n,l=nY(function(e,t){return oo.apply(void 0,n7(t).concat([r(c)]))},s);if(o(oe(oe(oe({},e),t),l)),c<1)u=requestAnimationFrame(f);else{var p=nY(function(e,t){return oo.apply(void 0,n7(t).concat([r(1)]))},s);o(oe(oe(oe({},e),t),p))}},function(){return requestAnimationFrame(f),function(){cancelAnimationFrame(u)}}};function os(e){return(os="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var ol=["children","begin","duration","attributeName","easing","isActive","steps","from","to","canBegin","onAnimationEnd","shouldReAnimate","onAnimationReStart"];function ou(e){return function(e){if(Array.isArray(e))return of(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return of(e,void 0);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return of(e,t)}}(e)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function of(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function op(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function od(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?op(Object(r),!0).forEach(function(t){oh(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):op(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function oh(e,t,r){return(t=oy(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function oy(e){var t=function(e,t){if("object"!==os(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!==os(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===os(t)?t:String(t)}function om(e,t){return(om=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function ov(e,t){if(t&&("object"===os(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return ob(e)}function ob(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function og(e){return(og=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}var ox=function(e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");o.prototype=Object.create(e&&e.prototype,{constructor:{value:o,writable:!0,configurable:!0}}),Object.defineProperty(o,"prototype",{writable:!1}),e&&om(o,e);var t,r,n=(t=function(){if("undefined"==typeof Reflect||!Reflect.construct||Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(e){return!1}}(),function(){var e,r=og(o);return e=t?Reflect.construct(r,arguments,og(this).constructor):r.apply(this,arguments),ov(this,e)});function o(e,t){if(!(this instanceof o))throw TypeError("Cannot call a class as a function");var r=n.call(this,e,t),i=r.props,a=i.isActive,c=i.attributeName,s=i.from,l=i.to,u=i.steps,f=i.children,p=i.duration;if(r.handleStyleChange=r.handleStyleChange.bind(ob(r)),r.changeStyle=r.changeStyle.bind(ob(r)),!a||p<=0)return r.state={style:{}},"function"==typeof f&&(r.state={style:l}),ov(r);if(u&&u.length)r.state={style:u[0].style};else if(s){if("function"==typeof f)return r.state={style:s},ov(r);r.state={style:c?oh({},c,s):s}}else r.state={style:{}};return r}return r=[{key:"componentDidMount",value:function(){var e=this.props,t=e.isActive,r=e.canBegin;this.mounted=!0,t&&r&&this.runAnimation(this.props)}},{key:"componentDidUpdate",value:function(e){var t=this.props,r=t.isActive,n=t.canBegin,o=t.attributeName,i=t.shouldReAnimate,a=t.to,c=t.from,s=this.state.style;if(n){if(!r){var l={style:o?oh({},o,a):a};this.state&&s&&(o&&s[o]!==a||!o&&s!==a)&&this.setState(l);return}if(!nF(e.to,a)||!e.canBegin||!e.isActive){var u=!e.canBegin||!e.isActive;this.manager&&this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation();var f=u||i?c:e.to;if(this.state&&s){var p={style:o?oh({},o,f):f};(o&&s[o]!==f||!o&&s!==f)&&this.setState(p)}this.runAnimation(od(od({},this.props),{},{from:f,begin:0}))}}}},{key:"componentWillUnmount",value:function(){this.mounted=!1;var e=this.props.onAnimationEnd;this.unSubscribe&&this.unSubscribe(),this.manager&&(this.manager.stop(),this.manager=null),this.stopJSAnimation&&this.stopJSAnimation(),e&&e()}},{key:"handleStyleChange",value:function(e){this.changeStyle(e)}},{key:"changeStyle",value:function(e){this.mounted&&this.setState({style:e})}},{key:"runJSAnimation",value:function(e){var t=this,r=e.from,n=e.to,o=e.duration,i=e.easing,a=e.begin,c=e.onAnimationEnd,s=e.onAnimationStart,l=oc(r,n,n6(i),o,this.changeStyle);this.manager.start([s,a,function(){t.stopJSAnimation=l()},o,c])}},{key:"runStepAnimation",value:function(e){var t=this,r=e.steps,n=e.begin,o=e.onAnimationStart,i=r[0],a=i.style,c=i.duration;return this.manager.start([o].concat(ou(r.reduce(function(e,n,o){if(0===o)return e;var i=n.duration,a=n.easing,c=void 0===a?"ease":a,s=n.style,l=n.properties,u=n.onAnimationEnd,f=o>0?r[o-1]:n,p=l||Object.keys(s);if("function"==typeof c||"spring"===c)return[].concat(ou(e),[t.runJSAnimation.bind(t,{from:f.style,to:s,duration:i,easing:c}),i]);var d=nZ(p,i,c),h=od(od(od({},f.style),s),{},{transition:d});return[].concat(ou(e),[h,i,u]).filter(nK)},[a,Math.max(void 0===c?0:c,n)])),[e.onAnimationEnd]))}},{key:"runAnimation",value:function(e){this.manager||(this.manager=(r=function(){return null},n=!1,o=function e(t){if(!n){if(Array.isArray(t)){if(!t.length)return;var o=function(e){if(Array.isArray(e))return e}(t)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(t)||function(e,t){if(e){if("string"==typeof e)return nW(e,void 0);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return nW(e,t)}}(t)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),i=o[0],a=o.slice(1);return"number"==typeof i?void n$(e.bind(null,a),i):(e(i),void n$(e.bind(null,a)))}"object"===nq(t)&&r(t),"function"==typeof t&&t()}},{stop:function(){n=!0},start:function(e){n=!1,o(e)},subscribe:function(e){return r=e,function(){r=function(){return null}}}}));var t,r,n,o,i=e.begin,a=e.duration,c=e.attributeName,s=e.to,l=e.easing,u=e.onAnimationStart,f=e.onAnimationEnd,p=e.steps,d=e.children,h=this.manager;if(this.unSubscribe=h.subscribe(this.handleStyleChange),"function"==typeof l||"function"==typeof d||"spring"===l)return void this.runJSAnimation(e);if(p.length>1)return void this.runStepAnimation(e);var y=c?oh({},c,s):s,m=nZ(Object.keys(y),a,l);h.start([u,i,od(od({},y),{},{transition:m}),a,f])}},{key:"render",value:function(){var e=this.props,t=e.children,r=(e.begin,e.duration),n=(e.attributeName,e.easing,e.isActive),o=(e.steps,e.from,e.to,e.canBegin,e.onAnimationEnd,e.shouldReAnimate,e.onAnimationReStart,function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r,n,o={},i=Object.keys(e);for(n=0;n<i.length;n++)r=i[n],t.indexOf(r)>=0||(o[r]=e[r]);return o}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,ol)),i=l.Children.count(t),a=this.state.style;if("function"==typeof t)return t(a);if(!n||0===i||r<=0)return t;var c=function(e){var t=e.props,r=t.style,n=t.className;return(0,l.cloneElement)(e,od(od({},o),{},{style:od(od({},void 0===r?{}:r),a),className:n}))};return 1===i?c(l.Children.only(t)):u().createElement("div",null,l.Children.map(t,function(e){return c(e)}))}}],function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,oy(n.key),n)}}(o.prototype,r),Object.defineProperty(o,"prototype",{writable:!1}),o}(l.PureComponent);function ow(e){return(ow="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function oj(){return(oj=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function oO(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function oS(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function oP(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?oS(Object(r),!0).forEach(function(t){var n,o,i;n=e,o=t,i=r[t],(o=function(e){var t=function(e,t){if("object"!=ow(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=ow(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==ow(t)?t:t+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):oS(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}ox.displayName="Animate",ox.defaultProps={begin:0,duration:1e3,from:"",to:"",attributeName:"",easing:"ease",isActive:!0,canBegin:!0,steps:[],onAnimationEnd:function(){},onAnimationStart:function(){}},ox.propTypes={from:np().oneOfType([np().object,np().string]),to:np().oneOfType([np().object,np().string]),attributeName:np().string,duration:np().number,begin:np().number,easing:np().oneOfType([np().string,np().func]),steps:np().arrayOf(np().shape({duration:np().number.isRequired,style:np().object.isRequired,easing:np().oneOfType([np().oneOf(["ease","ease-in","ease-out","ease-in-out","linear"]),np().func]),properties:np().arrayOf("string"),onAnimationEnd:np().func})),children:np().oneOfType([np().node,np().func]),isActive:np().bool,canBegin:np().bool,onAnimationEnd:np().func,shouldReAnimate:np().bool,onAnimationStart:np().func,onAnimationReStart:np().func};var oA=function(e,t,r,n,o){var i,a=Math.min(Math.abs(r)/2,Math.abs(n)/2),c=n>=0?1:-1,s=r>=0?1:-1,l=+(n>=0&&r>=0||n<0&&r<0);if(a>0&&o instanceof Array){for(var u=[0,0,0,0],f=0;f<4;f++)u[f]=o[f]>a?a:o[f];i="M".concat(e,",").concat(t+c*u[0]),u[0]>0&&(i+="A ".concat(u[0],",").concat(u[0],",0,0,").concat(l,",").concat(e+s*u[0],",").concat(t)),i+="L ".concat(e+r-s*u[1],",").concat(t),u[1]>0&&(i+="A ".concat(u[1],",").concat(u[1],",0,0,").concat(l,",\n        ").concat(e+r,",").concat(t+c*u[1])),i+="L ".concat(e+r,",").concat(t+n-c*u[2]),u[2]>0&&(i+="A ".concat(u[2],",").concat(u[2],",0,0,").concat(l,",\n        ").concat(e+r-s*u[2],",").concat(t+n)),i+="L ".concat(e+s*u[3],",").concat(t+n),u[3]>0&&(i+="A ".concat(u[3],",").concat(u[3],",0,0,").concat(l,",\n        ").concat(e,",").concat(t+n-c*u[3])),i+="Z"}else if(a>0&&o===+o&&o>0){var p=Math.min(a,o);i="M ".concat(e,",").concat(t+c*p,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(e+s*p,",").concat(t,"\n            L ").concat(e+r-s*p,",").concat(t,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(e+r,",").concat(t+c*p,"\n            L ").concat(e+r,",").concat(t+n-c*p,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(e+r-s*p,",").concat(t+n,"\n            L ").concat(e+s*p,",").concat(t+n,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(e,",").concat(t+n-c*p," Z")}else i="M ".concat(e,",").concat(t," h ").concat(r," v ").concat(n," h ").concat(-r," Z");return i},ok=function(e,t){if(!e||!t)return!1;var r=e.x,n=e.y,o=t.x,i=t.y,a=t.width,c=t.height;if(Math.abs(a)>0&&Math.abs(c)>0){var s=Math.min(o,o+a),l=Math.max(o,o+a),u=Math.min(i,i+c),f=Math.max(i,i+c);return r>=s&&r<=l&&n>=u&&n<=f}return!1},oE={x:0,y:0,width:0,height:0,radius:0,isAnimationActive:!1,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},oN=function(e){var t,r=oP(oP({},oE),e),n=(0,l.useRef)(),o=function(e){if(Array.isArray(e))return e}(t=(0,l.useState)(-1))||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,c=[],s=!0,l=!1;try{i=(r=r.call(e)).next,!1;for(;!(s=(n=i.call(r)).done)&&(c.push(n.value),c.length!==t);s=!0);}catch(e){l=!0,o=e}finally{try{if(!s&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(t,2)||function(e,t){if(e){if("string"==typeof e)return oO(e,2);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return oO(e,t)}}(t,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),i=o[0],a=o[1];(0,l.useEffect)(function(){if(n.current&&n.current.getTotalLength)try{var e=n.current.getTotalLength();e&&a(e)}catch(e){}},[]);var c=r.x,s=r.y,f=r.width,p=r.height,d=r.radius,h=r.className,y=r.animationEasing,m=r.animationDuration,v=r.animationBegin,b=r.isAnimationActive,g=r.isUpdateAnimationActive;if(c!==+c||s!==+s||f!==+f||p!==+p||0===f||0===p)return null;var x=(0,eT.A)("recharts-rectangle",h);return g?u().createElement(ox,{canBegin:i>0,from:{width:f,height:p,x:c,y:s},to:{width:f,height:p,x:c,y:s},duration:m,animationEasing:y,isActive:g},function(e){var t=e.width,o=e.height,a=e.x,c=e.y;return u().createElement(ox,{canBegin:i>0,from:"0px ".concat(-1===i?1:i,"px"),to:"".concat(i,"px 0px"),attributeName:"strokeDasharray",begin:v,duration:m,isActive:b,easing:y},u().createElement("path",oj({},tg(r,!0),{className:x,d:oA(a,c,t,o,d),ref:n})))}):u().createElement("path",oj({},tg(r,!0),{className:x,d:oA(c,s,f,p,d)}))};function oT(e,t){switch(arguments.length){case 0:break;case 1:this.range(e);break;default:this.range(t).domain(e)}return this}function oM(e,t){switch(arguments.length){case 0:break;case 1:"function"==typeof e?this.interpolator(e):this.range(e);break;default:this.domain(e),"function"==typeof t?this.interpolator(t):this.range(t)}return this}class o_ extends Map{constructor(e,t=oD){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:t}}),null!=e)for(let[t,r]of e)this.set(t,r)}get(e){return super.get(oC(this,e))}has(e){return super.has(oC(this,e))}set(e,t){return super.set(function({_intern:e,_key:t},r){let n=t(r);return e.has(n)?e.get(n):(e.set(n,r),r)}(this,e),t)}delete(e){return super.delete(function({_intern:e,_key:t},r){let n=t(r);return e.has(n)&&(r=e.get(n),e.delete(n)),r}(this,e))}}function oC({_intern:e,_key:t},r){let n=t(r);return e.has(n)?e.get(n):r}function oD(e){return null!==e&&"object"==typeof e?e.valueOf():e}let oI=Symbol("implicit");function oB(){var e=new o_,t=[],r=[],n=oI;function o(o){let i=e.get(o);if(void 0===i){if(n!==oI)return n;e.set(o,i=t.push(o)-1)}return r[i%r.length]}return o.domain=function(r){if(!arguments.length)return t.slice();for(let n of(t=[],e=new o_,r))e.has(n)||e.set(n,t.push(n)-1);return o},o.range=function(e){return arguments.length?(r=Array.from(e),o):r.slice()},o.unknown=function(e){return arguments.length?(n=e,o):n},o.copy=function(){return oB(t,r).unknown(n)},oT.apply(o,arguments),o}function oR(){var e,t,r=oB().unknown(void 0),n=r.domain,o=r.range,i=0,a=1,c=!1,s=0,l=0,u=.5;function f(){var r=n().length,f=a<i,p=f?a:i,d=f?i:a;e=(d-p)/Math.max(1,r-s+2*l),c&&(e=Math.floor(e)),p+=(d-p-e*(r-s))*u,t=e*(1-s),c&&(p=Math.round(p),t=Math.round(t));var h=(function(e,t,r){e*=1,t*=1,r=(o=arguments.length)<2?(t=e,e=0,1):o<3?1:+r;for(var n=-1,o=0|Math.max(0,Math.ceil((t-e)/r)),i=Array(o);++n<o;)i[n]=e+n*r;return i})(r).map(function(t){return p+e*t});return o(f?h.reverse():h)}return delete r.unknown,r.domain=function(e){return arguments.length?(n(e),f()):n()},r.range=function(e){return arguments.length?([i,a]=e,i*=1,a*=1,f()):[i,a]},r.rangeRound=function(e){return[i,a]=e,i*=1,a*=1,c=!0,f()},r.bandwidth=function(){return t},r.step=function(){return e},r.round=function(e){return arguments.length?(c=!!e,f()):c},r.padding=function(e){return arguments.length?(s=Math.min(1,l=+e),f()):s},r.paddingInner=function(e){return arguments.length?(s=Math.min(1,e),f()):s},r.paddingOuter=function(e){return arguments.length?(l=+e,f()):l},r.align=function(e){return arguments.length?(u=Math.max(0,Math.min(1,e)),f()):u},r.copy=function(){return oR(n(),[i,a]).round(c).paddingInner(s).paddingOuter(l).align(u)},oT.apply(f(),arguments)}function oL(){return function e(t){var r=t.copy;return t.padding=t.paddingOuter,delete t.paddingInner,delete t.paddingOuter,t.copy=function(){return e(r())},t}(oR.apply(null,arguments).paddingInner(1))}function oz(e){return(oz="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function oF(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function oU(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?oF(Object(r),!0).forEach(function(t){var n,o,i;n=e,o=t,i=r[t],(o=function(e){var t=function(e,t){if("object"!=oz(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=oz(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==oz(t)?t:t+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):oF(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function o$(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}var oq={widthCache:{},cacheCount:0},oW={position:"absolute",top:"-20000px",left:0,padding:0,margin:0,border:"none",whiteSpace:"pre"},oH="recharts_measurement_span",oX=function(e){var t,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(null==e||tJ.isSsr)return{width:0,height:0};var n=(Object.keys(t=oU({},r)).forEach(function(e){t[e]||delete t[e]}),t),o=JSON.stringify({text:e,copyStyle:n});if(oq.widthCache[o])return oq.widthCache[o];try{var i=document.getElementById(oH);i||((i=document.createElement("span")).setAttribute("id",oH),i.setAttribute("aria-hidden","true"),document.body.appendChild(i));var a=oU(oU({},oW),n);Object.assign(i.style,a),i.textContent="".concat(e);var c=i.getBoundingClientRect(),s={width:c.width,height:c.height};return oq.widthCache[o]=s,++oq.cacheCount>2e3&&(oq.cacheCount=0,oq.widthCache={}),s}catch(e){return{width:0,height:0}}};function oV(e){return(oV="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function oG(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,c=[],s=!0,l=!1;try{if(i=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;s=!1}else for(;!(s=(n=i.call(r)).done)&&(c.push(n.value),c.length!==t);s=!0);}catch(e){l=!0,o=e}finally{try{if(!s&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(e,t)||function(e,t){if(e){if("string"==typeof e)return oK(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return oK(e,t)}}(e,t)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function oK(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function oY(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,function(e){var t=function(e,t){if("object"!=oV(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=oV(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==oV(t)?t:t+""}(n.key),n)}}var oZ=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([*/])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,oJ=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([+-])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,oQ=/^px|cm|vh|vw|em|rem|%|mm|in|pt|pc|ex|ch|vmin|vmax|Q$/,o0=/(-?\d+(?:\.\d+)?)([a-zA-Z%]+)?/,o1={cm:96/2.54,mm:96/25.4,pt:96/72,pc:16,in:96,Q:96/101.6,px:1},o2=Object.keys(o1),o5=function(){var e,t;function r(e,t){if(!(this instanceof r))throw TypeError("Cannot call a class as a function");this.num=e,this.unit=t,this.num=e,this.unit=t,Number.isNaN(e)&&(this.unit=""),""===t||oQ.test(t)||(this.num=NaN,this.unit=""),o2.includes(t)&&(this.num=e*o1[t],this.unit="px")}return e=[{key:"add",value:function(e){return this.unit!==e.unit?new r(NaN,""):new r(this.num+e.num,this.unit)}},{key:"subtract",value:function(e){return this.unit!==e.unit?new r(NaN,""):new r(this.num-e.num,this.unit)}},{key:"multiply",value:function(e){return""!==this.unit&&""!==e.unit&&this.unit!==e.unit?new r(NaN,""):new r(this.num*e.num,this.unit||e.unit)}},{key:"divide",value:function(e){return""!==this.unit&&""!==e.unit&&this.unit!==e.unit?new r(NaN,""):new r(this.num/e.num,this.unit||e.unit)}},{key:"toString",value:function(){return"".concat(this.num).concat(this.unit)}},{key:"isNaN",value:function(){return Number.isNaN(this.num)}}],t=[{key:"parse",value:function(e){var t,n=oG(null!=(t=o0.exec(e))?t:[],3),o=n[1],i=n[2];return new r(parseFloat(o),null!=i?i:"")}}],e&&oY(r.prototype,e),t&&oY(r,t),Object.defineProperty(r,"prototype",{writable:!1}),r}();function o4(e){if(e.includes("NaN"))return"NaN";for(var t=e;t.includes("*")||t.includes("/");){var r,n=oG(null!=(r=oZ.exec(t))?r:[],4),o=n[1],i=n[2],a=n[3],c=o5.parse(null!=o?o:""),s=o5.parse(null!=a?a:""),l="*"===i?c.multiply(s):c.divide(s);if(l.isNaN())return"NaN";t=t.replace(oZ,l.toString())}for(;t.includes("+")||/.-\d+(?:\.\d+)?/.test(t);){var u,f=oG(null!=(u=oJ.exec(t))?u:[],4),p=f[1],d=f[2],h=f[3],y=o5.parse(null!=p?p:""),m=o5.parse(null!=h?h:""),v="+"===d?y.add(m):y.subtract(m);if(v.isNaN())return"NaN";t=t.replace(oJ,v.toString())}return t}var o3=/\(([^()]*)\)/;function o6(e){var t=function(e){try{var t;return t=e.replace(/\s+/g,""),t=function(e){for(var t=e;t.includes("(");){var r=oG(o3.exec(t),2)[1];t=t.replace(o3,o4(r))}return t}(t),t=o4(t)}catch(e){return"NaN"}}(e.slice(5,-1));return"NaN"===t?"":t}var o8=["x","y","lineHeight","capHeight","scaleToFit","textAnchor","verticalAnchor","fill"],o7=["dx","dy","angle","className","breakAll"];function o9(){return(o9=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function ie(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}function it(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,c=[],s=!0,l=!1;try{if(i=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;s=!1}else for(;!(s=(n=i.call(r)).done)&&(c.push(n.value),c.length!==t);s=!0);}catch(e){l=!0,o=e}finally{try{if(!s&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(e,t)||function(e,t){if(e){if("string"==typeof e)return ir(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return ir(e,t)}}(e,t)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ir(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}var io=/[ \f\n\r\t\v\u2028\u2029]+/,ii=function(e){var t=e.children,r=e.breakAll,n=e.style;try{var o=[];e1()(t)||(o=r?t.toString().split(""):t.toString().split(io));var i=o.map(function(e){return{word:e,width:oX(e,n).width}}),a=r?0:oX("\xa0",n).width;return{wordsWithComputedWidth:i,spaceWidth:a}}catch(e){return null}},ia=function(e,t,r,n,o){var i,a=e.maxLines,c=e.children,s=e.style,l=e.breakAll,u=eq(a),f=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return e.reduce(function(e,t){var i=t.word,a=t.width,c=e[e.length-1];return c&&(null==n||o||c.width+a+r<Number(n))?(c.words.push(i),c.width+=a+r):e.push({words:[i],width:a}),e},[])},p=f(t);if(!u)return p;for(var d=function(e){var t=f(ii({breakAll:l,style:s,children:c.slice(0,e)+"…"}).wordsWithComputedWidth);return[t.length>a||t.reduce(function(e,t){return e.width>t.width?e:t}).width>Number(n),t]},h=0,y=c.length-1,m=0;h<=y&&m<=c.length-1;){var v=Math.floor((h+y)/2),b=it(d(v-1),2),g=b[0],x=b[1],w=it(d(v),1)[0];if(g||w||(h=v+1),g&&w&&(y=v-1),!g&&w){i=x;break}m++}return i||p},ic=function(e){return[{words:e1()(e)?[]:e.toString().split(io)}]},is=function(e){var t=e.width,r=e.scaleToFit,n=e.children,o=e.style,i=e.breakAll,a=e.maxLines;if((t||r)&&!tJ.isSsr){var c=ii({breakAll:i,children:n,style:o});if(!c)return ic(n);var s=c.wordsWithComputedWidth,l=c.spaceWidth;return ia({breakAll:i,children:n,maxLines:a,style:o},s,l,t,r)}return ic(n)},il="#808080",iu=function(e){var t,r=e.x,n=void 0===r?0:r,o=e.y,i=void 0===o?0:o,a=e.lineHeight,c=void 0===a?"1em":a,s=e.capHeight,f=void 0===s?"0.71em":s,p=e.scaleToFit,d=void 0!==p&&p,h=e.textAnchor,y=e.verticalAnchor,m=e.fill,v=void 0===m?il:m,b=ie(e,o8),g=(0,l.useMemo)(function(){return is({breakAll:b.breakAll,children:b.children,maxLines:b.maxLines,scaleToFit:d,style:b.style,width:b.width})},[b.breakAll,b.children,b.maxLines,d,b.style,b.width]),x=b.dx,w=b.dy,j=b.angle,O=b.className,S=b.breakAll,P=ie(b,o7);if(!eW(n)||!eW(i))return null;var A=n+(eq(x)?x:0),k=i+(eq(w)?w:0);switch(void 0===y?"end":y){case"start":t=o6("calc(".concat(f,")"));break;case"middle":t=o6("calc(".concat((g.length-1)/2," * -").concat(c," + (").concat(f," / 2))"));break;default:t=o6("calc(".concat(g.length-1," * -").concat(c,")"))}var E=[];if(d){var N=g[0].width,T=b.width;E.push("scale(".concat((eq(T)?T/N:1)/N,")"))}return j&&E.push("rotate(".concat(j,", ").concat(A,", ").concat(k,")")),E.length&&(P.transform=E.join(" ")),u().createElement("text",o9({},tg(P,!0),{x:A,y:k,className:(0,eT.A)("recharts-text",O),textAnchor:void 0===h?"start":h,fill:v.includes("url")?il:v}),g.map(function(e,r){var n=e.words.join(S?"":" ");return u().createElement("tspan",{x:A,dy:0===r?t:c,key:"".concat(n,"-").concat(r)},n)}))};let ip=Math.sqrt(50),id=Math.sqrt(10),ih=Math.sqrt(2);function iy(e,t,r){let n,o,i,a=(t-e)/Math.max(0,r),c=Math.floor(Math.log10(a)),s=a/Math.pow(10,c),l=s>=ip?10:s>=id?5:s>=ih?2:1;return(c<0?(n=Math.round(e*(i=Math.pow(10,-c)/l)),o=Math.round(t*i),n/i<e&&++n,o/i>t&&--o,i=-i):(n=Math.round(e/(i=Math.pow(10,c)*l)),o=Math.round(t/i),n*i<e&&++n,o*i>t&&--o),o<n&&.5<=r&&r<2)?iy(e,t,2*r):[n,o,i]}function im(e,t,r){if(t*=1,e*=1,!((r*=1)>0))return[];if(e===t)return[e];let n=t<e,[o,i,a]=n?iy(t,e,r):iy(e,t,r);if(!(i>=o))return[];let c=i-o+1,s=Array(c);if(n)if(a<0)for(let e=0;e<c;++e)s[e]=-((i-e)/a);else for(let e=0;e<c;++e)s[e]=(i-e)*a;else if(a<0)for(let e=0;e<c;++e)s[e]=-((o+e)/a);else for(let e=0;e<c;++e)s[e]=(o+e)*a;return s}function iv(e,t,r){return iy(e*=1,t*=1,r*=1)[2]}function ib(e,t,r){t*=1,e*=1,r*=1;let n=t<e,o=n?iv(t,e,r):iv(e,t,r);return(n?-1:1)*(o<0?-(1/o):o)}function ig(e,t){return null==e||null==t?NaN:e<t?-1:e>t?1:e>=t?0:NaN}function ix(e,t){return null==e||null==t?NaN:t<e?-1:t>e?1:t>=e?0:NaN}function iw(e){let t,r,n;function o(e,n,i=0,a=e.length){if(i<a){if(0!==t(n,n))return a;do{let t=i+a>>>1;0>r(e[t],n)?i=t+1:a=t}while(i<a)}return i}return 2!==e.length?(t=ig,r=(t,r)=>ig(e(t),r),n=(t,r)=>e(t)-r):(t=e===ig||e===ix?e:ij,r=e,n=e),{left:o,center:function(e,t,r=0,i=e.length){let a=o(e,t,r,i-1);return a>r&&n(e[a-1],t)>-n(e[a],t)?a-1:a},right:function(e,n,o=0,i=e.length){if(o<i){if(0!==t(n,n))return i;do{let t=o+i>>>1;0>=r(e[t],n)?o=t+1:i=t}while(o<i)}return o}}}function ij(){return 0}function iO(e){return null===e?NaN:+e}let iS=iw(ig),iP=iS.right;function iA(e,t,r){e.prototype=t.prototype=r,r.constructor=e}function ik(e,t){var r=Object.create(e.prototype);for(var n in t)r[n]=t[n];return r}function iE(){}iS.left,iw(iO).center;var iN="\\s*([+-]?\\d+)\\s*",iT="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",iM="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",i_=/^#([0-9a-f]{3,8})$/,iC=RegExp(`^rgb\\(${iN},${iN},${iN}\\)$`),iD=RegExp(`^rgb\\(${iM},${iM},${iM}\\)$`),iI=RegExp(`^rgba\\(${iN},${iN},${iN},${iT}\\)$`),iB=RegExp(`^rgba\\(${iM},${iM},${iM},${iT}\\)$`),iR=RegExp(`^hsl\\(${iT},${iM},${iM}\\)$`),iL=RegExp(`^hsla\\(${iT},${iM},${iM},${iT}\\)$`),iz={aliceblue:0xf0f8ff,antiquewhite:0xfaebd7,aqua:65535,aquamarine:8388564,azure:0xf0ffff,beige:0xf5f5dc,bisque:0xffe4c4,black:0,blanchedalmond:0xffebcd,blue:255,blueviolet:9055202,brown:0xa52a2a,burlywood:0xdeb887,cadetblue:6266528,chartreuse:8388352,chocolate:0xd2691e,coral:0xff7f50,cornflowerblue:6591981,cornsilk:0xfff8dc,crimson:0xdc143c,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:0xb8860b,darkgray:0xa9a9a9,darkgreen:25600,darkgrey:0xa9a9a9,darkkhaki:0xbdb76b,darkmagenta:9109643,darkolivegreen:5597999,darkorange:0xff8c00,darkorchid:0x9932cc,darkred:9109504,darksalmon:0xe9967a,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:0xff1493,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:0xb22222,floralwhite:0xfffaf0,forestgreen:2263842,fuchsia:0xff00ff,gainsboro:0xdcdcdc,ghostwhite:0xf8f8ff,gold:0xffd700,goldenrod:0xdaa520,gray:8421504,green:32768,greenyellow:0xadff2f,grey:8421504,honeydew:0xf0fff0,hotpink:0xff69b4,indianred:0xcd5c5c,indigo:4915330,ivory:0xfffff0,khaki:0xf0e68c,lavender:0xe6e6fa,lavenderblush:0xfff0f5,lawngreen:8190976,lemonchiffon:0xfffacd,lightblue:0xadd8e6,lightcoral:0xf08080,lightcyan:0xe0ffff,lightgoldenrodyellow:0xfafad2,lightgray:0xd3d3d3,lightgreen:9498256,lightgrey:0xd3d3d3,lightpink:0xffb6c1,lightsalmon:0xffa07a,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:0xb0c4de,lightyellow:0xffffe0,lime:65280,limegreen:3329330,linen:0xfaf0e6,magenta:0xff00ff,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:0xba55d3,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:0xc71585,midnightblue:1644912,mintcream:0xf5fffa,mistyrose:0xffe4e1,moccasin:0xffe4b5,navajowhite:0xffdead,navy:128,oldlace:0xfdf5e6,olive:8421376,olivedrab:7048739,orange:0xffa500,orangered:0xff4500,orchid:0xda70d6,palegoldenrod:0xeee8aa,palegreen:0x98fb98,paleturquoise:0xafeeee,palevioletred:0xdb7093,papayawhip:0xffefd5,peachpuff:0xffdab9,peru:0xcd853f,pink:0xffc0cb,plum:0xdda0dd,powderblue:0xb0e0e6,purple:8388736,rebeccapurple:6697881,red:0xff0000,rosybrown:0xbc8f8f,royalblue:4286945,saddlebrown:9127187,salmon:0xfa8072,sandybrown:0xf4a460,seagreen:3050327,seashell:0xfff5ee,sienna:0xa0522d,silver:0xc0c0c0,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:0xfffafa,springgreen:65407,steelblue:4620980,tan:0xd2b48c,teal:32896,thistle:0xd8bfd8,tomato:0xff6347,turquoise:4251856,violet:0xee82ee,wheat:0xf5deb3,white:0xffffff,whitesmoke:0xf5f5f5,yellow:0xffff00,yellowgreen:0x9acd32};function iF(){return this.rgb().formatHex()}function iU(){return this.rgb().formatRgb()}function i$(e){var t,r;return e=(e+"").trim().toLowerCase(),(t=i_.exec(e))?(r=t[1].length,t=parseInt(t[1],16),6===r?iq(t):3===r?new iX(t>>8&15|t>>4&240,t>>4&15|240&t,(15&t)<<4|15&t,1):8===r?iW(t>>24&255,t>>16&255,t>>8&255,(255&t)/255):4===r?iW(t>>12&15|t>>8&240,t>>8&15|t>>4&240,t>>4&15|240&t,((15&t)<<4|15&t)/255):null):(t=iC.exec(e))?new iX(t[1],t[2],t[3],1):(t=iD.exec(e))?new iX(255*t[1]/100,255*t[2]/100,255*t[3]/100,1):(t=iI.exec(e))?iW(t[1],t[2],t[3],t[4]):(t=iB.exec(e))?iW(255*t[1]/100,255*t[2]/100,255*t[3]/100,t[4]):(t=iR.exec(e))?iJ(t[1],t[2]/100,t[3]/100,1):(t=iL.exec(e))?iJ(t[1],t[2]/100,t[3]/100,t[4]):iz.hasOwnProperty(e)?iq(iz[e]):"transparent"===e?new iX(NaN,NaN,NaN,0):null}function iq(e){return new iX(e>>16&255,e>>8&255,255&e,1)}function iW(e,t,r,n){return n<=0&&(e=t=r=NaN),new iX(e,t,r,n)}function iH(e,t,r,n){var o;return 1==arguments.length?((o=e)instanceof iE||(o=i$(o)),o)?new iX((o=o.rgb()).r,o.g,o.b,o.opacity):new iX:new iX(e,t,r,null==n?1:n)}function iX(e,t,r,n){this.r=+e,this.g=+t,this.b=+r,this.opacity=+n}function iV(){return`#${iZ(this.r)}${iZ(this.g)}${iZ(this.b)}`}function iG(){let e=iK(this.opacity);return`${1===e?"rgb(":"rgba("}${iY(this.r)}, ${iY(this.g)}, ${iY(this.b)}${1===e?")":`, ${e})`}`}function iK(e){return isNaN(e)?1:Math.max(0,Math.min(1,e))}function iY(e){return Math.max(0,Math.min(255,Math.round(e)||0))}function iZ(e){return((e=iY(e))<16?"0":"")+e.toString(16)}function iJ(e,t,r,n){return n<=0?e=t=r=NaN:r<=0||r>=1?e=t=NaN:t<=0&&(e=NaN),new i0(e,t,r,n)}function iQ(e){if(e instanceof i0)return new i0(e.h,e.s,e.l,e.opacity);if(e instanceof iE||(e=i$(e)),!e)return new i0;if(e instanceof i0)return e;var t=(e=e.rgb()).r/255,r=e.g/255,n=e.b/255,o=Math.min(t,r,n),i=Math.max(t,r,n),a=NaN,c=i-o,s=(i+o)/2;return c?(a=t===i?(r-n)/c+(r<n)*6:r===i?(n-t)/c+2:(t-r)/c+4,c/=s<.5?i+o:2-i-o,a*=60):c=s>0&&s<1?0:a,new i0(a,c,s,e.opacity)}function i0(e,t,r,n){this.h=+e,this.s=+t,this.l=+r,this.opacity=+n}function i1(e){return(e=(e||0)%360)<0?e+360:e}function i2(e){return Math.max(0,Math.min(1,e||0))}function i5(e,t,r){return(e<60?t+(r-t)*e/60:e<180?r:e<240?t+(r-t)*(240-e)/60:t)*255}function i4(e,t,r,n,o){var i=e*e,a=i*e;return((1-3*e+3*i-a)*t+(4-6*i+3*a)*r+(1+3*e+3*i-3*a)*n+a*o)/6}iA(iE,i$,{copy(e){return Object.assign(new this.constructor,this,e)},displayable(){return this.rgb().displayable()},hex:iF,formatHex:iF,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return iQ(this).formatHsl()},formatRgb:iU,toString:iU}),iA(iX,iH,ik(iE,{brighter(e){return e=null==e?1.4285714285714286:Math.pow(1.4285714285714286,e),new iX(this.r*e,this.g*e,this.b*e,this.opacity)},darker(e){return e=null==e?.7:Math.pow(.7,e),new iX(this.r*e,this.g*e,this.b*e,this.opacity)},rgb(){return this},clamp(){return new iX(iY(this.r),iY(this.g),iY(this.b),iK(this.opacity))},displayable(){return -.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:iV,formatHex:iV,formatHex8:function(){return`#${iZ(this.r)}${iZ(this.g)}${iZ(this.b)}${iZ((isNaN(this.opacity)?1:this.opacity)*255)}`},formatRgb:iG,toString:iG})),iA(i0,function(e,t,r,n){return 1==arguments.length?iQ(e):new i0(e,t,r,null==n?1:n)},ik(iE,{brighter(e){return e=null==e?1.4285714285714286:Math.pow(1.4285714285714286,e),new i0(this.h,this.s,this.l*e,this.opacity)},darker(e){return e=null==e?.7:Math.pow(.7,e),new i0(this.h,this.s,this.l*e,this.opacity)},rgb(){var e=this.h%360+(this.h<0)*360,t=isNaN(e)||isNaN(this.s)?0:this.s,r=this.l,n=r+(r<.5?r:1-r)*t,o=2*r-n;return new iX(i5(e>=240?e-240:e+120,o,n),i5(e,o,n),i5(e<120?e+240:e-120,o,n),this.opacity)},clamp(){return new i0(i1(this.h),i2(this.s),i2(this.l),iK(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){let e=iK(this.opacity);return`${1===e?"hsl(":"hsla("}${i1(this.h)}, ${100*i2(this.s)}%, ${100*i2(this.l)}%${1===e?")":`, ${e})`}`}}));let i3=e=>()=>e;function i6(e,t){var r,n,o=t-e;return o?(r=e,n=o,function(e){return r+e*n}):i3(isNaN(e)?t:e)}let i8=function e(t){var r,n=1==(r=+t)?i6:function(e,t){var n,o,i;return t-e?(n=e,o=t,n=Math.pow(n,i=r),o=Math.pow(o,i)-n,i=1/i,function(e){return Math.pow(n+e*o,i)}):i3(isNaN(e)?t:e)};function o(e,t){var r=n((e=iH(e)).r,(t=iH(t)).r),o=n(e.g,t.g),i=n(e.b,t.b),a=i6(e.opacity,t.opacity);return function(t){return e.r=r(t),e.g=o(t),e.b=i(t),e.opacity=a(t),e+""}}return o.gamma=e,o}(1);function i7(e){return function(t){var r,n,o=t.length,i=Array(o),a=Array(o),c=Array(o);for(r=0;r<o;++r)n=iH(t[r]),i[r]=n.r||0,a[r]=n.g||0,c[r]=n.b||0;return i=e(i),a=e(a),c=e(c),n.opacity=1,function(e){return n.r=i(e),n.g=a(e),n.b=c(e),n+""}}}i7(function(e){var t=e.length-1;return function(r){var n=r<=0?r=0:r>=1?(r=1,t-1):Math.floor(r*t),o=e[n],i=e[n+1],a=n>0?e[n-1]:2*o-i,c=n<t-1?e[n+2]:2*i-o;return i4((r-n/t)*t,a,o,i,c)}}),i7(function(e){var t=e.length;return function(r){var n=Math.floor(((r%=1)<0?++r:r)*t),o=e[(n+t-1)%t],i=e[n%t],a=e[(n+1)%t],c=e[(n+2)%t];return i4((r-n/t)*t,o,i,a,c)}});function i9(e,t){return e*=1,t*=1,function(r){return e*(1-r)+t*r}}var ae=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,at=RegExp(ae.source,"g");function ar(e,t){var r,n,o=typeof t;return null==t||"boolean"===o?i3(t):("number"===o?i9:"string"===o?(n=i$(t))?(t=n,i8):function(e,t){var r,n,o,i,a,c=ae.lastIndex=at.lastIndex=0,s=-1,l=[],u=[];for(e+="",t+="";(o=ae.exec(e))&&(i=at.exec(t));)(a=i.index)>c&&(a=t.slice(c,a),l[s]?l[s]+=a:l[++s]=a),(o=o[0])===(i=i[0])?l[s]?l[s]+=i:l[++s]=i:(l[++s]=null,u.push({i:s,x:i9(o,i)})),c=at.lastIndex;return c<t.length&&(a=t.slice(c),l[s]?l[s]+=a:l[++s]=a),l.length<2?u[0]?(r=u[0].x,function(e){return r(e)+""}):(n=t,function(){return n}):(t=u.length,function(e){for(var r,n=0;n<t;++n)l[(r=u[n]).i]=r.x(e);return l.join("")})}:t instanceof i$?i8:t instanceof Date?function(e,t){var r=new Date;return e*=1,t*=1,function(n){return r.setTime(e*(1-n)+t*n),r}}:!ArrayBuffer.isView(r=t)||r instanceof DataView?Array.isArray(t)?function(e,t){var r,n=t?t.length:0,o=e?Math.min(n,e.length):0,i=Array(o),a=Array(n);for(r=0;r<o;++r)i[r]=ar(e[r],t[r]);for(;r<n;++r)a[r]=t[r];return function(e){for(r=0;r<o;++r)a[r]=i[r](e);return a}}:"function"!=typeof t.valueOf&&"function"!=typeof t.toString||isNaN(t)?function(e,t){var r,n={},o={};for(r in(null===e||"object"!=typeof e)&&(e={}),(null===t||"object"!=typeof t)&&(t={}),t)r in e?n[r]=ar(e[r],t[r]):o[r]=t[r];return function(e){for(r in n)o[r]=n[r](e);return o}}:i9:function(e,t){t||(t=[]);var r,n=e?Math.min(t.length,e.length):0,o=t.slice();return function(i){for(r=0;r<n;++r)o[r]=e[r]*(1-i)+t[r]*i;return o}})(e,t)}function an(e,t){return e*=1,t*=1,function(r){return Math.round(e*(1-r)+t*r)}}function ao(e){return+e}var ai=[0,1];function aa(e){return e}function ac(e,t){var r;return(t-=e*=1)?function(r){return(r-e)/t}:(r=isNaN(t)?NaN:.5,function(){return r})}function as(e,t,r){var n=e[0],o=e[1],i=t[0],a=t[1];return o<n?(n=ac(o,n),i=r(a,i)):(n=ac(n,o),i=r(i,a)),function(e){return i(n(e))}}function al(e,t,r){var n=Math.min(e.length,t.length)-1,o=Array(n),i=Array(n),a=-1;for(e[n]<e[0]&&(e=e.slice().reverse(),t=t.slice().reverse());++a<n;)o[a]=ac(e[a],e[a+1]),i[a]=r(t[a],t[a+1]);return function(t){var r=iP(e,t,1,n)-1;return i[r](o[r](t))}}function au(e,t){return t.domain(e.domain()).range(e.range()).interpolate(e.interpolate()).clamp(e.clamp()).unknown(e.unknown())}function af(){var e,t,r,n,o,i,a=ai,c=ai,s=ar,l=aa;function u(){var e,t,r,s=Math.min(a.length,c.length);return l!==aa&&(e=a[0],t=a[s-1],e>t&&(r=e,e=t,t=r),l=function(r){return Math.max(e,Math.min(t,r))}),n=s>2?al:as,o=i=null,f}function f(t){return null==t||isNaN(t*=1)?r:(o||(o=n(a.map(e),c,s)))(e(l(t)))}return f.invert=function(r){return l(t((i||(i=n(c,a.map(e),i9)))(r)))},f.domain=function(e){return arguments.length?(a=Array.from(e,ao),u()):a.slice()},f.range=function(e){return arguments.length?(c=Array.from(e),u()):c.slice()},f.rangeRound=function(e){return c=Array.from(e),s=an,u()},f.clamp=function(e){return arguments.length?(l=!!e||aa,u()):l!==aa},f.interpolate=function(e){return arguments.length?(s=e,u()):s},f.unknown=function(e){return arguments.length?(r=e,f):r},function(r,n){return e=r,t=n,u()}}function ap(){return af()(aa,aa)}var ad=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function ah(e){var t;if(!(t=ad.exec(e)))throw Error("invalid format: "+e);return new ay({fill:t[1],align:t[2],sign:t[3],symbol:t[4],zero:t[5],width:t[6],comma:t[7],precision:t[8]&&t[8].slice(1),trim:t[9],type:t[10]})}function ay(e){this.fill=void 0===e.fill?" ":e.fill+"",this.align=void 0===e.align?">":e.align+"",this.sign=void 0===e.sign?"-":e.sign+"",this.symbol=void 0===e.symbol?"":e.symbol+"",this.zero=!!e.zero,this.width=void 0===e.width?void 0:+e.width,this.comma=!!e.comma,this.precision=void 0===e.precision?void 0:+e.precision,this.trim=!!e.trim,this.type=void 0===e.type?"":e.type+""}function am(e,t){if((r=(e=t?e.toExponential(t-1):e.toExponential()).indexOf("e"))<0)return null;var r,n=e.slice(0,r);return[n.length>1?n[0]+n.slice(2):n,+e.slice(r+1)]}function av(e){return(e=am(Math.abs(e)))?e[1]:NaN}function ab(e,t){var r=am(e,t);if(!r)return e+"";var n=r[0],o=r[1];return o<0?"0."+Array(-o).join("0")+n:n.length>o+1?n.slice(0,o+1)+"."+n.slice(o+1):n+Array(o-n.length+2).join("0")}ah.prototype=ay.prototype,ay.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(void 0===this.width?"":Math.max(1,0|this.width))+(this.comma?",":"")+(void 0===this.precision?"":"."+Math.max(0,0|this.precision))+(this.trim?"~":"")+this.type};let ag={"%":(e,t)=>(100*e).toFixed(t),b:e=>Math.round(e).toString(2),c:e=>e+"",d:function(e){return Math.abs(e=Math.round(e))>=1e21?e.toLocaleString("en").replace(/,/g,""):e.toString(10)},e:(e,t)=>e.toExponential(t),f:(e,t)=>e.toFixed(t),g:(e,t)=>e.toPrecision(t),o:e=>Math.round(e).toString(8),p:(e,t)=>ab(100*e,t),r:ab,s:function(e,t){var r=am(e,t);if(!r)return e+"";var n=r[0],o=r[1],i=o-(sG=3*Math.max(-8,Math.min(8,Math.floor(o/3))))+1,a=n.length;return i===a?n:i>a?n+Array(i-a+1).join("0"):i>0?n.slice(0,i)+"."+n.slice(i):"0."+Array(1-i).join("0")+am(e,Math.max(0,t+i-1))[0]},X:e=>Math.round(e).toString(16).toUpperCase(),x:e=>Math.round(e).toString(16)};function ax(e){return e}var aw=Array.prototype.map,aj=["y","z","a","f","p","n","\xb5","m","","k","M","G","T","P","E","Z","Y"];function aO(e,t,r,n){var o,i,a,c=ib(e,t,r);switch((n=ah(null==n?",f":n)).type){case"s":var s=Math.max(Math.abs(e),Math.abs(t));return null!=n.precision||isNaN(a=Math.max(0,3*Math.max(-8,Math.min(8,Math.floor(av(s)/3)))-av(Math.abs(c))))||(n.precision=a),sZ(n,s);case"":case"e":case"g":case"p":case"r":null!=n.precision||isNaN(a=Math.max(0,av(Math.abs(Math.max(Math.abs(e),Math.abs(t)))-(o=Math.abs(o=c)))-av(o))+1)||(n.precision=a-("e"===n.type));break;case"f":case"%":null!=n.precision||isNaN(a=Math.max(0,-av(Math.abs(c))))||(n.precision=a-("%"===n.type)*2)}return sY(n)}function aS(e){var t=e.domain;return e.ticks=function(e){var r=t();return im(r[0],r[r.length-1],null==e?10:e)},e.tickFormat=function(e,r){var n=t();return aO(n[0],n[n.length-1],null==e?10:e,r)},e.nice=function(r){null==r&&(r=10);var n,o,i=t(),a=0,c=i.length-1,s=i[a],l=i[c],u=10;for(l<s&&(o=s,s=l,l=o,o=a,a=c,c=o);u-- >0;){if((o=iv(s,l,r))===n)return i[a]=s,i[c]=l,t(i);if(o>0)s=Math.floor(s/o)*o,l=Math.ceil(l/o)*o;else if(o<0)s=Math.ceil(s*o)/o,l=Math.floor(l*o)/o;else break;n=o}return e},e}function aP(){var e=ap();return e.copy=function(){return au(e,aP())},oT.apply(e,arguments),aS(e)}function aA(e,t){e=e.slice();var r,n=0,o=e.length-1,i=e[n],a=e[o];return a<i&&(r=n,n=o,o=r,r=i,i=a,a=r),e[n]=t.floor(i),e[o]=t.ceil(a),e}function ak(e){return Math.log(e)}function aE(e){return Math.exp(e)}function aN(e){return-Math.log(-e)}function aT(e){return-Math.exp(-e)}function aM(e){return isFinite(e)?+("1e"+e):e<0?0:e}function a_(e){return(t,r)=>-e(-t,r)}function aC(e){let t,r,n=e(ak,aE),o=n.domain,i=10;function a(){var a,c;return t=(a=i)===Math.E?Math.log:10===a&&Math.log10||2===a&&Math.log2||(a=Math.log(a),e=>Math.log(e)/a),r=10===(c=i)?aM:c===Math.E?Math.exp:e=>Math.pow(c,e),o()[0]<0?(t=a_(t),r=a_(r),e(aN,aT)):e(ak,aE),n}return n.base=function(e){return arguments.length?(i=+e,a()):i},n.domain=function(e){return arguments.length?(o(e),a()):o()},n.ticks=e=>{let n,a,c=o(),s=c[0],l=c[c.length-1],u=l<s;u&&([s,l]=[l,s]);let f=t(s),p=t(l),d=null==e?10:+e,h=[];if(!(i%1)&&p-f<d){if(f=Math.floor(f),p=Math.ceil(p),s>0){for(;f<=p;++f)for(n=1;n<i;++n)if(!((a=f<0?n/r(-f):n*r(f))<s)){if(a>l)break;h.push(a)}}else for(;f<=p;++f)for(n=i-1;n>=1;--n)if(!((a=f>0?n/r(-f):n*r(f))<s)){if(a>l)break;h.push(a)}2*h.length<d&&(h=im(s,l,d))}else h=im(f,p,Math.min(p-f,d)).map(r);return u?h.reverse():h},n.tickFormat=(e,o)=>{if(null==e&&(e=10),null==o&&(o=10===i?"s":","),"function"!=typeof o&&(i%1||null!=(o=ah(o)).precision||(o.trim=!0),o=sY(o)),e===1/0)return o;let a=Math.max(1,i*e/n.ticks().length);return e=>{let n=e/r(Math.round(t(e)));return n*i<i-.5&&(n*=i),n<=a?o(e):""}},n.nice=()=>o(aA(o(),{floor:e=>r(Math.floor(t(e))),ceil:e=>r(Math.ceil(t(e)))})),n}function aD(e){return function(t){return Math.sign(t)*Math.log1p(Math.abs(t/e))}}function aI(e){return function(t){return Math.sign(t)*Math.expm1(Math.abs(t))*e}}function aB(e){var t=1,r=e(aD(1),aI(t));return r.constant=function(r){return arguments.length?e(aD(t=+r),aI(t)):t},aS(r)}function aR(e){return function(t){return t<0?-Math.pow(-t,e):Math.pow(t,e)}}function aL(e){return e<0?-Math.sqrt(-e):Math.sqrt(e)}function az(e){return e<0?-e*e:e*e}function aF(e){var t=e(aa,aa),r=1;return t.exponent=function(t){return arguments.length?1==(r=+t)?e(aa,aa):.5===r?e(aL,az):e(aR(r),aR(1/r)):r},aS(t)}function aU(){var e=aF(af());return e.copy=function(){return au(e,aU()).exponent(e.exponent())},oT.apply(e,arguments),e}function a$(){return aU.apply(null,arguments).exponent(.5)}function aq(e){return Math.sign(e)*e*e}function aW(e,t){let r;if(void 0===t)for(let t of e)null!=t&&(r<t||void 0===r&&t>=t)&&(r=t);else{let n=-1;for(let o of e)null!=(o=t(o,++n,e))&&(r<o||void 0===r&&o>=o)&&(r=o)}return r}function aH(e,t){let r;if(void 0===t)for(let t of e)null!=t&&(r>t||void 0===r&&t>=t)&&(r=t);else{let n=-1;for(let o of e)null!=(o=t(o,++n,e))&&(r>o||void 0===r&&o>=o)&&(r=o)}return r}sY=(sK=function(e){var t,r,n,o=void 0===e.grouping||void 0===e.thousands?ax:(t=aw.call(e.grouping,Number),r=e.thousands+"",function(e,n){for(var o=e.length,i=[],a=0,c=t[0],s=0;o>0&&c>0&&(s+c+1>n&&(c=Math.max(1,n-s)),i.push(e.substring(o-=c,o+c)),!((s+=c+1)>n));)c=t[a=(a+1)%t.length];return i.reverse().join(r)}),i=void 0===e.currency?"":e.currency[0]+"",a=void 0===e.currency?"":e.currency[1]+"",c=void 0===e.decimal?".":e.decimal+"",s=void 0===e.numerals?ax:(n=aw.call(e.numerals,String),function(e){return e.replace(/[0-9]/g,function(e){return n[+e]})}),l=void 0===e.percent?"%":e.percent+"",u=void 0===e.minus?"−":e.minus+"",f=void 0===e.nan?"NaN":e.nan+"";function p(e){var t=(e=ah(e)).fill,r=e.align,n=e.sign,p=e.symbol,d=e.zero,h=e.width,y=e.comma,m=e.precision,v=e.trim,b=e.type;"n"===b?(y=!0,b="g"):ag[b]||(void 0===m&&(m=12),v=!0,b="g"),(d||"0"===t&&"="===r)&&(d=!0,t="0",r="=");var g="$"===p?i:"#"===p&&/[boxX]/.test(b)?"0"+b.toLowerCase():"",x="$"===p?a:/[%p]/.test(b)?l:"",w=ag[b],j=/[defgprs%]/.test(b);function O(e){var i,a,l,p=g,O=x;if("c"===b)O=w(e)+O,e="";else{var S=(e*=1)<0||1/e<0;if(e=isNaN(e)?f:w(Math.abs(e),m),v&&(e=function(e){e:for(var t,r=e.length,n=1,o=-1;n<r;++n)switch(e[n]){case".":o=t=n;break;case"0":0===o&&(o=n),t=n;break;default:if(!+e[n])break e;o>0&&(o=0)}return o>0?e.slice(0,o)+e.slice(t+1):e}(e)),S&&0==+e&&"+"!==n&&(S=!1),p=(S?"("===n?n:u:"-"===n||"("===n?"":n)+p,O=("s"===b?aj[8+sG/3]:"")+O+(S&&"("===n?")":""),j){for(i=-1,a=e.length;++i<a;)if(48>(l=e.charCodeAt(i))||l>57){O=(46===l?c+e.slice(i+1):e.slice(i))+O,e=e.slice(0,i);break}}}y&&!d&&(e=o(e,1/0));var P=p.length+e.length+O.length,A=P<h?Array(h-P+1).join(t):"";switch(y&&d&&(e=o(A+e,A.length?h-O.length:1/0),A=""),r){case"<":e=p+e+O+A;break;case"=":e=p+A+e+O;break;case"^":e=A.slice(0,P=A.length>>1)+p+e+O+A.slice(P);break;default:e=A+p+e+O}return s(e)}return m=void 0===m?6:/[gprs]/.test(b)?Math.max(1,Math.min(21,m)):Math.max(0,Math.min(20,m)),O.toString=function(){return e+""},O}return{format:p,formatPrefix:function(e,t){var r=p(((e=ah(e)).type="f",e)),n=3*Math.max(-8,Math.min(8,Math.floor(av(t)/3))),o=Math.pow(10,-n),i=aj[8+n/3];return function(e){return r(o*e)+i}}}}({thousands:",",grouping:[3],currency:["$",""]})).format,sZ=sK.formatPrefix;function aX(e,t){return(null==e||!(e>=e))-(null==t||!(t>=t))||(e<t?-1:+(e>t))}function aV(e,t,r){let n=e[t];e[t]=e[r],e[r]=n}let aG=new Date,aK=new Date;function aY(e,t,r,n){function o(t){return e(t=0==arguments.length?new Date:new Date(+t)),t}return o.floor=t=>(e(t=new Date(+t)),t),o.ceil=r=>(e(r=new Date(r-1)),t(r,1),e(r),r),o.round=e=>{let t=o(e),r=o.ceil(e);return e-t<r-e?t:r},o.offset=(e,r)=>(t(e=new Date(+e),null==r?1:Math.floor(r)),e),o.range=(r,n,i)=>{let a,c=[];if(r=o.ceil(r),i=null==i?1:Math.floor(i),!(r<n)||!(i>0))return c;do c.push(a=new Date(+r)),t(r,i),e(r);while(a<r&&r<n);return c},o.filter=r=>aY(t=>{if(t>=t)for(;e(t),!r(t);)t.setTime(t-1)},(e,n)=>{if(e>=e)if(n<0)for(;++n<=0;)for(;t(e,-1),!r(e););else for(;--n>=0;)for(;t(e,1),!r(e););}),r&&(o.count=(t,n)=>(aG.setTime(+t),aK.setTime(+n),e(aG),e(aK),Math.floor(r(aG,aK))),o.every=e=>isFinite(e=Math.floor(e))&&e>0?e>1?o.filter(n?t=>n(t)%e==0:t=>o.count(0,t)%e==0):o:null),o}let aZ=aY(()=>{},(e,t)=>{e.setTime(+e+t)},(e,t)=>t-e);aZ.every=e=>isFinite(e=Math.floor(e))&&e>0?e>1?aY(t=>{t.setTime(Math.floor(t/e)*e)},(t,r)=>{t.setTime(+t+r*e)},(t,r)=>(r-t)/e):aZ:null,aZ.range;let aJ=aY(e=>{e.setTime(e-e.getMilliseconds())},(e,t)=>{e.setTime(+e+1e3*t)},(e,t)=>(t-e)/1e3,e=>e.getUTCSeconds());aJ.range;let aQ=aY(e=>{e.setTime(e-e.getMilliseconds()-1e3*e.getSeconds())},(e,t)=>{e.setTime(+e+6e4*t)},(e,t)=>(t-e)/6e4,e=>e.getMinutes());aQ.range;let a0=aY(e=>{e.setUTCSeconds(0,0)},(e,t)=>{e.setTime(+e+6e4*t)},(e,t)=>(t-e)/6e4,e=>e.getUTCMinutes());a0.range;let a1=aY(e=>{e.setTime(e-e.getMilliseconds()-1e3*e.getSeconds()-6e4*e.getMinutes())},(e,t)=>{e.setTime(+e+36e5*t)},(e,t)=>(t-e)/36e5,e=>e.getHours());a1.range;let a2=aY(e=>{e.setUTCMinutes(0,0,0)},(e,t)=>{e.setTime(+e+36e5*t)},(e,t)=>(t-e)/36e5,e=>e.getUTCHours());a2.range;let a5=aY(e=>e.setHours(0,0,0,0),(e,t)=>e.setDate(e.getDate()+t),(e,t)=>(t-e-(t.getTimezoneOffset()-e.getTimezoneOffset())*6e4)/864e5,e=>e.getDate()-1);a5.range;let a4=aY(e=>{e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+t)},(e,t)=>(t-e)/864e5,e=>e.getUTCDate()-1);a4.range;let a3=aY(e=>{e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+t)},(e,t)=>(t-e)/864e5,e=>Math.floor(e/864e5));function a6(e){return aY(t=>{t.setDate(t.getDate()-(t.getDay()+7-e)%7),t.setHours(0,0,0,0)},(e,t)=>{e.setDate(e.getDate()+7*t)},(e,t)=>(t-e-(t.getTimezoneOffset()-e.getTimezoneOffset())*6e4)/6048e5)}a3.range;let a8=a6(0),a7=a6(1),a9=a6(2),ce=a6(3),ct=a6(4),cr=a6(5),cn=a6(6);function co(e){return aY(t=>{t.setUTCDate(t.getUTCDate()-(t.getUTCDay()+7-e)%7),t.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+7*t)},(e,t)=>(t-e)/6048e5)}a8.range,a7.range,a9.range,ce.range,ct.range,cr.range,cn.range;let ci=co(0),ca=co(1),cc=co(2),cs=co(3),cl=co(4),cu=co(5),cf=co(6);ci.range,ca.range,cc.range,cs.range,cl.range,cu.range,cf.range;let cp=aY(e=>{e.setDate(1),e.setHours(0,0,0,0)},(e,t)=>{e.setMonth(e.getMonth()+t)},(e,t)=>t.getMonth()-e.getMonth()+(t.getFullYear()-e.getFullYear())*12,e=>e.getMonth());cp.range;let cd=aY(e=>{e.setUTCDate(1),e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCMonth(e.getUTCMonth()+t)},(e,t)=>t.getUTCMonth()-e.getUTCMonth()+(t.getUTCFullYear()-e.getUTCFullYear())*12,e=>e.getUTCMonth());cd.range;let ch=aY(e=>{e.setMonth(0,1),e.setHours(0,0,0,0)},(e,t)=>{e.setFullYear(e.getFullYear()+t)},(e,t)=>t.getFullYear()-e.getFullYear(),e=>e.getFullYear());ch.every=e=>isFinite(e=Math.floor(e))&&e>0?aY(t=>{t.setFullYear(Math.floor(t.getFullYear()/e)*e),t.setMonth(0,1),t.setHours(0,0,0,0)},(t,r)=>{t.setFullYear(t.getFullYear()+r*e)}):null,ch.range;let cy=aY(e=>{e.setUTCMonth(0,1),e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCFullYear(e.getUTCFullYear()+t)},(e,t)=>t.getUTCFullYear()-e.getUTCFullYear(),e=>e.getUTCFullYear());function cm(e,t,r,n,o,i){let a=[[aJ,1,1e3],[aJ,5,5e3],[aJ,15,15e3],[aJ,30,3e4],[i,1,6e4],[i,5,3e5],[i,15,9e5],[i,30,18e5],[o,1,36e5],[o,3,108e5],[o,6,216e5],[o,12,432e5],[n,1,864e5],[n,2,1728e5],[r,1,6048e5],[t,1,2592e6],[t,3,7776e6],[e,1,31536e6]];function c(t,r,n){let o=Math.abs(r-t)/n,i=iw(([,,e])=>e).right(a,o);if(i===a.length)return e.every(ib(t/31536e6,r/31536e6,n));if(0===i)return aZ.every(Math.max(ib(t,r,n),1));let[c,s]=a[o/a[i-1][2]<a[i][2]/o?i-1:i];return c.every(s)}return[function(e,t,r){let n=t<e;n&&([e,t]=[t,e]);let o=r&&"function"==typeof r.range?r:c(e,t,r),i=o?o.range(e,+t+1):[];return n?i.reverse():i},c]}cy.every=e=>isFinite(e=Math.floor(e))&&e>0?aY(t=>{t.setUTCFullYear(Math.floor(t.getUTCFullYear()/e)*e),t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0)},(t,r)=>{t.setUTCFullYear(t.getUTCFullYear()+r*e)}):null,cy.range;let[cv,cb]=cm(cy,cd,ci,a3,a2,a0),[cg,cx]=cm(ch,cp,a8,a5,a1,aQ);function cw(e){if(0<=e.y&&e.y<100){var t=new Date(-1,e.m,e.d,e.H,e.M,e.S,e.L);return t.setFullYear(e.y),t}return new Date(e.y,e.m,e.d,e.H,e.M,e.S,e.L)}function cj(e){if(0<=e.y&&e.y<100){var t=new Date(Date.UTC(-1,e.m,e.d,e.H,e.M,e.S,e.L));return t.setUTCFullYear(e.y),t}return new Date(Date.UTC(e.y,e.m,e.d,e.H,e.M,e.S,e.L))}function cO(e,t,r){return{y:e,m:t,d:r,H:0,M:0,S:0,L:0}}var cS={"-":"",_:" ",0:"0"},cP=/^\s*\d+/,cA=/^%/,ck=/[\\^$*+?|[\]().{}]/g;function cE(e,t,r){var n=e<0?"-":"",o=(n?-e:e)+"",i=o.length;return n+(i<r?Array(r-i+1).join(t)+o:o)}function cN(e){return e.replace(ck,"\\$&")}function cT(e){return RegExp("^(?:"+e.map(cN).join("|")+")","i")}function cM(e){return new Map(e.map((e,t)=>[e.toLowerCase(),t]))}function c_(e,t,r){var n=cP.exec(t.slice(r,r+1));return n?(e.w=+n[0],r+n[0].length):-1}function cC(e,t,r){var n=cP.exec(t.slice(r,r+1));return n?(e.u=+n[0],r+n[0].length):-1}function cD(e,t,r){var n=cP.exec(t.slice(r,r+2));return n?(e.U=+n[0],r+n[0].length):-1}function cI(e,t,r){var n=cP.exec(t.slice(r,r+2));return n?(e.V=+n[0],r+n[0].length):-1}function cB(e,t,r){var n=cP.exec(t.slice(r,r+2));return n?(e.W=+n[0],r+n[0].length):-1}function cR(e,t,r){var n=cP.exec(t.slice(r,r+4));return n?(e.y=+n[0],r+n[0].length):-1}function cL(e,t,r){var n=cP.exec(t.slice(r,r+2));return n?(e.y=+n[0]+(+n[0]>68?1900:2e3),r+n[0].length):-1}function cz(e,t,r){var n=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(t.slice(r,r+6));return n?(e.Z=n[1]?0:-(n[2]+(n[3]||"00")),r+n[0].length):-1}function cF(e,t,r){var n=cP.exec(t.slice(r,r+1));return n?(e.q=3*n[0]-3,r+n[0].length):-1}function cU(e,t,r){var n=cP.exec(t.slice(r,r+2));return n?(e.m=n[0]-1,r+n[0].length):-1}function c$(e,t,r){var n=cP.exec(t.slice(r,r+2));return n?(e.d=+n[0],r+n[0].length):-1}function cq(e,t,r){var n=cP.exec(t.slice(r,r+3));return n?(e.m=0,e.d=+n[0],r+n[0].length):-1}function cW(e,t,r){var n=cP.exec(t.slice(r,r+2));return n?(e.H=+n[0],r+n[0].length):-1}function cH(e,t,r){var n=cP.exec(t.slice(r,r+2));return n?(e.M=+n[0],r+n[0].length):-1}function cX(e,t,r){var n=cP.exec(t.slice(r,r+2));return n?(e.S=+n[0],r+n[0].length):-1}function cV(e,t,r){var n=cP.exec(t.slice(r,r+3));return n?(e.L=+n[0],r+n[0].length):-1}function cG(e,t,r){var n=cP.exec(t.slice(r,r+6));return n?(e.L=Math.floor(n[0]/1e3),r+n[0].length):-1}function cK(e,t,r){var n=cA.exec(t.slice(r,r+1));return n?r+n[0].length:-1}function cY(e,t,r){var n=cP.exec(t.slice(r));return n?(e.Q=+n[0],r+n[0].length):-1}function cZ(e,t,r){var n=cP.exec(t.slice(r));return n?(e.s=+n[0],r+n[0].length):-1}function cJ(e,t){return cE(e.getDate(),t,2)}function cQ(e,t){return cE(e.getHours(),t,2)}function c0(e,t){return cE(e.getHours()%12||12,t,2)}function c1(e,t){return cE(1+a5.count(ch(e),e),t,3)}function c2(e,t){return cE(e.getMilliseconds(),t,3)}function c5(e,t){return c2(e,t)+"000"}function c4(e,t){return cE(e.getMonth()+1,t,2)}function c3(e,t){return cE(e.getMinutes(),t,2)}function c6(e,t){return cE(e.getSeconds(),t,2)}function c8(e){var t=e.getDay();return 0===t?7:t}function c7(e,t){return cE(a8.count(ch(e)-1,e),t,2)}function c9(e){var t=e.getDay();return t>=4||0===t?ct(e):ct.ceil(e)}function se(e,t){return e=c9(e),cE(ct.count(ch(e),e)+(4===ch(e).getDay()),t,2)}function st(e){return e.getDay()}function sr(e,t){return cE(a7.count(ch(e)-1,e),t,2)}function sn(e,t){return cE(e.getFullYear()%100,t,2)}function so(e,t){return cE((e=c9(e)).getFullYear()%100,t,2)}function si(e,t){return cE(e.getFullYear()%1e4,t,4)}function sa(e,t){var r=e.getDay();return cE((e=r>=4||0===r?ct(e):ct.ceil(e)).getFullYear()%1e4,t,4)}function sc(e){var t=e.getTimezoneOffset();return(t>0?"-":(t*=-1,"+"))+cE(t/60|0,"0",2)+cE(t%60,"0",2)}function ss(e,t){return cE(e.getUTCDate(),t,2)}function sl(e,t){return cE(e.getUTCHours(),t,2)}function su(e,t){return cE(e.getUTCHours()%12||12,t,2)}function sf(e,t){return cE(1+a4.count(cy(e),e),t,3)}function sp(e,t){return cE(e.getUTCMilliseconds(),t,3)}function sd(e,t){return sp(e,t)+"000"}function sh(e,t){return cE(e.getUTCMonth()+1,t,2)}function sy(e,t){return cE(e.getUTCMinutes(),t,2)}function sm(e,t){return cE(e.getUTCSeconds(),t,2)}function sv(e){var t=e.getUTCDay();return 0===t?7:t}function sb(e,t){return cE(ci.count(cy(e)-1,e),t,2)}function sg(e){var t=e.getUTCDay();return t>=4||0===t?cl(e):cl.ceil(e)}function sx(e,t){return e=sg(e),cE(cl.count(cy(e),e)+(4===cy(e).getUTCDay()),t,2)}function sw(e){return e.getUTCDay()}function sj(e,t){return cE(ca.count(cy(e)-1,e),t,2)}function sO(e,t){return cE(e.getUTCFullYear()%100,t,2)}function sS(e,t){return cE((e=sg(e)).getUTCFullYear()%100,t,2)}function sP(e,t){return cE(e.getUTCFullYear()%1e4,t,4)}function sA(e,t){var r=e.getUTCDay();return cE((e=r>=4||0===r?cl(e):cl.ceil(e)).getUTCFullYear()%1e4,t,4)}function sk(){return"+0000"}function sE(){return"%"}function sN(e){return+e}function sT(e){return Math.floor(e/1e3)}function sM(e){return new Date(e)}function s_(e){return e instanceof Date?+e:+new Date(+e)}function sC(e,t,r,n,o,i,a,c,s,l){var u=ap(),f=u.invert,p=u.domain,d=l(".%L"),h=l(":%S"),y=l("%I:%M"),m=l("%I %p"),v=l("%a %d"),b=l("%b %d"),g=l("%B"),x=l("%Y");function w(e){return(s(e)<e?d:c(e)<e?h:a(e)<e?y:i(e)<e?m:n(e)<e?o(e)<e?v:b:r(e)<e?g:x)(e)}return u.invert=function(e){return new Date(f(e))},u.domain=function(e){return arguments.length?p(Array.from(e,s_)):p().map(sM)},u.ticks=function(t){var r=p();return e(r[0],r[r.length-1],null==t?10:t)},u.tickFormat=function(e,t){return null==t?w:l(t)},u.nice=function(e){var r=p();return e&&"function"==typeof e.range||(e=t(r[0],r[r.length-1],null==e?10:e)),e?p(aA(r,e)):u},u.copy=function(){return au(u,sC(e,t,r,n,o,i,a,c,s,l))},u}function sD(){return oT.apply(sC(cg,cx,ch,cp,a8,a5,a1,aQ,aJ,sQ).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)}function sI(){return oT.apply(sC(cv,cb,cy,cd,ci,a4,a2,a0,aJ,s0).domain([Date.UTC(2e3,0,1),Date.UTC(2e3,0,2)]),arguments)}function sB(){var e,t,r,n,o,i=0,a=1,c=aa,s=!1;function l(t){return null==t||isNaN(t*=1)?o:c(0===r?.5:(t=(n(t)-e)*r,s?Math.max(0,Math.min(1,t)):t))}function u(e){return function(t){var r,n;return arguments.length?([r,n]=t,c=e(r,n),l):[c(0),c(1)]}}return l.domain=function(o){return arguments.length?([i,a]=o,e=n(i*=1),t=n(a*=1),r=e===t?0:1/(t-e),l):[i,a]},l.clamp=function(e){return arguments.length?(s=!!e,l):s},l.interpolator=function(e){return arguments.length?(c=e,l):c},l.range=u(ar),l.rangeRound=u(an),l.unknown=function(e){return arguments.length?(o=e,l):o},function(o){return n=o,e=o(i),t=o(a),r=e===t?0:1/(t-e),l}}function sR(e,t){return t.domain(e.domain()).interpolator(e.interpolator()).clamp(e.clamp()).unknown(e.unknown())}function sL(){var e=aF(sB());return e.copy=function(){return sR(e,sL()).exponent(e.exponent())},oM.apply(e,arguments)}function sz(){return sL.apply(null,arguments).exponent(.5)}function sF(){var e,t,r,n,o,i,a,c=0,s=.5,l=1,u=1,f=aa,p=!1;function d(e){return isNaN(e*=1)?a:(e=.5+((e=+i(e))-t)*(u*e<u*t?n:o),f(p?Math.max(0,Math.min(1,e)):e))}function h(e){return function(t){var r,n,o;return arguments.length?([r,n,o]=t,f=function(e,t){void 0===t&&(t=e,e=ar);for(var r=0,n=t.length-1,o=t[0],i=Array(n<0?0:n);r<n;)i[r]=e(o,o=t[++r]);return function(e){var t=Math.max(0,Math.min(n-1,Math.floor(e*=n)));return i[t](e-t)}}(e,[r,n,o]),d):[f(0),f(.5),f(1)]}}return d.domain=function(a){return arguments.length?([c,s,l]=a,e=i(c*=1),t=i(s*=1),r=i(l*=1),n=e===t?0:.5/(t-e),o=t===r?0:.5/(r-t),u=t<e?-1:1,d):[c,s,l]},d.clamp=function(e){return arguments.length?(p=!!e,d):p},d.interpolator=function(e){return arguments.length?(f=e,d):f},d.range=h(ar),d.rangeRound=h(an),d.unknown=function(e){return arguments.length?(a=e,d):a},function(a){return i=a,e=a(c),t=a(s),r=a(l),n=e===t?0:.5/(t-e),o=t===r?0:.5/(r-t),u=t<e?-1:1,d}}function sU(){var e=aF(sF());return e.copy=function(){return sR(e,sU()).exponent(e.exponent())},oM.apply(e,arguments)}function s$(){return sU.apply(null,arguments).exponent(.5)}function sq(e,t){if((o=e.length)>1)for(var r,n,o,i=1,a=e[t[0]],c=a.length;i<o;++i)for(n=a,a=e[t[i]],r=0;r<c;++r)a[r][1]+=a[r][0]=isNaN(n[r][1])?n[r][0]:n[r][1]}function sW(e){return"object"==typeof e&&"length"in e?e:Array.from(e)}function sH(e){for(var t=e.length,r=Array(t);--t>=0;)r[t]=t;return r}function sX(e,t){return e[t]}function sV(e){let t=[];return t.key=e,t}sQ=(sJ=function(e){var t=e.dateTime,r=e.date,n=e.time,o=e.periods,i=e.days,a=e.shortDays,c=e.months,s=e.shortMonths,l=cT(o),u=cM(o),f=cT(i),p=cM(i),d=cT(a),h=cM(a),y=cT(c),m=cM(c),v=cT(s),b=cM(s),g={a:function(e){return a[e.getDay()]},A:function(e){return i[e.getDay()]},b:function(e){return s[e.getMonth()]},B:function(e){return c[e.getMonth()]},c:null,d:cJ,e:cJ,f:c5,g:so,G:sa,H:cQ,I:c0,j:c1,L:c2,m:c4,M:c3,p:function(e){return o[+(e.getHours()>=12)]},q:function(e){return 1+~~(e.getMonth()/3)},Q:sN,s:sT,S:c6,u:c8,U:c7,V:se,w:st,W:sr,x:null,X:null,y:sn,Y:si,Z:sc,"%":sE},x={a:function(e){return a[e.getUTCDay()]},A:function(e){return i[e.getUTCDay()]},b:function(e){return s[e.getUTCMonth()]},B:function(e){return c[e.getUTCMonth()]},c:null,d:ss,e:ss,f:sd,g:sS,G:sA,H:sl,I:su,j:sf,L:sp,m:sh,M:sy,p:function(e){return o[+(e.getUTCHours()>=12)]},q:function(e){return 1+~~(e.getUTCMonth()/3)},Q:sN,s:sT,S:sm,u:sv,U:sb,V:sx,w:sw,W:sj,x:null,X:null,y:sO,Y:sP,Z:sk,"%":sE},w={a:function(e,t,r){var n=d.exec(t.slice(r));return n?(e.w=h.get(n[0].toLowerCase()),r+n[0].length):-1},A:function(e,t,r){var n=f.exec(t.slice(r));return n?(e.w=p.get(n[0].toLowerCase()),r+n[0].length):-1},b:function(e,t,r){var n=v.exec(t.slice(r));return n?(e.m=b.get(n[0].toLowerCase()),r+n[0].length):-1},B:function(e,t,r){var n=y.exec(t.slice(r));return n?(e.m=m.get(n[0].toLowerCase()),r+n[0].length):-1},c:function(e,r,n){return S(e,t,r,n)},d:c$,e:c$,f:cG,g:cL,G:cR,H:cW,I:cW,j:cq,L:cV,m:cU,M:cH,p:function(e,t,r){var n=l.exec(t.slice(r));return n?(e.p=u.get(n[0].toLowerCase()),r+n[0].length):-1},q:cF,Q:cY,s:cZ,S:cX,u:cC,U:cD,V:cI,w:c_,W:cB,x:function(e,t,n){return S(e,r,t,n)},X:function(e,t,r){return S(e,n,t,r)},y:cL,Y:cR,Z:cz,"%":cK};function j(e,t){return function(r){var n,o,i,a=[],c=-1,s=0,l=e.length;for(r instanceof Date||(r=new Date(+r));++c<l;)37===e.charCodeAt(c)&&(a.push(e.slice(s,c)),null!=(o=cS[n=e.charAt(++c)])?n=e.charAt(++c):o="e"===n?" ":"0",(i=t[n])&&(n=i(r,o)),a.push(n),s=c+1);return a.push(e.slice(s,c)),a.join("")}}function O(e,t){return function(r){var n,o,i=cO(1900,void 0,1);if(S(i,e,r+="",0)!=r.length)return null;if("Q"in i)return new Date(i.Q);if("s"in i)return new Date(1e3*i.s+("L"in i?i.L:0));if(!t||"Z"in i||(i.Z=0),"p"in i&&(i.H=i.H%12+12*i.p),void 0===i.m&&(i.m="q"in i?i.q:0),"V"in i){if(i.V<1||i.V>53)return null;"w"in i||(i.w=1),"Z"in i?(n=(o=(n=cj(cO(i.y,0,1))).getUTCDay())>4||0===o?ca.ceil(n):ca(n),n=a4.offset(n,(i.V-1)*7),i.y=n.getUTCFullYear(),i.m=n.getUTCMonth(),i.d=n.getUTCDate()+(i.w+6)%7):(n=(o=(n=cw(cO(i.y,0,1))).getDay())>4||0===o?a7.ceil(n):a7(n),n=a5.offset(n,(i.V-1)*7),i.y=n.getFullYear(),i.m=n.getMonth(),i.d=n.getDate()+(i.w+6)%7)}else("W"in i||"U"in i)&&("w"in i||(i.w="u"in i?i.u%7:+("W"in i)),o="Z"in i?cj(cO(i.y,0,1)).getUTCDay():cw(cO(i.y,0,1)).getDay(),i.m=0,i.d="W"in i?(i.w+6)%7+7*i.W-(o+5)%7:i.w+7*i.U-(o+6)%7);return"Z"in i?(i.H+=i.Z/100|0,i.M+=i.Z%100,cj(i)):cw(i)}}function S(e,t,r,n){for(var o,i,a=0,c=t.length,s=r.length;a<c;){if(n>=s)return -1;if(37===(o=t.charCodeAt(a++))){if(!(i=w[(o=t.charAt(a++))in cS?t.charAt(a++):o])||(n=i(e,r,n))<0)return -1}else if(o!=r.charCodeAt(n++))return -1}return n}return g.x=j(r,g),g.X=j(n,g),g.c=j(t,g),x.x=j(r,x),x.X=j(n,x),x.c=j(t,x),{format:function(e){var t=j(e+="",g);return t.toString=function(){return e},t},parse:function(e){var t=O(e+="",!1);return t.toString=function(){return e},t},utcFormat:function(e){var t=j(e+="",x);return t.toString=function(){return e},t},utcParse:function(e){var t=O(e+="",!0);return t.toString=function(){return e},t}}}({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]})).format,sJ.parse,s0=sJ.utcFormat,sJ.utcParse,Array.prototype.slice;var sG,sK,sY,sZ,sJ,sQ,s0,s1,s2,s5=r(90453),s4=r.n(s5),s3=r(15883),s6=r.n(s3),s8=r(21592),s7=r.n(s8),s9=r(71967),le=r.n(s9),lt=!0,lr="[DecimalError] ",ln=lr+"Invalid argument: ",lo=lr+"Exponent out of range: ",li=Math.floor,la=Math.pow,lc=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,ls=li(1286742750677284.5),ll={};function lu(e,t){var r,n,o,i,a,c,s,l,u=e.constructor,f=u.precision;if(!e.s||!t.s)return t.s||(t=new u(e)),lt?lx(t,f):t;if(s=e.d,l=t.d,a=e.e,o=t.e,s=s.slice(),i=a-o){for(i<0?(n=s,i=-i,c=l.length):(n=l,o=a,c=s.length),i>(c=(a=Math.ceil(f/7))>c?a+1:c+1)&&(i=c,n.length=1),n.reverse();i--;)n.push(0);n.reverse()}for((c=s.length)-(i=l.length)<0&&(i=c,n=l,l=s,s=n),r=0;i;)r=(s[--i]=s[i]+l[i]+r)/1e7|0,s[i]%=1e7;for(r&&(s.unshift(r),++o),c=s.length;0==s[--c];)s.pop();return t.d=s,t.e=o,lt?lx(t,f):t}function lf(e,t,r){if(e!==~~e||e<t||e>r)throw Error(ln+e)}function lp(e){var t,r,n,o=e.length-1,i="",a=e[0];if(o>0){for(i+=a,t=1;t<o;t++)(r=7-(n=e[t]+"").length)&&(i+=lv(r)),i+=n;(r=7-(n=(a=e[t])+"").length)&&(i+=lv(r))}else if(0===a)return"0";for(;a%10==0;)a/=10;return i+a}ll.absoluteValue=ll.abs=function(){var e=new this.constructor(this);return e.s&&(e.s=1),e},ll.comparedTo=ll.cmp=function(e){var t,r,n,o;if(e=new this.constructor(e),this.s!==e.s)return this.s||-e.s;if(this.e!==e.e)return this.e>e.e^this.s<0?1:-1;for(t=0,r=(n=this.d.length)<(o=e.d.length)?n:o;t<r;++t)if(this.d[t]!==e.d[t])return this.d[t]>e.d[t]^this.s<0?1:-1;return n===o?0:n>o^this.s<0?1:-1},ll.decimalPlaces=ll.dp=function(){var e=this.d.length-1,t=(e-this.e)*7;if(e=this.d[e])for(;e%10==0;e/=10)t--;return t<0?0:t},ll.dividedBy=ll.div=function(e){return ld(this,new this.constructor(e))},ll.dividedToIntegerBy=ll.idiv=function(e){var t=this.constructor;return lx(ld(this,new t(e),0,1),t.precision)},ll.equals=ll.eq=function(e){return!this.cmp(e)},ll.exponent=function(){return ly(this)},ll.greaterThan=ll.gt=function(e){return this.cmp(e)>0},ll.greaterThanOrEqualTo=ll.gte=function(e){return this.cmp(e)>=0},ll.isInteger=ll.isint=function(){return this.e>this.d.length-2},ll.isNegative=ll.isneg=function(){return this.s<0},ll.isPositive=ll.ispos=function(){return this.s>0},ll.isZero=function(){return 0===this.s},ll.lessThan=ll.lt=function(e){return 0>this.cmp(e)},ll.lessThanOrEqualTo=ll.lte=function(e){return 1>this.cmp(e)},ll.logarithm=ll.log=function(e){var t,r=this.constructor,n=r.precision,o=n+5;if(void 0===e)e=new r(10);else if((e=new r(e)).s<1||e.eq(s2))throw Error(lr+"NaN");if(this.s<1)throw Error(lr+(this.s?"NaN":"-Infinity"));return this.eq(s2)?new r(0):(lt=!1,t=ld(lb(this,o),lb(e,o),o),lt=!0,lx(t,n))},ll.minus=ll.sub=function(e){return e=new this.constructor(e),this.s==e.s?lw(this,e):lu(this,(e.s=-e.s,e))},ll.modulo=ll.mod=function(e){var t,r=this.constructor,n=r.precision;if(!(e=new r(e)).s)throw Error(lr+"NaN");return this.s?(lt=!1,t=ld(this,e,0,1).times(e),lt=!0,this.minus(t)):lx(new r(this),n)},ll.naturalExponential=ll.exp=function(){return lh(this)},ll.naturalLogarithm=ll.ln=function(){return lb(this)},ll.negated=ll.neg=function(){var e=new this.constructor(this);return e.s=-e.s||0,e},ll.plus=ll.add=function(e){return e=new this.constructor(e),this.s==e.s?lu(this,e):lw(this,(e.s=-e.s,e))},ll.precision=ll.sd=function(e){var t,r,n;if(void 0!==e&&!!e!==e&&1!==e&&0!==e)throw Error(ln+e);if(t=ly(this)+1,r=7*(n=this.d.length-1)+1,n=this.d[n]){for(;n%10==0;n/=10)r--;for(n=this.d[0];n>=10;n/=10)r++}return e&&t>r?t:r},ll.squareRoot=ll.sqrt=function(){var e,t,r,n,o,i,a,c=this.constructor;if(this.s<1){if(!this.s)return new c(0);throw Error(lr+"NaN")}for(e=ly(this),lt=!1,0==(o=Math.sqrt(+this))||o==1/0?(((t=lp(this.d)).length+e)%2==0&&(t+="0"),o=Math.sqrt(t),e=li((e+1)/2)-(e<0||e%2),n=new c(t=o==1/0?"5e"+e:(t=o.toExponential()).slice(0,t.indexOf("e")+1)+e)):n=new c(o.toString()),o=a=(r=c.precision)+3;;)if(n=(i=n).plus(ld(this,i,a+2)).times(.5),lp(i.d).slice(0,a)===(t=lp(n.d)).slice(0,a)){if(t=t.slice(a-3,a+1),o==a&&"4999"==t){if(lx(i,r+1,0),i.times(i).eq(this)){n=i;break}}else if("9999"!=t)break;a+=4}return lt=!0,lx(n,r)},ll.times=ll.mul=function(e){var t,r,n,o,i,a,c,s,l,u=this.constructor,f=this.d,p=(e=new u(e)).d;if(!this.s||!e.s)return new u(0);for(e.s*=this.s,r=this.e+e.e,(s=f.length)<(l=p.length)&&(i=f,f=p,p=i,a=s,s=l,l=a),i=[],n=a=s+l;n--;)i.push(0);for(n=l;--n>=0;){for(t=0,o=s+n;o>n;)c=i[o]+p[n]*f[o-n-1]+t,i[o--]=c%1e7|0,t=c/1e7|0;i[o]=(i[o]+t)%1e7|0}for(;!i[--a];)i.pop();return t?++r:i.shift(),e.d=i,e.e=r,lt?lx(e,u.precision):e},ll.toDecimalPlaces=ll.todp=function(e,t){var r=this,n=r.constructor;return(r=new n(r),void 0===e)?r:(lf(e,0,1e9),void 0===t?t=n.rounding:lf(t,0,8),lx(r,e+ly(r)+1,t))},ll.toExponential=function(e,t){var r,n=this,o=n.constructor;return void 0===e?r=lj(n,!0):(lf(e,0,1e9),void 0===t?t=o.rounding:lf(t,0,8),r=lj(n=lx(new o(n),e+1,t),!0,e+1)),r},ll.toFixed=function(e,t){var r,n,o=this.constructor;return void 0===e?lj(this):(lf(e,0,1e9),void 0===t?t=o.rounding:lf(t,0,8),r=lj((n=lx(new o(this),e+ly(this)+1,t)).abs(),!1,e+ly(n)+1),this.isneg()&&!this.isZero()?"-"+r:r)},ll.toInteger=ll.toint=function(){var e=this.constructor;return lx(new e(this),ly(this)+1,e.rounding)},ll.toNumber=function(){return+this},ll.toPower=ll.pow=function(e){var t,r,n,o,i,a,c=this,s=c.constructor,l=+(e=new s(e));if(!e.s)return new s(s2);if(!(c=new s(c)).s){if(e.s<1)throw Error(lr+"Infinity");return c}if(c.eq(s2))return c;if(n=s.precision,e.eq(s2))return lx(c,n);if(a=(t=e.e)>=(r=e.d.length-1),i=c.s,a){if((r=l<0?-l:l)<=0x1fffffffffffff){for(o=new s(s2),t=Math.ceil(n/7+4),lt=!1;r%2&&lO((o=o.times(c)).d,t),0!==(r=li(r/2));)lO((c=c.times(c)).d,t);return lt=!0,e.s<0?new s(s2).div(o):lx(o,n)}}else if(i<0)throw Error(lr+"NaN");return i=i<0&&1&e.d[Math.max(t,r)]?-1:1,c.s=1,lt=!1,o=e.times(lb(c,n+12)),lt=!0,(o=lh(o)).s=i,o},ll.toPrecision=function(e,t){var r,n,o=this,i=o.constructor;return void 0===e?(r=ly(o),n=lj(o,r<=i.toExpNeg||r>=i.toExpPos)):(lf(e,1,1e9),void 0===t?t=i.rounding:lf(t,0,8),r=ly(o=lx(new i(o),e,t)),n=lj(o,e<=r||r<=i.toExpNeg,e)),n},ll.toSignificantDigits=ll.tosd=function(e,t){var r=this.constructor;return void 0===e?(e=r.precision,t=r.rounding):(lf(e,1,1e9),void 0===t?t=r.rounding:lf(t,0,8)),lx(new r(this),e,t)},ll.toString=ll.valueOf=ll.val=ll.toJSON=ll[Symbol.for("nodejs.util.inspect.custom")]=function(){var e=ly(this),t=this.constructor;return lj(this,e<=t.toExpNeg||e>=t.toExpPos)};var ld=function(){function e(e,t){var r,n=0,o=e.length;for(e=e.slice();o--;)r=e[o]*t+n,e[o]=r%1e7|0,n=r/1e7|0;return n&&e.unshift(n),e}function t(e,t,r,n){var o,i;if(r!=n)i=r>n?1:-1;else for(o=i=0;o<r;o++)if(e[o]!=t[o]){i=e[o]>t[o]?1:-1;break}return i}function r(e,t,r){for(var n=0;r--;)e[r]-=n,n=+(e[r]<t[r]),e[r]=1e7*n+e[r]-t[r];for(;!e[0]&&e.length>1;)e.shift()}return function(n,o,i,a){var c,s,l,u,f,p,d,h,y,m,v,b,g,x,w,j,O,S,P=n.constructor,A=n.s==o.s?1:-1,k=n.d,E=o.d;if(!n.s)return new P(n);if(!o.s)throw Error(lr+"Division by zero");for(l=0,s=n.e-o.e,O=E.length,w=k.length,h=(d=new P(A)).d=[];E[l]==(k[l]||0);)++l;if(E[l]>(k[l]||0)&&--s,(b=null==i?i=P.precision:a?i+(ly(n)-ly(o))+1:i)<0)return new P(0);if(b=b/7+2|0,l=0,1==O)for(u=0,E=E[0],b++;(l<w||u)&&b--;l++)g=1e7*u+(k[l]||0),h[l]=g/E|0,u=g%E|0;else{for((u=1e7/(E[0]+1)|0)>1&&(E=e(E,u),k=e(k,u),O=E.length,w=k.length),x=O,m=(y=k.slice(0,O)).length;m<O;)y[m++]=0;(S=E.slice()).unshift(0),j=E[0],E[1]>=1e7/2&&++j;do u=0,(c=t(E,y,O,m))<0?(v=y[0],O!=m&&(v=1e7*v+(y[1]||0)),(u=v/j|0)>1?(u>=1e7&&(u=1e7-1),p=(f=e(E,u)).length,m=y.length,1==(c=t(f,y,p,m))&&(u--,r(f,O<p?S:E,p))):(0==u&&(c=u=1),f=E.slice()),(p=f.length)<m&&f.unshift(0),r(y,f,m),-1==c&&(m=y.length,(c=t(E,y,O,m))<1&&(u++,r(y,O<m?S:E,m))),m=y.length):0===c&&(u++,y=[0]),h[l++]=u,c&&y[0]?y[m++]=k[x]||0:(y=[k[x]],m=1);while((x++<w||void 0!==y[0])&&b--)}return h[0]||h.shift(),d.e=s,lx(d,a?i+ly(d)+1:i)}}();function lh(e,t){var r,n,o,i,a,c=0,s=0,l=e.constructor,u=l.precision;if(ly(e)>16)throw Error(lo+ly(e));if(!e.s)return new l(s2);for(null==t?(lt=!1,a=u):a=t,i=new l(.03125);e.abs().gte(.1);)e=e.times(i),s+=5;for(a+=Math.log(la(2,s))/Math.LN10*2+5|0,r=n=o=new l(s2),l.precision=a;;){if(n=lx(n.times(e),a),r=r.times(++c),lp((i=o.plus(ld(n,r,a))).d).slice(0,a)===lp(o.d).slice(0,a)){for(;s--;)o=lx(o.times(o),a);return l.precision=u,null==t?(lt=!0,lx(o,u)):o}o=i}}function ly(e){for(var t=7*e.e,r=e.d[0];r>=10;r/=10)t++;return t}function lm(e,t,r){if(t>e.LN10.sd())throw lt=!0,r&&(e.precision=r),Error(lr+"LN10 precision limit exceeded");return lx(new e(e.LN10),t)}function lv(e){for(var t="";e--;)t+="0";return t}function lb(e,t){var r,n,o,i,a,c,s,l,u,f=1,p=e,d=p.d,h=p.constructor,y=h.precision;if(p.s<1)throw Error(lr+(p.s?"NaN":"-Infinity"));if(p.eq(s2))return new h(0);if(null==t?(lt=!1,l=y):l=t,p.eq(10))return null==t&&(lt=!0),lm(h,l);if(h.precision=l+=10,n=(r=lp(d)).charAt(0),!(15e14>Math.abs(i=ly(p))))return s=lm(h,l+2,y).times(i+""),p=lb(new h(n+"."+r.slice(1)),l-10).plus(s),h.precision=y,null==t?(lt=!0,lx(p,y)):p;for(;n<7&&1!=n||1==n&&r.charAt(1)>3;)n=(r=lp((p=p.times(e)).d)).charAt(0),f++;for(i=ly(p),n>1?(p=new h("0."+r),i++):p=new h(n+"."+r.slice(1)),c=a=p=ld(p.minus(s2),p.plus(s2),l),u=lx(p.times(p),l),o=3;;){if(a=lx(a.times(u),l),lp((s=c.plus(ld(a,new h(o),l))).d).slice(0,l)===lp(c.d).slice(0,l))return c=c.times(2),0!==i&&(c=c.plus(lm(h,l+2,y).times(i+""))),c=ld(c,new h(f),l),h.precision=y,null==t?(lt=!0,lx(c,y)):c;c=s,o+=2}}function lg(e,t){var r,n,o;for((r=t.indexOf("."))>-1&&(t=t.replace(".","")),(n=t.search(/e/i))>0?(r<0&&(r=n),r+=+t.slice(n+1),t=t.substring(0,n)):r<0&&(r=t.length),n=0;48===t.charCodeAt(n);)++n;for(o=t.length;48===t.charCodeAt(o-1);)--o;if(t=t.slice(n,o)){if(o-=n,e.e=li((r=r-n-1)/7),e.d=[],n=(r+1)%7,r<0&&(n+=7),n<o){for(n&&e.d.push(+t.slice(0,n)),o-=7;n<o;)e.d.push(+t.slice(n,n+=7));n=7-(t=t.slice(n)).length}else n-=o;for(;n--;)t+="0";if(e.d.push(+t),lt&&(e.e>ls||e.e<-ls))throw Error(lo+r)}else e.s=0,e.e=0,e.d=[0];return e}function lx(e,t,r){var n,o,i,a,c,s,l,u,f=e.d;for(a=1,i=f[0];i>=10;i/=10)a++;if((n=t-a)<0)n+=7,o=t,l=f[u=0];else{if((u=Math.ceil((n+1)/7))>=(i=f.length))return e;for(a=1,l=i=f[u];i>=10;i/=10)a++;n%=7,o=n-7+a}if(void 0!==r&&(c=l/(i=la(10,a-o-1))%10|0,s=t<0||void 0!==f[u+1]||l%i,s=r<4?(c||s)&&(0==r||r==(e.s<0?3:2)):c>5||5==c&&(4==r||s||6==r&&(n>0?o>0?l/la(10,a-o):0:f[u-1])%10&1||r==(e.s<0?8:7))),t<1||!f[0])return s?(i=ly(e),f.length=1,t=t-i-1,f[0]=la(10,(7-t%7)%7),e.e=li(-t/7)||0):(f.length=1,f[0]=e.e=e.s=0),e;if(0==n?(f.length=u,i=1,u--):(f.length=u+1,i=la(10,7-n),f[u]=o>0?(l/la(10,a-o)%la(10,o)|0)*i:0),s)for(;;)if(0==u){1e7==(f[0]+=i)&&(f[0]=1,++e.e);break}else{if(f[u]+=i,1e7!=f[u])break;f[u--]=0,i=1}for(n=f.length;0===f[--n];)f.pop();if(lt&&(e.e>ls||e.e<-ls))throw Error(lo+ly(e));return e}function lw(e,t){var r,n,o,i,a,c,s,l,u,f,p=e.constructor,d=p.precision;if(!e.s||!t.s)return t.s?t.s=-t.s:t=new p(e),lt?lx(t,d):t;if(s=e.d,f=t.d,n=t.e,l=e.e,s=s.slice(),a=l-n){for((u=a<0)?(r=s,a=-a,c=f.length):(r=f,n=l,c=s.length),a>(o=Math.max(Math.ceil(d/7),c)+2)&&(a=o,r.length=1),r.reverse(),o=a;o--;)r.push(0);r.reverse()}else{for((u=(o=s.length)<(c=f.length))&&(c=o),o=0;o<c;o++)if(s[o]!=f[o]){u=s[o]<f[o];break}a=0}for(u&&(r=s,s=f,f=r,t.s=-t.s),c=s.length,o=f.length-c;o>0;--o)s[c++]=0;for(o=f.length;o>a;){if(s[--o]<f[o]){for(i=o;i&&0===s[--i];)s[i]=1e7-1;--s[i],s[o]+=1e7}s[o]-=f[o]}for(;0===s[--c];)s.pop();for(;0===s[0];s.shift())--n;return s[0]?(t.d=s,t.e=n,lt?lx(t,d):t):new p(0)}function lj(e,t,r){var n,o=ly(e),i=lp(e.d),a=i.length;return t?(r&&(n=r-a)>0?i=i.charAt(0)+"."+i.slice(1)+lv(n):a>1&&(i=i.charAt(0)+"."+i.slice(1)),i=i+(o<0?"e":"e+")+o):o<0?(i="0."+lv(-o-1)+i,r&&(n=r-a)>0&&(i+=lv(n))):o>=a?(i+=lv(o+1-a),r&&(n=r-o-1)>0&&(i=i+"."+lv(n))):((n=o+1)<a&&(i=i.slice(0,n)+"."+i.slice(n)),r&&(n=r-a)>0&&(o+1===a&&(i+="."),i+=lv(n))),e.s<0?"-"+i:i}function lO(e,t){if(e.length>t)return e.length=t,!0}function lS(e){if(!e||"object"!=typeof e)throw Error(lr+"Object expected");var t,r,n,o=["precision",1,1e9,"rounding",0,8,"toExpNeg",-1/0,0,"toExpPos",0,1/0];for(t=0;t<o.length;t+=3)if(void 0!==(n=e[r=o[t]]))if(li(n)===n&&n>=o[t+1]&&n<=o[t+2])this[r]=n;else throw Error(ln+r+": "+n);if(void 0!==(n=e[r="LN10"]))if(n==Math.LN10)this[r]=new this(n);else throw Error(ln+r+": "+n);return this}var s1=function e(t){var r,n,o;function i(e){if(!(this instanceof i))return new i(e);if(this.constructor=i,e instanceof i){this.s=e.s,this.e=e.e,this.d=(e=e.d)?e.slice():e;return}if("number"==typeof e){if(0*e!=0)throw Error(ln+e);if(e>0)this.s=1;else if(e<0)e=-e,this.s=-1;else{this.s=0,this.e=0,this.d=[0];return}if(e===~~e&&e<1e7){this.e=0,this.d=[e];return}return lg(this,e.toString())}if("string"!=typeof e)throw Error(ln+e);if(45===e.charCodeAt(0)?(e=e.slice(1),this.s=-1):this.s=1,lc.test(e))lg(this,e);else throw Error(ln+e)}if(i.prototype=ll,i.ROUND_UP=0,i.ROUND_DOWN=1,i.ROUND_CEIL=2,i.ROUND_FLOOR=3,i.ROUND_HALF_UP=4,i.ROUND_HALF_DOWN=5,i.ROUND_HALF_EVEN=6,i.ROUND_HALF_CEIL=7,i.ROUND_HALF_FLOOR=8,i.clone=e,i.config=i.set=lS,void 0===t&&(t={}),t)for(r=0,o=["precision","rounding","toExpNeg","toExpPos","LN10"];r<o.length;)t.hasOwnProperty(n=o[r++])||(t[n]=this[n]);return i.config(t),i}({precision:20,rounding:4,toExpNeg:-7,toExpPos:21,LN10:"2.302585092994045684017991454684364207601101488628772976033327900967572609677352480235997205089598298341967784042286"});s2=new s1(1);let lP=s1;function lA(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}var lk=function(e){return e},lE={},lN=function(e){return e===lE},lT=function(e){return function t(){return 0==arguments.length||1==arguments.length&&lN(arguments.length<=0?void 0:arguments[0])?t:e.apply(void 0,arguments)}},lM=function(e){return function e(t,r){return 1===t?r:lT(function(){for(var n=arguments.length,o=Array(n),i=0;i<n;i++)o[i]=arguments[i];var a=o.filter(function(e){return e!==lE}).length;return a>=t?r.apply(void 0,o):e(t-a,lT(function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];var i=o.map(function(e){return lN(e)?t.shift():e});return r.apply(void 0,((function(e){if(Array.isArray(e))return lA(e)})(i)||function(e){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(e))return Array.from(e)}(i)||function(e,t){if(e){if("string"==typeof e)return lA(e,void 0);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return lA(e,t)}}(i)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()).concat(t))}))})}(e.length,e)},l_=function(e,t){for(var r=[],n=e;n<t;++n)r[n-e]=n;return r},lC=lM(function(e,t){return Array.isArray(t)?t.map(e):Object.keys(t).map(function(e){return t[e]}).map(e)}),lD=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];if(!t.length)return lk;var n=t.reverse(),o=n[0],i=n.slice(1);return function(){return i.reduce(function(e,t){return t(e)},o.apply(void 0,arguments))}},lI=function(e){return Array.isArray(e)?e.reverse():e.split("").reverse.join("")},lB=function(e){var t=null,r=null;return function(){for(var n=arguments.length,o=Array(n),i=0;i<n;i++)o[i]=arguments[i];return t&&o.every(function(e,r){return e===t[r]})?r:(t=o,r=e.apply(void 0,o))}};lM(function(e,t,r){var n=+e;return n+r*(t-n)}),lM(function(e,t,r){var n=t-e;return(r-e)/(n=n||1/0)}),lM(function(e,t,r){var n=t-e;return Math.max(0,Math.min(1,(r-e)/(n=n||1/0)))});let lR={rangeStep:function(e,t,r){for(var n=new lP(e),o=0,i=[];n.lt(t)&&o<1e5;)i.push(n.toNumber()),n=n.add(r),o++;return i},getDigitCount:function(e){var t;return 0===e?1:Math.floor(new lP(e).abs().log(10).toNumber())+1}};function lL(e){return function(e){if(Array.isArray(e))return lU(e)}(e)||function(e){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(e))return Array.from(e)}(e)||lF(e)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function lz(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(e)){var r=[],n=!0,o=!1,i=void 0;try{for(var a,c=e[Symbol.iterator]();!(n=(a=c.next()).done)&&(r.push(a.value),!t||r.length!==t);n=!0);}catch(e){o=!0,i=e}finally{try{n||null==c.return||c.return()}finally{if(o)throw i}}return r}}(e,t)||lF(e,t)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function lF(e,t){if(e){if("string"==typeof e)return lU(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return lU(e,t)}}function lU(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function l$(e){var t=lz(e,2),r=t[0],n=t[1],o=r,i=n;return r>n&&(o=n,i=r),[o,i]}function lq(e,t,r){if(e.lte(0))return new lP(0);var n=lR.getDigitCount(e.toNumber()),o=new lP(10).pow(n),i=e.div(o),a=1!==n?.05:.1,c=new lP(Math.ceil(i.div(a).toNumber())).add(r).mul(a).mul(o);return t?c:new lP(Math.ceil(c))}function lW(e,t,r){var n=1,o=new lP(e);if(!o.isint()&&r){var i=Math.abs(e);i<1?(n=new lP(10).pow(lR.getDigitCount(e)-1),o=new lP(Math.floor(o.div(n).toNumber())).mul(n)):i>1&&(o=new lP(Math.floor(e)))}else 0===e?o=new lP(Math.floor((t-1)/2)):r||(o=new lP(Math.floor(e)));var a=Math.floor((t-1)/2);return lD(lC(function(e){return o.add(new lP(e-a).mul(n)).toNumber()}),l_)(0,t)}var lH=lB(function(e){var t=lz(e,2),r=t[0],n=t[1],o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6,i=!(arguments.length>2)||void 0===arguments[2]||arguments[2],a=Math.max(o,2),c=lz(l$([r,n]),2),s=c[0],l=c[1];if(s===-1/0||l===1/0){var u=l===1/0?[s].concat(lL(l_(0,o-1).map(function(){return 1/0}))):[].concat(lL(l_(0,o-1).map(function(){return-1/0})),[l]);return r>n?lI(u):u}if(s===l)return lW(s,o,i);var f=function e(t,r,n,o){var i,a=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0;if(!Number.isFinite((r-t)/(n-1)))return{step:new lP(0),tickMin:new lP(0),tickMax:new lP(0)};var c=lq(new lP(r).sub(t).div(n-1),o,a),s=Math.ceil((i=t<=0&&r>=0?new lP(0):(i=new lP(t).add(r).div(2)).sub(new lP(i).mod(c))).sub(t).div(c).toNumber()),l=Math.ceil(new lP(r).sub(i).div(c).toNumber()),u=s+l+1;return u>n?e(t,r,n,o,a+1):(u<n&&(l=r>0?l+(n-u):l,s=r>0?s:s+(n-u)),{step:c,tickMin:i.sub(new lP(s).mul(c)),tickMax:i.add(new lP(l).mul(c))})}(s,l,a,i),p=f.step,d=f.tickMin,h=f.tickMax,y=lR.rangeStep(d,h.add(new lP(.1).mul(p)),p);return r>n?lI(y):y});lB(function(e){var t=lz(e,2),r=t[0],n=t[1],o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6,i=!(arguments.length>2)||void 0===arguments[2]||arguments[2],a=Math.max(o,2),c=lz(l$([r,n]),2),s=c[0],l=c[1];if(s===-1/0||l===1/0)return[r,n];if(s===l)return lW(s,o,i);var u=lq(new lP(l).sub(s).div(a-1),i,0),f=lD(lC(function(e){return new lP(s).add(new lP(e).mul(u)).toNumber()}),l_)(0,a).filter(function(e){return e>=s&&e<=l});return r>n?lI(f):f});var lX=lB(function(e,t){var r=lz(e,2),n=r[0],o=r[1],i=!(arguments.length>2)||void 0===arguments[2]||arguments[2],a=lz(l$([n,o]),2),c=a[0],s=a[1];if(c===-1/0||s===1/0)return[n,o];if(c===s)return[c];var l=Math.max(t,2),u=lq(new lP(s).sub(c).div(l-1),i,0),f=[].concat(lL(lR.rangeStep(new lP(c),new lP(s).sub(new lP(.99).mul(u)),u)),[s]);return n>o?lI(f):f}),lV=["offset","layout","width","dataKey","data","dataPointFormatter","xAxis","yAxis"];function lG(e){return(lG="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function lK(){return(lK=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function lY(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function lZ(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(lZ=function(){return!!e})()}function lJ(e){return(lJ=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function lQ(e,t){return(lQ=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function l0(e,t,r){return(t=l1(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function l1(e){var t=function(e,t){if("object"!=lG(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=lG(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==lG(t)?t:t+""}var l2=function(e){var t;function r(){var e,t;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return e=r,t=arguments,e=lJ(e),function(e,t){if(t&&("object"===lG(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");var r=e;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,lZ()?Reflect.construct(e,t||[],lJ(this).constructor):e.apply(this,t))}if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(e&&e.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),e&&lQ(r,e),t=[{key:"render",value:function(){var e=this.props,t=e.offset,r=e.layout,n=e.width,o=e.dataKey,i=e.data,a=e.dataPointFormatter,c=e.xAxis,s=e.yAxis,l=tg(function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,lV),!1);"x"===this.props.direction&&"number"!==c.type&&ni(!1);var f=i.map(function(e){var i,f,p=a(e,o),d=p.x,h=p.y,y=p.value,m=p.errorVal;if(!m)return null;var v=[];if(Array.isArray(m)){var b=function(e){if(Array.isArray(e))return e}(m)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,c=[],s=!0,l=!1;try{i=(r=r.call(e)).next,!1;for(;!(s=(n=i.call(r)).done)&&(c.push(n.value),c.length!==t);s=!0);}catch(e){l=!0,o=e}finally{try{if(!s&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(m,2)||function(e,t){if(e){if("string"==typeof e)return lY(e,2);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return lY(e,t)}}(m,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}();i=b[0],f=b[1]}else i=f=m;if("vertical"===r){var g=c.scale,x=h+t,w=x+n,j=x-n,O=g(y-i),S=g(y+f);v.push({x1:S,y1:w,x2:S,y2:j}),v.push({x1:O,y1:x,x2:S,y2:x}),v.push({x1:O,y1:w,x2:O,y2:j})}else if("horizontal"===r){var P=s.scale,A=d+t,k=A-n,E=A+n,N=P(y-i),T=P(y+f);v.push({x1:k,y1:T,x2:E,y2:T}),v.push({x1:A,y1:N,x2:A,y2:T}),v.push({x1:k,y1:N,x2:E,y2:N})}return u().createElement(ns,lK({className:"recharts-errorBar",key:"bar-".concat(v.map(function(e){return"".concat(e.x1,"-").concat(e.x2,"-").concat(e.y1,"-").concat(e.y2)}))},l),v.map(function(e){return u().createElement("line",lK({},e,{key:"line-".concat(e.x1,"-").concat(e.x2,"-").concat(e.y1,"-").concat(e.y2)}))}))});return u().createElement(ns,{className:"recharts-errorBars"},f)}}],function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,l1(n.key),n)}}(r.prototype,t),Object.defineProperty(r,"prototype",{writable:!1}),r}(u().Component);function l5(e){return(l5="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function l4(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function l3(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?l4(Object(r),!0).forEach(function(t){var n,o,i;n=e,o=t,i=r[t],(o=function(e){var t=function(e,t){if("object"!=l5(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=l5(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==l5(t)?t:t+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):l4(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}l0(l2,"defaultProps",{stroke:"black",strokeWidth:1.5,width:5,offset:0,layout:"horizontal"}),l0(l2,"displayName","ErrorBar");var l6=function(e){var t,r=e.children,n=e.formattedGraphicalItems,o=e.legendWidth,i=e.legendContent,a=ty(r,r4);if(!a)return null;var c=r4.defaultProps,s=void 0!==c?l3(l3({},c),a.props):{};return t=a.props&&a.props.payload?a.props&&a.props.payload:"children"===i?(n||[]).reduce(function(e,t){var r=t.item,n=t.props,o=n.sectors||n.data||[];return e.concat(o.map(function(e){return{type:a.props.iconType||r.props.legendType,value:e.name,color:e.fill,payload:e}}))},[]):(n||[]).map(function(e){var t=e.item,r=t.type.defaultProps,n=void 0!==r?l3(l3({},r),t.props):{},o=n.dataKey,i=n.name,a=n.legendType;return{inactive:n.hide,dataKey:o,type:s.iconType||a||"square",color:ua(t),value:i||o,payload:n}}),l3(l3(l3({},s),r4.getWithHeight(a,o)),{},{payload:t,item:a})};function l8(e){return(l8="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function l7(e){return function(e){if(Array.isArray(e))return l9(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return l9(e,void 0);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return l9(e,t)}}(e)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function l9(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function ue(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function ut(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ue(Object(r),!0).forEach(function(t){ur(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ue(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function ur(e,t,r){var n;return(n=function(e,t){if("object"!=l8(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=l8(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"),(t="symbol"==l8(n)?n:n+"")in e)?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function un(e,t,r){return e1()(e)||e1()(t)?r:eW(t)?eL()(e,t,r):e5()(t)?t(e):r}function uo(e,t,r,n){var o=s7()(e,function(e){return un(e,t)});if("number"===r){var i=o.filter(function(e){return eq(e)||parseFloat(e)});return i.length?[s6()(i),s4()(i)]:[1/0,-1/0]}return(n?o.filter(function(e){return!e1()(e)}):o).map(function(e){return eW(e)||e instanceof Date?e:""})}var ui=function(e){var t,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=arguments.length>2?arguments[2]:void 0,o=arguments.length>3?arguments[3]:void 0,i=-1,a=null!=(t=null==r?void 0:r.length)?t:0;if(a<=1)return 0;if(o&&"angleAxis"===o.axisType&&1e-6>=Math.abs(Math.abs(o.range[1]-o.range[0])-360))for(var c=o.range,s=0;s<a;s++){var l=s>0?n[s-1].coordinate:n[a-1].coordinate,u=n[s].coordinate,f=s>=a-1?n[0].coordinate:n[s+1].coordinate,p=void 0;if(eU(u-l)!==eU(f-u)){var d=[];if(eU(f-u)===eU(c[1]-c[0])){p=f;var h=u+c[1]-c[0];d[0]=Math.min(h,(h+l)/2),d[1]=Math.max(h,(h+l)/2)}else{p=l;var y=f+c[1]-c[0];d[0]=Math.min(u,(y+u)/2),d[1]=Math.max(u,(y+u)/2)}var m=[Math.min(u,(p+u)/2),Math.max(u,(p+u)/2)];if(e>m[0]&&e<=m[1]||e>=d[0]&&e<=d[1]){i=n[s].index;break}}else{var v=Math.min(l,f),b=Math.max(l,f);if(e>(v+u)/2&&e<=(b+u)/2){i=n[s].index;break}}}else for(var g=0;g<a;g++)if(0===g&&e<=(r[g].coordinate+r[g+1].coordinate)/2||g>0&&g<a-1&&e>(r[g].coordinate+r[g-1].coordinate)/2&&e<=(r[g].coordinate+r[g+1].coordinate)/2||g===a-1&&e>(r[g].coordinate+r[g-1].coordinate)/2){i=r[g].index;break}return i},ua=function(e){var t,r,n=e.type.displayName,o=null!=(t=e.type)&&t.defaultProps?ut(ut({},e.type.defaultProps),e.props):e.props,i=o.stroke,a=o.fill;switch(n){case"Line":r=i;break;case"Area":case"Radar":r=i&&"none"!==i?i:a;break;default:r=a}return r},uc=function(e){var t=e.barSize,r=e.totalSize,n=e.stackGroups,o=void 0===n?{}:n;if(!o)return{};for(var i={},a=Object.keys(o),c=0,s=a.length;c<s;c++)for(var l=o[a[c]].stackGroups,u=Object.keys(l),f=0,p=u.length;f<p;f++){var d=l[u[f]],h=d.items,y=d.cateAxisId,m=h.filter(function(e){return tu(e.type).indexOf("Bar")>=0});if(m&&m.length){var v=m[0].type.defaultProps,b=void 0!==v?ut(ut({},v),m[0].props):m[0].props,g=b.barSize,x=b[y];i[x]||(i[x]=[]);var w=e1()(g)?t:g;i[x].push({item:m[0],stackList:m.slice(1),barSize:e1()(w)?void 0:eV(w,r,0)})}}return i},us=function(e){var t,r=e.barGap,n=e.barCategoryGap,o=e.bandSize,i=e.sizeList,a=void 0===i?[]:i,c=e.maxBarSize,s=a.length;if(s<1)return null;var l=eV(r,o,0,!0),u=[];if(a[0].barSize===+a[0].barSize){var f=!1,p=o/s,d=a.reduce(function(e,t){return e+t.barSize||0},0);(d+=(s-1)*l)>=o&&(d-=(s-1)*l,l=0),d>=o&&p>0&&(f=!0,p*=.9,d=s*p);var h={offset:((o-d)/2|0)-l,size:0};t=a.reduce(function(e,t){var r={item:t.item,position:{offset:h.offset+h.size+l,size:f?p:t.barSize}},n=[].concat(l7(e),[r]);return h=n[n.length-1].position,t.stackList&&t.stackList.length&&t.stackList.forEach(function(e){n.push({item:e,position:h})}),n},u)}else{var y=eV(n,o,0,!0);o-2*y-(s-1)*l<=0&&(l=0);var m=(o-2*y-(s-1)*l)/s;m>1&&(m>>=0);var v=c===+c?Math.min(m,c):m;t=a.reduce(function(e,t,r){var n=[].concat(l7(e),[{item:t.item,position:{offset:y+(m+l)*r+(m-v)/2,size:v}}]);return t.stackList&&t.stackList.length&&t.stackList.forEach(function(e){n.push({item:e,position:n[n.length-1].position})}),n},u)}return t},ul=function(e,t,r,n){var o=r.children,i=r.width,a=r.margin,c=l6({children:o,legendWidth:i-(a.left||0)-(a.right||0)});if(c){var s=n||{},l=s.width,u=s.height,f=c.align,p=c.verticalAlign,d=c.layout;if(("vertical"===d||"horizontal"===d&&"middle"===p)&&"center"!==f&&eq(e[f]))return ut(ut({},e),{},ur({},f,e[f]+(l||0)));if(("horizontal"===d||"vertical"===d&&"center"===f)&&"middle"!==p&&eq(e[p]))return ut(ut({},e),{},ur({},p,e[p]+(u||0)))}return e},uu=function(e,t,r,n,o){var i=th(t.props.children,l2).filter(function(e){var t;return t=e.props.direction,!!e1()(o)||("horizontal"===n?"yAxis"===o:"vertical"===n||"x"===t?"xAxis"===o:"y"!==t||"yAxis"===o)});if(i&&i.length){var a=i.map(function(e){return e.props.dataKey});return e.reduce(function(e,t){var n=un(t,r);if(e1()(n))return e;var o=Array.isArray(n)?[s6()(n),s4()(n)]:[n,n],i=a.reduce(function(e,r){var n=un(t,r,0),i=o[0]-Math.abs(Array.isArray(n)?n[0]:n),a=o[1]+Math.abs(Array.isArray(n)?n[1]:n);return[Math.min(i,e[0]),Math.max(a,e[1])]},[1/0,-1/0]);return[Math.min(i[0],e[0]),Math.max(i[1],e[1])]},[1/0,-1/0])}return null},uf=function(e,t,r,n,o){var i=t.map(function(t){return uu(e,t,r,o,n)}).filter(function(e){return!e1()(e)});return i&&i.length?i.reduce(function(e,t){return[Math.min(e[0],t[0]),Math.max(e[1],t[1])]},[1/0,-1/0]):null},up=function(e,t,r,n,o){var i=t.map(function(t){var i=t.props.dataKey;return"number"===r&&i&&uu(e,t,i,n)||uo(e,i,r,o)});if("number"===r)return i.reduce(function(e,t){return[Math.min(e[0],t[0]),Math.max(e[1],t[1])]},[1/0,-1/0]);var a={};return i.reduce(function(e,t){for(var r=0,n=t.length;r<n;r++)a[t[r]]||(a[t[r]]=!0,e.push(t[r]));return e},[])},ud=function(e,t){return"horizontal"===e&&"xAxis"===t||"vertical"===e&&"yAxis"===t||"centric"===e&&"angleAxis"===t||"radial"===e&&"radiusAxis"===t},uh=function(e,t,r,n){if(n)return e.map(function(e){return e.coordinate});var o,i,a=e.map(function(e){return e.coordinate===t&&(o=!0),e.coordinate===r&&(i=!0),e.coordinate});return o||a.push(t),i||a.push(r),a},uy=function(e,t,r){if(!e)return null;var n=e.scale,o=e.duplicateDomain,i=e.type,a=e.range,c="scaleBand"===e.realScaleType?n.bandwidth()/2:2,s=(t||r)&&"category"===i&&n.bandwidth?n.bandwidth()/c:0;return(s="angleAxis"===e.axisType&&(null==a?void 0:a.length)>=2?2*eU(a[0]-a[1])*s:s,t&&(e.ticks||e.niceTicks))?(e.ticks||e.niceTicks).map(function(e){return{coordinate:n(o?o.indexOf(e):e)+s,value:e,offset:s}}).filter(function(e){return!eB()(e.coordinate)}):e.isCategorical&&e.categoricalDomain?e.categoricalDomain.map(function(e,t){return{coordinate:n(e)+s,value:e,index:t,offset:s}}):n.ticks&&!r?n.ticks(e.tickCount).map(function(e){return{coordinate:n(e)+s,value:e,offset:s}}):n.domain().map(function(e,t){return{coordinate:n(e)+s,value:o?o[e]:e,index:t,offset:s}})},um=new WeakMap,uv=function(e,t){if("function"!=typeof t)return e;um.has(e)||um.set(e,new WeakMap);var r=um.get(e);if(r.has(t))return r.get(t);var n=function(){e.apply(void 0,arguments),t.apply(void 0,arguments)};return r.set(t,n),n},ub=function(e,t,r){var o=e.scale,i=e.type,a=e.layout,c=e.axisType;if("auto"===o)return"radial"===a&&"radiusAxis"===c?{scale:oR(),realScaleType:"band"}:"radial"===a&&"angleAxis"===c?{scale:aP(),realScaleType:"linear"}:"category"===i&&t&&(t.indexOf("LineChart")>=0||t.indexOf("AreaChart")>=0||t.indexOf("ComposedChart")>=0&&!r)?{scale:oL(),realScaleType:"point"}:"category"===i?{scale:oR(),realScaleType:"band"}:{scale:aP(),realScaleType:"linear"};if(eD()(o)){var s="scale".concat(ra()(o));return{scale:(n[s]||oL)(),realScaleType:n[s]?s:"point"}}return e5()(o)?{scale:o}:{scale:oL(),realScaleType:"point"}},ug=function(e){var t=e.domain();if(t&&!(t.length<=2)){var r=t.length,n=e.range(),o=Math.min(n[0],n[1])-1e-4,i=Math.max(n[0],n[1])+1e-4,a=e(t[0]),c=e(t[r-1]);(a<o||a>i||c<o||c>i)&&e.domain([t[0],t[r-1]])}},ux=function(e,t){if(!e)return null;for(var r=0,n=e.length;r<n;r++)if(e[r].item===t)return e[r].position;return null},uw=function(e,t){if(!t||2!==t.length||!eq(t[0])||!eq(t[1]))return e;var r=Math.min(t[0],t[1]),n=Math.max(t[0],t[1]),o=[e[0],e[1]];return(!eq(e[0])||e[0]<r)&&(o[0]=r),(!eq(e[1])||e[1]>n)&&(o[1]=n),o[0]>n&&(o[0]=n),o[1]<r&&(o[1]=r),o},uj={sign:function(e){var t=e.length;if(!(t<=0))for(var r=0,n=e[0].length;r<n;++r)for(var o=0,i=0,a=0;a<t;++a){var c=eB()(e[a][r][1])?e[a][r][0]:e[a][r][1];c>=0?(e[a][r][0]=o,e[a][r][1]=o+c,o=e[a][r][1]):(e[a][r][0]=i,e[a][r][1]=i+c,i=e[a][r][1])}},expand:function(e,t){if((n=e.length)>0){for(var r,n,o,i=0,a=e[0].length;i<a;++i){for(o=r=0;r<n;++r)o+=e[r][i][1]||0;if(o)for(r=0;r<n;++r)e[r][i][1]/=o}sq(e,t)}},none:sq,silhouette:function(e,t){if((r=e.length)>0){for(var r,n=0,o=e[t[0]],i=o.length;n<i;++n){for(var a=0,c=0;a<r;++a)c+=e[a][n][1]||0;o[n][1]+=o[n][0]=-c/2}sq(e,t)}},wiggle:function(e,t){if((o=e.length)>0&&(n=(r=e[t[0]]).length)>0){for(var r,n,o,i=0,a=1;a<n;++a){for(var c=0,s=0,l=0;c<o;++c){for(var u=e[t[c]],f=u[a][1]||0,p=(f-(u[a-1][1]||0))/2,d=0;d<c;++d){var h=e[t[d]];p+=(h[a][1]||0)-(h[a-1][1]||0)}s+=f,l+=p*f}r[a-1][1]+=r[a-1][0]=i,s&&(i-=l/s)}r[a-1][1]+=r[a-1][0]=i,sq(e,t)}},positive:function(e){var t=e.length;if(!(t<=0))for(var r=0,n=e[0].length;r<n;++r)for(var o=0,i=0;i<t;++i){var a=eB()(e[i][r][1])?e[i][r][0]:e[i][r][1];a>=0?(e[i][r][0]=o,e[i][r][1]=o+a,o=e[i][r][1]):(e[i][r][0]=0,e[i][r][1]=0)}}},uO=function(e,t,r){var n=t.map(function(e){return e.props.dataKey}),o=uj[r];return(function(){var e=rj([]),t=sH,r=sq,n=sX;function o(o){var i,a,c=Array.from(e.apply(this,arguments),sV),s=c.length,l=-1;for(let e of o)for(i=0,++l;i<s;++i)(c[i][l]=[0,+n(e,c[i].key,l,o)]).data=e;for(i=0,a=sW(t(c));i<s;++i)c[a[i]].index=i;return r(c,a),c}return o.keys=function(t){return arguments.length?(e="function"==typeof t?t:rj(Array.from(t)),o):e},o.value=function(e){return arguments.length?(n="function"==typeof e?e:rj(+e),o):n},o.order=function(e){return arguments.length?(t=null==e?sH:"function"==typeof e?e:rj(Array.from(e)),o):t},o.offset=function(e){return arguments.length?(r=null==e?sq:e,o):r},o})().keys(n).value(function(e,t){return+un(e,t,0)}).order(sH).offset(o)(e)},uS=function(e,t,r,n,o,i){if(!e)return null;var a=(i?t.reverse():t).reduce(function(e,t){var o,i=null!=(o=t.type)&&o.defaultProps?ut(ut({},t.type.defaultProps),t.props):t.props,a=i.stackId;if(i.hide)return e;var c=i[r],s=e[c]||{hasStack:!1,stackGroups:{}};if(eW(a)){var l=s.stackGroups[a]||{numericAxisId:r,cateAxisId:n,items:[]};l.items.push(t),s.hasStack=!0,s.stackGroups[a]=l}else s.stackGroups[eX("_stackId_")]={numericAxisId:r,cateAxisId:n,items:[t]};return ut(ut({},e),{},ur({},c,s))},{});return Object.keys(a).reduce(function(t,i){var c=a[i];return c.hasStack&&(c.stackGroups=Object.keys(c.stackGroups).reduce(function(t,i){var a=c.stackGroups[i];return ut(ut({},t),{},ur({},i,{numericAxisId:r,cateAxisId:n,items:a.items,stackedData:uO(e,a.items,o)}))},{})),ut(ut({},t),{},ur({},i,c))},{})},uP=function(e,t){var r=t.realScaleType,n=t.type,o=t.tickCount,i=t.originalDomain,a=t.allowDecimals,c=r||t.scale;if("auto"!==c&&"linear"!==c)return null;if(o&&"number"===n&&i&&("auto"===i[0]||"auto"===i[1])){var s=e.domain();if(!s.length)return null;var l=lH(s,o,a);return e.domain([s6()(l),s4()(l)]),{niceTicks:l}}return o&&"number"===n?{niceTicks:lX(e.domain(),o,a)}:null};function uA(e){var t=e.axis,r=e.ticks,n=e.bandSize,o=e.entry,i=e.index,a=e.dataKey;if("category"===t.type){if(!t.allowDuplicatedCategory&&t.dataKey&&!e1()(o[t.dataKey])){var c=eZ(r,"value",o[t.dataKey]);if(c)return c.coordinate+n/2}return r[i]?r[i].coordinate+n/2:null}var s=un(o,e1()(a)?t.dataKey:a);return e1()(s)?null:t.scale(s)}var uk=function(e){var t=e.axis,r=e.ticks,n=e.offset,o=e.bandSize,i=e.entry,a=e.index;if("category"===t.type)return r[a]?r[a].coordinate+n:null;var c=un(i,t.dataKey,t.domain[a]);return e1()(c)?null:t.scale(c)-o/2+n},uE=function(e){var t=e.numericAxis,r=t.scale.domain();if("number"===t.type){var n=Math.min(r[0],r[1]),o=Math.max(r[0],r[1]);return n<=0&&o>=0?0:o<0?o:n}return r[0]},uN=function(e,t){var r,n=(null!=(r=e.type)&&r.defaultProps?ut(ut({},e.type.defaultProps),e.props):e.props).stackId;if(eW(n)){var o=t[n];if(o){var i=o.items.indexOf(e);return i>=0?o.stackedData[i]:null}}return null},uT=function(e,t,r){return Object.keys(e).reduce(function(n,o){var i=e[o].stackedData.reduce(function(e,n){var o=n.slice(t,r+1).reduce(function(e,t){return[s6()(t.concat([e[0]]).filter(eq)),s4()(t.concat([e[1]]).filter(eq))]},[1/0,-1/0]);return[Math.min(e[0],o[0]),Math.max(e[1],o[1])]},[1/0,-1/0]);return[Math.min(i[0],n[0]),Math.max(i[1],n[1])]},[1/0,-1/0]).map(function(e){return e===1/0||e===-1/0?0:e})},uM=/^dataMin[\s]*-[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,u_=/^dataMax[\s]*\+[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,uC=function(e,t,r){if(e5()(e))return e(t,r);if(!Array.isArray(e))return t;var n=[];if(eq(e[0]))n[0]=r?e[0]:Math.min(e[0],t[0]);else if(uM.test(e[0])){var o=+uM.exec(e[0])[1];n[0]=t[0]-o}else e5()(e[0])?n[0]=e[0](t[0]):n[0]=t[0];if(eq(e[1]))n[1]=r?e[1]:Math.max(e[1],t[1]);else if(u_.test(e[1])){var i=+u_.exec(e[1])[1];n[1]=t[1]+i}else e5()(e[1])?n[1]=e[1](t[1]):n[1]=t[1];return n},uD=function(e,t,r){if(e&&e.scale&&e.scale.bandwidth){var n=e.scale.bandwidth();if(!r||n>0)return n}if(e&&t&&t.length>=2){for(var o=tT()(t,function(e){return e.coordinate}),i=1/0,a=1,c=o.length;a<c;a++){var s=o[a],l=o[a-1];i=Math.min((s.coordinate||0)-(l.coordinate||0),i)}return i===1/0?0:i}return r?void 0:0},uI=function(e,t,r){return!e||!e.length||le()(e,eL()(r,"type.defaultProps.domain"))?t:e},uB=function(e,t){var r=e.type.defaultProps?ut(ut({},e.type.defaultProps),e.props):e.props,n=r.dataKey,o=r.name,i=r.unit,a=r.formatter,c=r.tooltipType,s=r.chartType,l=r.hide;return ut(ut({},tg(e,!1)),{},{dataKey:n,unit:i,formatter:a,name:o||n,color:ua(e),value:un(t,n),type:c,payload:t,chartType:s,hide:l})};function uR(e){return(uR="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function uL(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function uz(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?uL(Object(r),!0).forEach(function(t){uF(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):uL(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function uF(e,t,r){var n;return(n=function(e,t){if("object"!=uR(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=uR(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"),(t="symbol"==uR(n)?n:n+"")in e)?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var uU=["Webkit","Moz","O","ms"],u$=function(e,t){if(!e)return null;var r=e.replace(/(\w)/,function(e){return e.toUpperCase()}),n=uU.reduce(function(e,n){return uz(uz({},e),{},uF({},n+r,t))},{});return n[e]=t,n};function uq(e){return(uq="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function uW(){return(uW=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function uH(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function uX(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?uH(Object(r),!0).forEach(function(t){uZ(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):uH(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function uV(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,uJ(n.key),n)}}function uG(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(uG=function(){return!!e})()}function uK(e){return(uK=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function uY(e,t){return(uY=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function uZ(e,t,r){return(t=uJ(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function uJ(e){var t=function(e,t){if("object"!=uq(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=uq(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==uq(t)?t:t+""}var uQ=function(e){var t=e.data,r=e.startIndex,n=e.endIndex,o=e.x,i=e.width,a=e.travellerWidth;if(!t||!t.length)return{};var c=t.length,s=oL().domain(no()(0,c)).range([o,o+i-a]),l=s.domain().map(function(e){return s(e)});return{isTextActive:!1,isSlideMoving:!1,isTravellerMoving:!1,isTravellerFocused:!1,startX:s(r),endX:s(n),scale:s,scaleValues:l}},u0=function(e){return e.changedTouches&&!!e.changedTouches.length},u1=function(e){var t,r;function n(e){var t,r,o;if(!(this instanceof n))throw TypeError("Cannot call a class as a function");return r=n,o=[e],r=uK(r),uZ(t=function(e,t){if(t&&("object"===uq(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");var r=e;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,uG()?Reflect.construct(r,o||[],uK(this).constructor):r.apply(this,o)),"handleDrag",function(e){t.leaveTimer&&(clearTimeout(t.leaveTimer),t.leaveTimer=null),t.state.isTravellerMoving?t.handleTravellerMove(e):t.state.isSlideMoving&&t.handleSlideDrag(e)}),uZ(t,"handleTouchMove",function(e){null!=e.changedTouches&&e.changedTouches.length>0&&t.handleDrag(e.changedTouches[0])}),uZ(t,"handleDragEnd",function(){t.setState({isTravellerMoving:!1,isSlideMoving:!1},function(){var e=t.props,r=e.endIndex,n=e.onDragEnd,o=e.startIndex;null==n||n({endIndex:r,startIndex:o})}),t.detachDragEndListener()}),uZ(t,"handleLeaveWrapper",function(){(t.state.isTravellerMoving||t.state.isSlideMoving)&&(t.leaveTimer=window.setTimeout(t.handleDragEnd,t.props.leaveTimeOut))}),uZ(t,"handleEnterSlideOrTraveller",function(){t.setState({isTextActive:!0})}),uZ(t,"handleLeaveSlideOrTraveller",function(){t.setState({isTextActive:!1})}),uZ(t,"handleSlideDragStart",function(e){var r=u0(e)?e.changedTouches[0]:e;t.setState({isTravellerMoving:!1,isSlideMoving:!0,slideMoveStartX:r.pageX}),t.attachDragEndListener()}),t.travellerDragStartHandlers={startX:t.handleTravellerDragStart.bind(t,"startX"),endX:t.handleTravellerDragStart.bind(t,"endX")},t.state={},t}if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");return n.prototype=Object.create(e&&e.prototype,{constructor:{value:n,writable:!0,configurable:!0}}),Object.defineProperty(n,"prototype",{writable:!1}),e&&uY(n,e),t=[{key:"componentWillUnmount",value:function(){this.leaveTimer&&(clearTimeout(this.leaveTimer),this.leaveTimer=null),this.detachDragEndListener()}},{key:"getIndex",value:function(e){var t=e.startX,r=e.endX,o=this.state.scaleValues,i=this.props,a=i.gap,c=i.data.length-1,s=Math.min(t,r),l=Math.max(t,r),u=n.getIndexInRange(o,s),f=n.getIndexInRange(o,l);return{startIndex:u-u%a,endIndex:f===c?c:f-f%a}}},{key:"getTextOfTick",value:function(e){var t=this.props,r=t.data,n=t.tickFormatter,o=t.dataKey,i=un(r[e],o,e);return e5()(n)?n(i,e):i}},{key:"attachDragEndListener",value:function(){window.addEventListener("mouseup",this.handleDragEnd,!0),window.addEventListener("touchend",this.handleDragEnd,!0),window.addEventListener("mousemove",this.handleDrag,!0)}},{key:"detachDragEndListener",value:function(){window.removeEventListener("mouseup",this.handleDragEnd,!0),window.removeEventListener("touchend",this.handleDragEnd,!0),window.removeEventListener("mousemove",this.handleDrag,!0)}},{key:"handleSlideDrag",value:function(e){var t=this.state,r=t.slideMoveStartX,n=t.startX,o=t.endX,i=this.props,a=i.x,c=i.width,s=i.travellerWidth,l=i.startIndex,u=i.endIndex,f=i.onChange,p=e.pageX-r;p>0?p=Math.min(p,a+c-s-o,a+c-s-n):p<0&&(p=Math.max(p,a-n,a-o));var d=this.getIndex({startX:n+p,endX:o+p});(d.startIndex!==l||d.endIndex!==u)&&f&&f(d),this.setState({startX:n+p,endX:o+p,slideMoveStartX:e.pageX})}},{key:"handleTravellerDragStart",value:function(e,t){var r=u0(t)?t.changedTouches[0]:t;this.setState({isSlideMoving:!1,isTravellerMoving:!0,movingTravellerId:e,brushMoveStartX:r.pageX}),this.attachDragEndListener()}},{key:"handleTravellerMove",value:function(e){var t=this.state,r=t.brushMoveStartX,n=t.movingTravellerId,o=t.endX,i=t.startX,a=this.state[n],c=this.props,s=c.x,l=c.width,u=c.travellerWidth,f=c.onChange,p=c.gap,d=c.data,h={startX:this.state.startX,endX:this.state.endX},y=e.pageX-r;y>0?y=Math.min(y,s+l-u-a):y<0&&(y=Math.max(y,s-a)),h[n]=a+y;var m=this.getIndex(h),v=m.startIndex,b=m.endIndex,g=function(){var e=d.length-1;return"startX"===n&&(o>i?v%p==0:b%p==0)||!!(o<i)&&b===e||"endX"===n&&(o>i?b%p==0:v%p==0)||!!(o>i)&&b===e};this.setState(uZ(uZ({},n,a+y),"brushMoveStartX",e.pageX),function(){f&&g()&&f(m)})}},{key:"handleTravellerMoveKeyboard",value:function(e,t){var r=this,n=this.state,o=n.scaleValues,i=n.startX,a=n.endX,c=this.state[t],s=o.indexOf(c);if(-1!==s){var l=s+e;if(-1!==l&&!(l>=o.length)){var u=o[l];"startX"===t&&u>=a||"endX"===t&&u<=i||this.setState(uZ({},t,u),function(){r.props.onChange(r.getIndex({startX:r.state.startX,endX:r.state.endX}))})}}}},{key:"renderBackground",value:function(){var e=this.props,t=e.x,r=e.y,n=e.width,o=e.height,i=e.fill,a=e.stroke;return u().createElement("rect",{stroke:a,fill:i,x:t,y:r,width:n,height:o})}},{key:"renderPanorama",value:function(){var e=this.props,t=e.x,r=e.y,n=e.width,o=e.height,i=e.data,a=e.children,c=e.padding,s=l.Children.only(a);return s?u().cloneElement(s,{x:t,y:r,width:n,height:o,margin:c,compact:!0,data:i}):null}},{key:"renderTravellerLayer",value:function(e,t){var r,o,i=this,a=this.props,c=a.y,s=a.travellerWidth,l=a.height,f=a.traveller,p=a.ariaLabel,d=a.data,h=a.startIndex,y=a.endIndex,m=Math.max(e,this.props.x),v=uX(uX({},tg(this.props,!1)),{},{x:m,y:c,width:s,height:l}),b=p||"Min value: ".concat(null==(r=d[h])?void 0:r.name,", Max value: ").concat(null==(o=d[y])?void 0:o.name);return u().createElement(ns,{tabIndex:0,role:"slider","aria-label":b,"aria-valuenow":e,className:"recharts-brush-traveller",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.travellerDragStartHandlers[t],onTouchStart:this.travellerDragStartHandlers[t],onKeyDown:function(e){["ArrowLeft","ArrowRight"].includes(e.key)&&(e.preventDefault(),e.stopPropagation(),i.handleTravellerMoveKeyboard("ArrowRight"===e.key?1:-1,t))},onFocus:function(){i.setState({isTravellerFocused:!0})},onBlur:function(){i.setState({isTravellerFocused:!1})},style:{cursor:"col-resize"}},n.renderTraveller(f,v))}},{key:"renderSlide",value:function(e,t){var r=this.props,n=r.y,o=r.height,i=r.stroke,a=r.travellerWidth,c=Math.min(e,t)+a,s=Math.max(Math.abs(t-e)-a,0);return u().createElement("rect",{className:"recharts-brush-slide",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.handleSlideDragStart,onTouchStart:this.handleSlideDragStart,style:{cursor:"move"},stroke:"none",fill:i,fillOpacity:.2,x:c,y:n,width:s,height:o})}},{key:"renderText",value:function(){var e=this.props,t=e.startIndex,r=e.endIndex,n=e.y,o=e.height,i=e.travellerWidth,a=e.stroke,c=this.state,s=c.startX,l=c.endX,f={pointerEvents:"none",fill:a};return u().createElement(ns,{className:"recharts-brush-texts"},u().createElement(iu,uW({textAnchor:"end",verticalAnchor:"middle",x:Math.min(s,l)-5,y:n+o/2},f),this.getTextOfTick(t)),u().createElement(iu,uW({textAnchor:"start",verticalAnchor:"middle",x:Math.max(s,l)+i+5,y:n+o/2},f),this.getTextOfTick(r)))}},{key:"render",value:function(){var e=this.props,t=e.data,r=e.className,n=e.children,o=e.x,i=e.y,a=e.width,c=e.height,s=e.alwaysShowText,l=this.state,f=l.startX,p=l.endX,d=l.isTextActive,h=l.isSlideMoving,y=l.isTravellerMoving,m=l.isTravellerFocused;if(!t||!t.length||!eq(o)||!eq(i)||!eq(a)||!eq(c)||a<=0||c<=0)return null;var v=(0,eT.A)("recharts-brush",r),b=1===u().Children.count(n),g=u$("userSelect","none");return u().createElement(ns,{className:v,onMouseLeave:this.handleLeaveWrapper,onTouchMove:this.handleTouchMove,style:g},this.renderBackground(),b&&this.renderPanorama(),this.renderSlide(f,p),this.renderTravellerLayer(f,"startX"),this.renderTravellerLayer(p,"endX"),(d||h||y||m||s)&&this.renderText())}}],r=[{key:"renderDefaultTraveller",value:function(e){var t=e.x,r=e.y,n=e.width,o=e.height,i=e.stroke,a=Math.floor(r+o/2)-1;return u().createElement(u().Fragment,null,u().createElement("rect",{x:t,y:r,width:n,height:o,fill:i,stroke:"none"}),u().createElement("line",{x1:t+1,y1:a,x2:t+n-1,y2:a,fill:"none",stroke:"#fff"}),u().createElement("line",{x1:t+1,y1:a+2,x2:t+n-1,y2:a+2,fill:"none",stroke:"#fff"}))}},{key:"renderTraveller",value:function(e,t){var r;return u().isValidElement(e)?u().cloneElement(e,t):e5()(e)?e(t):n.renderDefaultTraveller(t)}},{key:"getDerivedStateFromProps",value:function(e,t){var r=e.data,n=e.width,o=e.x,i=e.travellerWidth,a=e.updateId,c=e.startIndex,s=e.endIndex;if(r!==t.prevData||a!==t.prevUpdateId)return uX({prevData:r,prevTravellerWidth:i,prevUpdateId:a,prevX:o,prevWidth:n},r&&r.length?uQ({data:r,width:n,x:o,travellerWidth:i,startIndex:c,endIndex:s}):{scale:null,scaleValues:null});if(t.scale&&(n!==t.prevWidth||o!==t.prevX||i!==t.prevTravellerWidth)){t.scale.range([o,o+n-i]);var l=t.scale.domain().map(function(e){return t.scale(e)});return{prevData:r,prevTravellerWidth:i,prevUpdateId:a,prevX:o,prevWidth:n,startX:t.scale(e.startIndex),endX:t.scale(e.endIndex),scaleValues:l}}return null}},{key:"getIndexInRange",value:function(e,t){for(var r=e.length,n=0,o=r-1;o-n>1;){var i=Math.floor((n+o)/2);e[i]>t?o=i:n=i}return t>=e[o]?o:n}}],t&&uV(n.prototype,t),r&&uV(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(l.PureComponent);function u2(e){return(u2="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function u5(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function u4(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?u5(Object(r),!0).forEach(function(t){(function(e,t,r){var n;(n=function(e,t){if("object"!=u2(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=u2(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"),(t="symbol"==u2(n)?n:n+"")in e)?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r})(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):u5(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}uZ(u1,"displayName","Brush"),uZ(u1,"defaultProps",{height:40,travellerWidth:5,gap:1,fill:"#fff",stroke:"#666",padding:{top:1,right:1,bottom:1,left:1},leaveTimeOut:1e3,alwaysShowText:!1});var u3=Math.PI/180,u6=function(e,t,r,n){return{x:e+Math.cos(-u3*n)*r,y:t+Math.sin(-u3*n)*r}},u8=function(e,t){var r=e.x,n=e.y;return Math.sqrt(Math.pow(r-t.x,2)+Math.pow(n-t.y,2))},u7=function(e,t){var r=e.x,n=e.y,o=t.cx,i=t.cy,a=u8({x:r,y:n},{x:o,y:i});if(a<=0)return{radius:a};var c=Math.acos((r-o)/a);return n>i&&(c=2*Math.PI-c),{radius:a,angle:180*c/Math.PI,angleInRadian:c}},u9=function(e){var t=e.startAngle,r=e.endAngle,n=Math.min(Math.floor(t/360),Math.floor(r/360));return{startAngle:t-360*n,endAngle:r-360*n}},fe=function(e,t){var r,n=u7({x:e.x,y:e.y},t),o=n.radius,i=n.angle,a=t.innerRadius,c=t.outerRadius;if(o<a||o>c)return!1;if(0===o)return!0;var s=u9(t),l=s.startAngle,u=s.endAngle,f=i;if(l<=u){for(;f>u;)f-=360;for(;f<l;)f+=360;r=f>=l&&f<=u}else{for(;f>l;)f-=360;for(;f<u;)f+=360;r=f>=u&&f<=l}return r?u4(u4({},t),{},{radius:o,angle:f+360*Math.min(Math.floor(t.startAngle/360),Math.floor(t.endAngle/360))}):null};function ft(e){return(ft="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var fr=["offset"];function fn(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function fo(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function fi(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?fo(Object(r),!0).forEach(function(t){var n,o,i;n=e,o=t,i=r[t],(o=function(e){var t=function(e,t){if("object"!=ft(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=ft(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==ft(t)?t:t+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):fo(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function fa(){return(fa=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}var fc=function(e){var t=e.value,r=e.formatter,n=e1()(e.children)?t:e.children;return e5()(r)?r(n):n},fs=function(e,t,r){var n,o,i=e.position,a=e.viewBox,c=e.offset,s=e.className,l=a.cx,f=a.cy,p=a.innerRadius,d=a.outerRadius,h=a.startAngle,y=a.endAngle,m=a.clockWise,v=(p+d)/2,b=eU(y-h)*Math.min(Math.abs(y-h),360),g=b>=0?1:-1;"insideStart"===i?(n=h+g*c,o=m):"insideEnd"===i?(n=y-g*c,o=!m):"end"===i&&(n=y+g*c,o=m),o=b<=0?o:!o;var x=u6(l,f,v,n),w=u6(l,f,v,n+(o?1:-1)*359),j="M".concat(x.x,",").concat(x.y,"\n    A").concat(v,",").concat(v,",0,1,").concat(+!o,",\n    ").concat(w.x,",").concat(w.y),O=e1()(e.id)?eX("recharts-radial-line-"):e.id;return u().createElement("text",fa({},r,{dominantBaseline:"central",className:(0,eT.A)("recharts-radial-bar-label",s)}),u().createElement("defs",null,u().createElement("path",{id:O,d:j})),u().createElement("textPath",{xlinkHref:"#".concat(O)},t))},fl=function(e){var t=e.viewBox,r=e.offset,n=e.position,o=t.cx,i=t.cy,a=t.innerRadius,c=t.outerRadius,s=(t.startAngle+t.endAngle)/2;if("outside"===n){var l=u6(o,i,c+r,s),u=l.x;return{x:u,y:l.y,textAnchor:u>=o?"start":"end",verticalAnchor:"middle"}}if("center"===n)return{x:o,y:i,textAnchor:"middle",verticalAnchor:"middle"};if("centerTop"===n)return{x:o,y:i,textAnchor:"middle",verticalAnchor:"start"};if("centerBottom"===n)return{x:o,y:i,textAnchor:"middle",verticalAnchor:"end"};var f=u6(o,i,(a+c)/2,s);return{x:f.x,y:f.y,textAnchor:"middle",verticalAnchor:"middle"}},fu=function(e){var t=e.viewBox,r=e.parentViewBox,n=e.offset,o=e.position,i=t.x,a=t.y,c=t.width,s=t.height,l=s>=0?1:-1,u=l*n,f=l>0?"end":"start",p=l>0?"start":"end",d=c>=0?1:-1,h=d*n,y=d>0?"end":"start",m=d>0?"start":"end";if("top"===o)return fi(fi({},{x:i+c/2,y:a-l*n,textAnchor:"middle",verticalAnchor:f}),r?{height:Math.max(a-r.y,0),width:c}:{});if("bottom"===o)return fi(fi({},{x:i+c/2,y:a+s+u,textAnchor:"middle",verticalAnchor:p}),r?{height:Math.max(r.y+r.height-(a+s),0),width:c}:{});if("left"===o){var v={x:i-h,y:a+s/2,textAnchor:y,verticalAnchor:"middle"};return fi(fi({},v),r?{width:Math.max(v.x-r.x,0),height:s}:{})}if("right"===o){var b={x:i+c+h,y:a+s/2,textAnchor:m,verticalAnchor:"middle"};return fi(fi({},b),r?{width:Math.max(r.x+r.width-b.x,0),height:s}:{})}var g=r?{width:c,height:s}:{};return"insideLeft"===o?fi({x:i+h,y:a+s/2,textAnchor:m,verticalAnchor:"middle"},g):"insideRight"===o?fi({x:i+c-h,y:a+s/2,textAnchor:y,verticalAnchor:"middle"},g):"insideTop"===o?fi({x:i+c/2,y:a+u,textAnchor:"middle",verticalAnchor:p},g):"insideBottom"===o?fi({x:i+c/2,y:a+s-u,textAnchor:"middle",verticalAnchor:f},g):"insideTopLeft"===o?fi({x:i+h,y:a+u,textAnchor:m,verticalAnchor:p},g):"insideTopRight"===o?fi({x:i+c-h,y:a+u,textAnchor:y,verticalAnchor:p},g):"insideBottomLeft"===o?fi({x:i+h,y:a+s-u,textAnchor:m,verticalAnchor:f},g):"insideBottomRight"===o?fi({x:i+c-h,y:a+s-u,textAnchor:y,verticalAnchor:f},g):e3()(o)&&(eq(o.x)||e$(o.x))&&(eq(o.y)||e$(o.y))?fi({x:i+eV(o.x,c),y:a+eV(o.y,s),textAnchor:"end",verticalAnchor:"end"},g):fi({x:i+c/2,y:a+s/2,textAnchor:"middle",verticalAnchor:"middle"},g)};function ff(e){var t,r=e.offset,n=fi({offset:void 0===r?5:r},function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,fr)),o=n.viewBox,i=n.position,a=n.value,c=n.children,s=n.content,f=n.className,p=n.textBreakAll;if(!o||e1()(a)&&e1()(c)&&!(0,l.isValidElement)(s)&&!e5()(s))return null;if((0,l.isValidElement)(s))return(0,l.cloneElement)(s,n);if(e5()(s)){if(t=(0,l.createElement)(s,n),(0,l.isValidElement)(t))return t}else t=fc(n);var d="cx"in o&&eq(o.cx),h=tg(n,!0);if(d&&("insideStart"===i||"insideEnd"===i||"end"===i))return fs(n,t,h);var y=d?fl(n):fu(n);return u().createElement(iu,fa({className:(0,eT.A)("recharts-label",void 0===f?"":f)},h,y,{breakAll:p}),t)}ff.displayName="Label";var fp=function(e){var t=e.cx,r=e.cy,n=e.angle,o=e.startAngle,i=e.endAngle,a=e.r,c=e.radius,s=e.innerRadius,l=e.outerRadius,u=e.x,f=e.y,p=e.top,d=e.left,h=e.width,y=e.height,m=e.clockWise,v=e.labelViewBox;if(v)return v;if(eq(h)&&eq(y)){if(eq(u)&&eq(f))return{x:u,y:f,width:h,height:y};if(eq(p)&&eq(d))return{x:p,y:d,width:h,height:y}}return eq(u)&&eq(f)?{x:u,y:f,width:0,height:0}:eq(t)&&eq(r)?{cx:t,cy:r,startAngle:o||n||0,endAngle:i||n||0,innerRadius:s||0,outerRadius:l||c||a||0,clockWise:m}:e.viewBox?e.viewBox:{}};ff.parseViewBox=fp,ff.renderCallByParent=function(e,t){var r,n,o=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!e||!e.children&&o&&!e.label)return null;var i=e.children,a=fp(e),c=th(i,ff).map(function(e,r){return(0,l.cloneElement)(e,{viewBox:t||a,key:"label-".concat(r)})});if(!o)return c;return[(r=e.label,n=t||a,!r?null:!0===r?u().createElement(ff,{key:"label-implicit",viewBox:n}):eW(r)?u().createElement(ff,{key:"label-implicit",viewBox:n,value:r}):(0,l.isValidElement)(r)?r.type===ff?(0,l.cloneElement)(r,{key:"label-implicit",viewBox:n}):u().createElement(ff,{key:"label-implicit",content:r,viewBox:n}):e5()(r)?u().createElement(ff,{key:"label-implicit",content:r,viewBox:n}):e3()(r)?u().createElement(ff,fa({viewBox:n},r,{key:"label-implicit"})):null)].concat(function(e){if(Array.isArray(e))return fn(e)}(c)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(c)||function(e,t){if(e){if("string"==typeof e)return fn(e,void 0);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return fn(e,t)}}(c)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}())};var fd=function(e,t){var r=e.alwaysShow,n=e.ifOverflow;return r&&(n="extendDomain"),n===t},fh=r(69691),fy=r.n(fh),fm=r(47212),fv=r.n(fm),fb=function(e){return null};fb.displayName="Cell";var fg=r(5359),fx=r.n(fg);function fw(e){return(fw="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var fj=["valueAccessor"],fO=["data","dataKey","clockWise","id","textBreakAll"];function fS(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function fP(){return(fP=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function fA(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function fk(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?fA(Object(r),!0).forEach(function(t){var n,o,i;n=e,o=t,i=r[t],(o=function(e){var t=function(e,t){if("object"!=fw(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=fw(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==fw(t)?t:t+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):fA(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function fE(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}var fN=function(e){return Array.isArray(e.value)?fx()(e.value):e.value};function fT(e){var t=e.valueAccessor,r=void 0===t?fN:t,n=fE(e,fj),o=n.data,i=n.dataKey,a=n.clockWise,c=n.id,s=n.textBreakAll,l=fE(n,fO);return o&&o.length?u().createElement(ns,{className:"recharts-label-list"},o.map(function(e,t){var n=e1()(i)?r(e,t):un(e&&e.payload,i),o=e1()(c)?{}:{id:"".concat(c,"-").concat(t)};return u().createElement(ff,fP({},tg(e,!0),l,o,{parentViewBox:e.parentViewBox,value:n,textBreakAll:s,viewBox:ff.parseViewBox(e1()(a)?e:fk(fk({},e),{},{clockWise:a})),key:"label-".concat(t),index:t}))})):null}fT.displayName="LabelList",fT.renderCallByParent=function(e,t){var r,n=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!e||!e.children&&n&&!e.label)return null;var o=th(e.children,fT).map(function(e,r){return(0,l.cloneElement)(e,{data:t,key:"labelList-".concat(r)})});return n?[(r=e.label,!r?null:!0===r?u().createElement(fT,{key:"labelList-implicit",data:t}):u().isValidElement(r)||e5()(r)?u().createElement(fT,{key:"labelList-implicit",data:t,content:r}):e3()(r)?u().createElement(fT,fP({data:t},r,{key:"labelList-implicit"})):null)].concat(function(e){if(Array.isArray(e))return fS(e)}(o)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(o)||function(e,t){if(e){if("string"==typeof e)return fS(e,void 0);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return fS(e,t)}}(o)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()):o};var fM=r(38404),f_=r.n(fM),fC=r(98451),fD=r.n(fC);function fI(e){return(fI="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function fB(){return(fB=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function fR(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function fL(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function fz(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?fL(Object(r),!0).forEach(function(t){var n,o,i;n=e,o=t,i=r[t],(o=function(e){var t=function(e,t){if("object"!=fI(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=fI(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==fI(t)?t:t+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):fL(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var fF=function(e,t,r,n,o){var i,a=r-n;return"M ".concat(e,",").concat(t)+"L ".concat(e+r,",").concat(t)+"L ".concat(e+r-a/2,",").concat(t+o)+"L ".concat(e+r-a/2-n,",").concat(t+o)+"L ".concat(e,",").concat(t," Z")},fU={x:0,y:0,upperWidth:0,lowerWidth:0,height:0,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},f$=function(e){var t,r=fz(fz({},fU),e),n=(0,l.useRef)(),o=function(e){if(Array.isArray(e))return e}(t=(0,l.useState)(-1))||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,c=[],s=!0,l=!1;try{i=(r=r.call(e)).next,!1;for(;!(s=(n=i.call(r)).done)&&(c.push(n.value),c.length!==t);s=!0);}catch(e){l=!0,o=e}finally{try{if(!s&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(t,2)||function(e,t){if(e){if("string"==typeof e)return fR(e,2);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return fR(e,t)}}(t,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),i=o[0],a=o[1];(0,l.useEffect)(function(){if(n.current&&n.current.getTotalLength)try{var e=n.current.getTotalLength();e&&a(e)}catch(e){}},[]);var c=r.x,s=r.y,f=r.upperWidth,p=r.lowerWidth,d=r.height,h=r.className,y=r.animationEasing,m=r.animationDuration,v=r.animationBegin,b=r.isUpdateAnimationActive;if(c!==+c||s!==+s||f!==+f||p!==+p||d!==+d||0===f&&0===p||0===d)return null;var g=(0,eT.A)("recharts-trapezoid",h);return b?u().createElement(ox,{canBegin:i>0,from:{upperWidth:0,lowerWidth:0,height:d,x:c,y:s},to:{upperWidth:f,lowerWidth:p,height:d,x:c,y:s},duration:m,animationEasing:y,isActive:b},function(e){var t=e.upperWidth,o=e.lowerWidth,a=e.height,c=e.x,s=e.y;return u().createElement(ox,{canBegin:i>0,from:"0px ".concat(-1===i?1:i,"px"),to:"".concat(i,"px 0px"),attributeName:"strokeDasharray",begin:v,duration:m,easing:y},u().createElement("path",fB({},tg(r,!0),{className:g,d:fF(c,s,t,o,a),ref:n})))}):u().createElement("g",null,u().createElement("path",fB({},tg(r,!0),{className:g,d:fF(c,s,f,p,d)})))};function fq(e){return(fq="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function fW(){return(fW=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function fH(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function fX(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?fH(Object(r),!0).forEach(function(t){var n,o,i;n=e,o=t,i=r[t],(o=function(e){var t=function(e,t){if("object"!=fq(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=fq(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==fq(t)?t:t+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):fH(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var fV=function(e){var t=e.cx,r=e.cy,n=e.radius,o=e.angle,i=e.sign,a=e.isExternal,c=e.cornerRadius,s=e.cornerIsExternal,l=c*(a?1:-1)+n,u=Math.asin(c/l)/u3,f=s?o:o+i*u;return{center:u6(t,r,l,f),circleTangency:u6(t,r,n,f),lineTangency:u6(t,r,l*Math.cos(u*u3),s?o-i*u:o),theta:u}},fG=function(e){var t=e.cx,r=e.cy,n=e.innerRadius,o=e.outerRadius,i=e.startAngle,a=e.endAngle,c=eU(a-i)*Math.min(Math.abs(a-i),359.999),s=i+c,l=u6(t,r,o,i),u=u6(t,r,o,s),f="M ".concat(l.x,",").concat(l.y,"\n    A ").concat(o,",").concat(o,",0,\n    ").concat(+(Math.abs(c)>180),",").concat(+(i>s),",\n    ").concat(u.x,",").concat(u.y,"\n  ");if(n>0){var p=u6(t,r,n,i),d=u6(t,r,n,s);f+="L ".concat(d.x,",").concat(d.y,"\n            A ").concat(n,",").concat(n,",0,\n            ").concat(+(Math.abs(c)>180),",").concat(+(i<=s),",\n            ").concat(p.x,",").concat(p.y," Z")}else f+="L ".concat(t,",").concat(r," Z");return f},fK=function(e){var t=e.cx,r=e.cy,n=e.innerRadius,o=e.outerRadius,i=e.cornerRadius,a=e.forceCornerRadius,c=e.cornerIsExternal,s=e.startAngle,l=e.endAngle,u=eU(l-s),f=fV({cx:t,cy:r,radius:o,angle:s,sign:u,cornerRadius:i,cornerIsExternal:c}),p=f.circleTangency,d=f.lineTangency,h=f.theta,y=fV({cx:t,cy:r,radius:o,angle:l,sign:-u,cornerRadius:i,cornerIsExternal:c}),m=y.circleTangency,v=y.lineTangency,b=y.theta,g=c?Math.abs(s-l):Math.abs(s-l)-h-b;if(g<0)return a?"M ".concat(d.x,",").concat(d.y,"\n        a").concat(i,",").concat(i,",0,0,1,").concat(2*i,",0\n        a").concat(i,",").concat(i,",0,0,1,").concat(-(2*i),",0\n      "):fG({cx:t,cy:r,innerRadius:n,outerRadius:o,startAngle:s,endAngle:l});var x="M ".concat(d.x,",").concat(d.y,"\n    A").concat(i,",").concat(i,",0,0,").concat(+(u<0),",").concat(p.x,",").concat(p.y,"\n    A").concat(o,",").concat(o,",0,").concat(+(g>180),",").concat(+(u<0),",").concat(m.x,",").concat(m.y,"\n    A").concat(i,",").concat(i,",0,0,").concat(+(u<0),",").concat(v.x,",").concat(v.y,"\n  ");if(n>0){var w=fV({cx:t,cy:r,radius:n,angle:s,sign:u,isExternal:!0,cornerRadius:i,cornerIsExternal:c}),j=w.circleTangency,O=w.lineTangency,S=w.theta,P=fV({cx:t,cy:r,radius:n,angle:l,sign:-u,isExternal:!0,cornerRadius:i,cornerIsExternal:c}),A=P.circleTangency,k=P.lineTangency,E=P.theta,N=c?Math.abs(s-l):Math.abs(s-l)-S-E;if(N<0&&0===i)return"".concat(x,"L").concat(t,",").concat(r,"Z");x+="L".concat(k.x,",").concat(k.y,"\n      A").concat(i,",").concat(i,",0,0,").concat(+(u<0),",").concat(A.x,",").concat(A.y,"\n      A").concat(n,",").concat(n,",0,").concat(+(N>180),",").concat(+(u>0),",").concat(j.x,",").concat(j.y,"\n      A").concat(i,",").concat(i,",0,0,").concat(+(u<0),",").concat(O.x,",").concat(O.y,"Z")}else x+="L".concat(t,",").concat(r,"Z");return x},fY={cx:0,cy:0,innerRadius:0,outerRadius:0,startAngle:0,endAngle:0,cornerRadius:0,forceCornerRadius:!1,cornerIsExternal:!1},fZ=function(e){var t,r=fX(fX({},fY),e),n=r.cx,o=r.cy,i=r.innerRadius,a=r.outerRadius,c=r.cornerRadius,s=r.forceCornerRadius,l=r.cornerIsExternal,f=r.startAngle,p=r.endAngle,d=r.className;if(a<i||f===p)return null;var h=(0,eT.A)("recharts-sector",d),y=a-i,m=eV(c,y,0,!0);return t=m>0&&360>Math.abs(f-p)?fK({cx:n,cy:o,innerRadius:i,outerRadius:a,cornerRadius:Math.min(m,y/2),forceCornerRadius:s,cornerIsExternal:l,startAngle:f,endAngle:p}):fG({cx:n,cy:o,innerRadius:i,outerRadius:a,startAngle:f,endAngle:p}),u().createElement("path",fW({},tg(r,!0),{className:h,d:t,role:"img"}))},fJ=["option","shapeType","propTransformer","activeClassName","isActive"];function fQ(e){return(fQ="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function f0(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function f1(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?f0(Object(r),!0).forEach(function(t){var n,o,i;n=e,o=t,i=r[t],(o=function(e){var t=function(e,t){if("object"!=fQ(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=fQ(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==fQ(t)?t:t+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):f0(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function f2(e){var t=e.shapeType,r=e.elementProps;switch(t){case"rectangle":return u().createElement(oN,r);case"trapezoid":return u().createElement(f$,r);case"sector":return u().createElement(fZ,r);case"symbols":if("symbols"===t)return u().createElement(rR,r);break;default:return null}}function f5(e){var t,r=e.option,n=e.shapeType,o=e.propTransformer,i=e.activeClassName,a=e.isActive,c=function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,fJ);if((0,l.isValidElement)(r))t=(0,l.cloneElement)(r,f1(f1({},c),(0,l.isValidElement)(r)?r.props:r));else if(e5()(r))t=r(c);else if(f_()(r)&&!fD()(r)){var s=(void 0===o?function(e,t){return f1(f1({},t),e)}:o)(r,c);t=u().createElement(f2,{shapeType:n,elementProps:s})}else t=u().createElement(f2,{shapeType:n,elementProps:c});return a?u().createElement(ns,{className:void 0===i?"recharts-active-shape":i},t):t}function f4(e,t){return null!=t&&"trapezoids"in e.props}function f3(e,t){return null!=t&&"sectors"in e.props}function f6(e,t){return null!=t&&"points"in e.props}function f8(e,t){var r,n,o=e.x===(null==t||null==(r=t.labelViewBox)?void 0:r.x)||e.x===t.x,i=e.y===(null==t||null==(n=t.labelViewBox)?void 0:n.y)||e.y===t.y;return o&&i}function f7(e,t){var r=e.endAngle===t.endAngle,n=e.startAngle===t.startAngle;return r&&n}function f9(e,t){var r=e.x===t.x,n=e.y===t.y,o=e.z===t.z;return r&&n&&o}var pe=["x","y"];function pt(e){return(pt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function pr(){return(pr=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function pn(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function po(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?pn(Object(r),!0).forEach(function(t){var n,o,i;n=e,o=t,i=r[t],(o=function(e){var t=function(e,t){if("object"!=pt(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=pt(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==pt(t)?t:t+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):pn(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function pi(e,t){var r=e.x,n=e.y,o=function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,pe),i=parseInt("".concat(r),10),a=parseInt("".concat(n),10),c=parseInt("".concat(t.height||o.height),10),s=parseInt("".concat(t.width||o.width),10);return po(po(po(po(po({},t),o),i?{x:i}:{}),a?{y:a}:{}),{},{height:c,width:s,name:t.name,radius:t.radius})}function pa(e){return u().createElement(f5,pr({shapeType:"rectangle",propTransformer:pi,activeClassName:"recharts-active-bar"},e))}var pc=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return function(r,n){if("number"==typeof e)return e;var o="number"==typeof r;return o?e(r,n):(o||ni(!1),t)}},ps=["value","background"];function pl(e){return(pl="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function pu(){return(pu=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function pf(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function pp(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?pf(Object(r),!0).forEach(function(t){pv(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):pf(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function pd(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,pb(n.key),n)}}function ph(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(ph=function(){return!!e})()}function py(e){return(py=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function pm(e,t){return(pm=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function pv(e,t,r){return(t=pb(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function pb(e){var t=function(e,t){if("object"!=pl(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=pl(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==pl(t)?t:t+""}var pg=function(e){var t,r;function n(){var e,t,r;if(!(this instanceof n))throw TypeError("Cannot call a class as a function");for(var o=arguments.length,i=Array(o),a=0;a<o;a++)i[a]=arguments[a];return t=n,r=[].concat(i),t=py(t),pv(e=function(e,t){if(t&&("object"===pl(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");var r=e;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,ph()?Reflect.construct(t,r||[],py(this).constructor):t.apply(this,r)),"state",{isAnimationFinished:!1}),pv(e,"id",eX("recharts-bar-")),pv(e,"handleAnimationEnd",function(){var t=e.props.onAnimationEnd;e.setState({isAnimationFinished:!0}),t&&t()}),pv(e,"handleAnimationStart",function(){var t=e.props.onAnimationStart;e.setState({isAnimationFinished:!1}),t&&t()}),e}if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");return n.prototype=Object.create(e&&e.prototype,{constructor:{value:n,writable:!0,configurable:!0}}),Object.defineProperty(n,"prototype",{writable:!1}),e&&pm(n,e),t=[{key:"renderRectanglesStatically",value:function(e){var t=this,r=this.props,n=r.shape,o=r.dataKey,i=r.activeIndex,a=r.activeBar,c=tg(this.props,!1);return e&&e.map(function(e,r){var s=r===i,l=pp(pp(pp({},c),e),{},{isActive:s,option:s?a:n,index:r,dataKey:o,onAnimationStart:t.handleAnimationStart,onAnimationEnd:t.handleAnimationEnd});return u().createElement(ns,pu({className:"recharts-bar-rectangle"},to(t.props,e,r),{key:"rectangle-".concat(null==e?void 0:e.x,"-").concat(null==e?void 0:e.y,"-").concat(null==e?void 0:e.value,"-").concat(r)}),u().createElement(pa,l))})}},{key:"renderRectanglesWithAnimation",value:function(){var e=this,t=this.props,r=t.data,n=t.layout,o=t.isAnimationActive,i=t.animationBegin,a=t.animationDuration,c=t.animationEasing,s=t.animationId,l=this.state.prevData;return u().createElement(ox,{begin:i,duration:a,isActive:o,easing:c,from:{t:0},to:{t:1},key:"bar-".concat(s),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},function(t){var o=t.t,i=r.map(function(e,t){var r=l&&l[t];if(r){var i=eY(r.x,e.x),a=eY(r.y,e.y),c=eY(r.width,e.width),s=eY(r.height,e.height);return pp(pp({},e),{},{x:i(o),y:a(o),width:c(o),height:s(o)})}if("horizontal"===n){var u=eY(0,e.height)(o);return pp(pp({},e),{},{y:e.y+e.height-u,height:u})}var f=eY(0,e.width)(o);return pp(pp({},e),{},{width:f})});return u().createElement(ns,null,e.renderRectanglesStatically(i))})}},{key:"renderRectangles",value:function(){var e=this.props,t=e.data,r=e.isAnimationActive,n=this.state.prevData;return r&&t&&t.length&&(!n||!le()(n,t))?this.renderRectanglesWithAnimation():this.renderRectanglesStatically(t)}},{key:"renderBackground",value:function(){var e=this,t=this.props,r=t.data,n=t.dataKey,o=t.activeIndex,i=tg(this.props.background,!1);return r.map(function(t,r){t.value;var a=t.background,c=function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(t,ps);if(!a)return null;var s=pp(pp(pp(pp(pp({},c),{},{fill:"#eee"},a),i),to(e.props,t,r)),{},{onAnimationStart:e.handleAnimationStart,onAnimationEnd:e.handleAnimationEnd,dataKey:n,index:r,className:"recharts-bar-background-rectangle"});return u().createElement(pa,pu({key:"background-bar-".concat(r),option:e.props.background,isActive:r===o},s))})}},{key:"renderErrorBar",value:function(e,t){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var r=this.props,n=r.data,o=r.xAxis,i=r.yAxis,a=r.layout,c=th(r.children,l2);if(!c)return null;var s="vertical"===a?n[0].height/2:n[0].width/2,l=function(e,t){var r=Array.isArray(e.value)?e.value[1]:e.value;return{x:e.x,y:e.y,value:r,errorVal:un(e,t)}};return u().createElement(ns,{clipPath:e?"url(#clipPath-".concat(t,")"):null},c.map(function(e){return u().cloneElement(e,{key:"error-bar-".concat(t,"-").concat(e.props.dataKey),data:n,xAxis:o,yAxis:i,layout:a,offset:s,dataPointFormatter:l})}))}},{key:"render",value:function(){var e=this.props,t=e.hide,r=e.data,n=e.className,o=e.xAxis,i=e.yAxis,a=e.left,c=e.top,s=e.width,l=e.height,f=e.isAnimationActive,p=e.background,d=e.id;if(t||!r||!r.length)return null;var h=this.state.isAnimationFinished,y=(0,eT.A)("recharts-bar",n),m=o&&o.allowDataOverflow,v=i&&i.allowDataOverflow,b=m||v,g=e1()(d)?this.id:d;return u().createElement(ns,{className:y},m||v?u().createElement("defs",null,u().createElement("clipPath",{id:"clipPath-".concat(g)},u().createElement("rect",{x:m?a:a-s/2,y:v?c:c-l/2,width:m?s:2*s,height:v?l:2*l}))):null,u().createElement(ns,{className:"recharts-bar-rectangles",clipPath:b?"url(#clipPath-".concat(g,")"):null},p?this.renderBackground():null,this.renderRectangles()),this.renderErrorBar(b,g),(!f||h)&&fT.renderCallByParent(this.props,r))}}],r=[{key:"getDerivedStateFromProps",value:function(e,t){return e.animationId!==t.prevAnimationId?{prevAnimationId:e.animationId,curData:e.data,prevData:t.curData}:e.data!==t.curData?{curData:e.data}:null}}],t&&pd(n.prototype,t),r&&pd(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(l.PureComponent);function px(e){return(px="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function pw(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,pP(n.key),n)}}function pj(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function pO(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?pj(Object(r),!0).forEach(function(t){pS(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):pj(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function pS(e,t,r){return(t=pP(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function pP(e){var t=function(e,t){if("object"!=px(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=px(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==px(t)?t:t+""}pv(pg,"displayName","Bar"),pv(pg,"defaultProps",{xAxisId:0,yAxisId:0,legendType:"rect",minPointSize:0,hide:!1,data:[],layout:"vertical",activeBar:!1,isAnimationActive:!tJ.isSsr,animationBegin:0,animationDuration:400,animationEasing:"ease"}),pv(pg,"getComposedData",function(e){var t=e.props,r=e.item,n=e.barPosition,o=e.bandSize,i=e.xAxis,a=e.yAxis,c=e.xAxisTicks,s=e.yAxisTicks,l=e.stackedData,u=e.dataStartIndex,f=e.displayedData,p=e.offset,d=ux(n,r);if(!d)return null;var h=t.layout,y=r.type.defaultProps,m=void 0!==y?pp(pp({},y),r.props):r.props,v=m.dataKey,b=m.children,g=m.minPointSize,x="horizontal"===h?a:i,w=l?x.scale.domain():null,j=uE({numericAxis:x}),O=th(b,fb),S=f.map(function(e,t){l?f=uw(l[u+t],w):Array.isArray(f=un(e,v))||(f=[j,f]);var n=pc(g,pg.defaultProps.minPointSize)(f[1],t);if("horizontal"===h){var f,p,y,m,b,x,S,P=[a.scale(f[0]),a.scale(f[1])],A=P[0],k=P[1];p=uk({axis:i,ticks:c,bandSize:o,offset:d.offset,entry:e,index:t}),y=null!=(S=null!=k?k:A)?S:void 0,m=d.size;var E=A-k;if(b=Number.isNaN(E)?0:E,x={x:p,y:a.y,width:m,height:a.height},Math.abs(n)>0&&Math.abs(b)<Math.abs(n)){var N=eU(b||n)*(Math.abs(n)-Math.abs(b));y-=N,b+=N}}else{var T=[i.scale(f[0]),i.scale(f[1])],M=T[0],_=T[1];if(p=M,y=uk({axis:a,ticks:s,bandSize:o,offset:d.offset,entry:e,index:t}),m=_-M,b=d.size,x={x:i.x,y:y,width:i.width,height:b},Math.abs(n)>0&&Math.abs(m)<Math.abs(n)){var C=eU(m||n)*(Math.abs(n)-Math.abs(m));m+=C}}return pp(pp(pp({},e),{},{x:p,y:y,width:m,height:b,value:l?f:f[1],payload:e,background:x},O&&O[t]&&O[t].props),{},{tooltipPayload:[uB(r,e)],tooltipPosition:{x:p+m/2,y:y+b/2}})});return pp({data:S,layout:h},p)});var pA=function(e,t){var r=e.x,n=e.y,o=t.x,i=t.y;return{x:Math.min(r,o),y:Math.min(n,i),width:Math.abs(o-r),height:Math.abs(i-n)}},pk=function(){var e,t;function r(e){if(!(this instanceof r))throw TypeError("Cannot call a class as a function");this.scale=e}return e=[{key:"domain",get:function(){return this.scale.domain}},{key:"range",get:function(){return this.scale.range}},{key:"rangeMin",get:function(){return this.range()[0]}},{key:"rangeMax",get:function(){return this.range()[1]}},{key:"bandwidth",get:function(){return this.scale.bandwidth}},{key:"apply",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=t.bandAware,n=t.position;if(void 0!==e){if(n)switch(n){case"start":default:return this.scale(e);case"middle":var o=this.bandwidth?this.bandwidth()/2:0;return this.scale(e)+o;case"end":var i=this.bandwidth?this.bandwidth():0;return this.scale(e)+i}if(r){var a=this.bandwidth?this.bandwidth()/2:0;return this.scale(e)+a}return this.scale(e)}}},{key:"isInRange",value:function(e){var t=this.range(),r=t[0],n=t[t.length-1];return r<=n?e>=r&&e<=n:e>=n&&e<=r}}],t=[{key:"create",value:function(e){return new r(e)}}],e&&pw(r.prototype,e),t&&pw(r,t),Object.defineProperty(r,"prototype",{writable:!1}),r}();pS(pk,"EPS",1e-4);var pE=function(e){var t=Object.keys(e).reduce(function(t,r){return pO(pO({},t),{},pS({},r,pk.create(e[r])))},{});return pO(pO({},t),{},{apply:function(e){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=r.bandAware,o=r.position;return fy()(e,function(e,r){return t[r].apply(e,{bandAware:n,position:o})})},isInRange:function(e){return fv()(e,function(e,r){return t[r].isInRange(e)})}})},pN=function(e){var t=e.width,r=e.height,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,o=(n%180+180)%180*Math.PI/180,i=Math.atan(r/t);return Math.abs(o>i&&o<Math.PI-i?r/Math.sin(o):t/Math.cos(o))};function pT(){return(pT=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function pM(e){return(pM="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function p_(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function pC(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?p_(Object(r),!0).forEach(function(t){pR(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):p_(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function pD(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(pD=function(){return!!e})()}function pI(e){return(pI=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function pB(e,t){return(pB=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function pR(e,t,r){return(t=pL(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function pL(e){var t=function(e,t){if("object"!=pM(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=pM(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==pM(t)?t:t+""}var pz=function(e){var t=e.x,r=e.y,n=e.xAxis,o=e.yAxis,i=pE({x:n.scale,y:o.scale}),a=i.apply({x:t,y:r},{bandAware:!0});return fd(e,"discard")&&!i.isInRange(a)?null:a},pF=function(e){var t;function r(){var e,t;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return e=r,t=arguments,e=pI(e),function(e,t){if(t&&("object"===pM(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");var r=e;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,pD()?Reflect.construct(e,t||[],pI(this).constructor):e.apply(this,t))}if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(e&&e.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),e&&pB(r,e),t=[{key:"render",value:function(){var e=this.props,t=e.x,n=e.y,o=e.r,i=e.alwaysShow,a=e.clipPathId,c=eW(t),s=eW(n);if(eQ(void 0===i,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.'),!c||!s)return null;var l=pz(this.props);if(!l)return null;var f=l.x,p=l.y,d=this.props,h=d.shape,y=d.className,m=pC(pC({clipPath:fd(this.props,"hidden")?"url(#".concat(a,")"):void 0},tg(this.props,!0)),{},{cx:f,cy:p});return u().createElement(ns,{className:(0,eT.A)("recharts-reference-dot",y)},r.renderDot(h,m),ff.renderCallByParent(this.props,{x:f-o,y:p-o,width:2*o,height:2*o}))}}],function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,pL(n.key),n)}}(r.prototype,t),Object.defineProperty(r,"prototype",{writable:!1}),r}(u().Component);pR(pF,"displayName","ReferenceDot"),pR(pF,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#fff",stroke:"#ccc",fillOpacity:1,strokeWidth:1}),pR(pF,"renderDot",function(e,t){var r;return u().isValidElement(e)?u().cloneElement(e,t):e5()(e)?e(t):u().createElement(nu,pT({},t,{cx:t.cx,cy:t.cy,className:"recharts-reference-dot-dot"}))});var pU=r(67367),p$=r.n(pU),pq=r(22964),pW=r.n(pq),pH=r(86451),pX=r.n(pH)()(function(e){return{x:e.left,y:e.top,width:e.width,height:e.height}},function(e){return["l",e.left,"t",e.top,"w",e.width,"h",e.height].join("")}),pV=(0,l.createContext)(void 0),pG=(0,l.createContext)(void 0),pK=(0,l.createContext)(void 0),pY=(0,l.createContext)({}),pZ=(0,l.createContext)(void 0),pJ=(0,l.createContext)(0),pQ=(0,l.createContext)(0),p0=function(e){var t=e.state,r=t.xAxisMap,n=t.yAxisMap,o=t.offset,i=e.clipPathId,a=e.children,c=e.width,s=e.height,l=pX(o);return u().createElement(pV.Provider,{value:r},u().createElement(pG.Provider,{value:n},u().createElement(pY.Provider,{value:o},u().createElement(pK.Provider,{value:l},u().createElement(pZ.Provider,{value:i},u().createElement(pJ.Provider,{value:s},u().createElement(pQ.Provider,{value:c},a)))))))},p1=function(e){var t=(0,l.useContext)(pV);null==t&&ni(!1);var r=t[e];return null==r&&ni(!1),r},p2=function(){var e=(0,l.useContext)(pG);return pW()(e,function(e){return fv()(e.domain,Number.isFinite)})||eG(e)},p5=function(e){var t=(0,l.useContext)(pG);null==t&&ni(!1);var r=t[e];return null==r&&ni(!1),r},p4=function(){return(0,l.useContext)(pQ)},p3=function(){return(0,l.useContext)(pJ)};function p6(e){return(p6="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function p8(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(p8=function(){return!!e})()}function p7(e){return(p7=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function p9(e,t){return(p9=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function de(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function dt(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?de(Object(r),!0).forEach(function(t){dr(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):de(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function dr(e,t,r){return(t=dn(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function dn(e){var t=function(e,t){if("object"!=p6(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=p6(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==p6(t)?t:t+""}function di(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function da(){return(da=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}var dc=function(e,t){var r;return u().isValidElement(e)?u().cloneElement(e,t):e5()(e)?e(t):u().createElement("line",da({},t,{className:"recharts-reference-line-line"}))},ds=function(e,t,r,n,o,i,a,c,s){var l=o.x,u=o.y,f=o.width,p=o.height;if(r){var d=s.y,h=e.y.apply(d,{position:i});if(fd(s,"discard")&&!e.y.isInRange(h))return null;var y=[{x:l+f,y:h},{x:l,y:h}];return"left"===c?y.reverse():y}if(t){var m=s.x,v=e.x.apply(m,{position:i});if(fd(s,"discard")&&!e.x.isInRange(v))return null;var b=[{x:v,y:u+p},{x:v,y:u}];return"top"===a?b.reverse():b}if(n){var g=s.segment.map(function(t){return e.apply(t,{position:i})});return fd(s,"discard")&&p$()(g,function(t){return!e.isInRange(t)})?null:g}return null};function dl(e){var t,r=e.x,n=e.y,o=e.segment,i=e.xAxisId,a=e.yAxisId,c=e.shape,s=e.className,f=e.alwaysShow,p=(0,l.useContext)(pZ),d=p1(i),h=p5(a),y=(0,l.useContext)(pK);if(!p||!y)return null;eQ(void 0===f,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var m=ds(pE({x:d.scale,y:h.scale}),eW(r),eW(n),o&&2===o.length,y,e.position,d.orientation,h.orientation,e);if(!m)return null;var v=function(e){if(Array.isArray(e))return e}(m)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,c=[],s=!0,l=!1;try{i=(r=r.call(e)).next,!1;for(;!(s=(n=i.call(r)).done)&&(c.push(n.value),c.length!==t);s=!0);}catch(e){l=!0,o=e}finally{try{if(!s&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(m,2)||function(e,t){if(e){if("string"==typeof e)return di(e,2);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return di(e,t)}}(m,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),b=v[0],g=b.x,x=b.y,w=v[1],j=w.x,O=w.y,S=dt(dt({clipPath:fd(e,"hidden")?"url(#".concat(p,")"):void 0},tg(e,!0)),{},{x1:g,y1:x,x2:j,y2:O});return u().createElement(ns,{className:(0,eT.A)("recharts-reference-line",s)},dc(c,S),ff.renderCallByParent(e,pA({x:(t={x1:g,y1:x,x2:j,y2:O}).x1,y:t.y1},{x:t.x2,y:t.y2})))}var du=function(e){var t;function r(){var e,t;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return e=r,t=arguments,e=p7(e),function(e,t){if(t&&("object"===p6(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");var r=e;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,p8()?Reflect.construct(e,t||[],p7(this).constructor):e.apply(this,t))}if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(e&&e.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),e&&p9(r,e),t=[{key:"render",value:function(){return u().createElement(dl,this.props)}}],function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,dn(n.key),n)}}(r.prototype,t),Object.defineProperty(r,"prototype",{writable:!1}),r}(u().Component);function df(){return(df=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function dp(e){return(dp="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function dd(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function dh(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?dd(Object(r),!0).forEach(function(t){db(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):dd(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}dr(du,"displayName","ReferenceLine"),dr(du,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,fill:"none",stroke:"#ccc",fillOpacity:1,strokeWidth:1,position:"middle"});function dy(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(dy=function(){return!!e})()}function dm(e){return(dm=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function dv(e,t){return(dv=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function db(e,t,r){return(t=dg(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function dg(e){var t=function(e,t){if("object"!=dp(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=dp(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==dp(t)?t:t+""}var dx=function(e,t,r,n,o){var i=o.x1,a=o.x2,c=o.y1,s=o.y2,l=o.xAxis,u=o.yAxis;if(!l||!u)return null;var f=pE({x:l.scale,y:u.scale}),p={x:e?f.x.apply(i,{position:"start"}):f.x.rangeMin,y:r?f.y.apply(c,{position:"start"}):f.y.rangeMin},d={x:t?f.x.apply(a,{position:"end"}):f.x.rangeMax,y:n?f.y.apply(s,{position:"end"}):f.y.rangeMax};return!fd(o,"discard")||f.isInRange(p)&&f.isInRange(d)?pA(p,d):null},dw=function(e){var t;function r(){var e,t;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return e=r,t=arguments,e=dm(e),function(e,t){if(t&&("object"===dp(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");var r=e;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,dy()?Reflect.construct(e,t||[],dm(this).constructor):e.apply(this,t))}if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(e&&e.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),e&&dv(r,e),t=[{key:"render",value:function(){var e=this.props,t=e.x1,n=e.x2,o=e.y1,i=e.y2,a=e.className,c=e.alwaysShow,s=e.clipPathId;eQ(void 0===c,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var l=eW(t),f=eW(n),p=eW(o),d=eW(i),h=this.props.shape;if(!l&&!f&&!p&&!d&&!h)return null;var y=dx(l,f,p,d,this.props);if(!y&&!h)return null;var m=fd(this.props,"hidden")?"url(#".concat(s,")"):void 0;return u().createElement(ns,{className:(0,eT.A)("recharts-reference-area",a)},r.renderRect(h,dh(dh({clipPath:m},tg(this.props,!0)),y)),ff.renderCallByParent(this.props,y))}}],function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,dg(n.key),n)}}(r.prototype,t),Object.defineProperty(r,"prototype",{writable:!1}),r}(u().Component);function dj(e){return function(e){if(Array.isArray(e))return dO(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return dO(e,void 0);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return dO(e,t)}}(e)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function dO(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}db(dw,"displayName","ReferenceArea"),db(dw,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#ccc",fillOpacity:.5,stroke:"none",strokeWidth:1}),db(dw,"renderRect",function(e,t){var r;return u().isValidElement(e)?u().cloneElement(e,t):e5()(e)?e(t):u().createElement(oN,df({},t,{className:"recharts-reference-area-rect"}))});var dS=function(e,t,r,n,o){var i=th(e,du),a=th(e,pF),c=[].concat(dj(i),dj(a)),s=th(e,dw),l="".concat(n,"Id"),u=n[0],f=t;if(c.length&&(f=c.reduce(function(e,t){if(t.props[l]===r&&fd(t.props,"extendDomain")&&eq(t.props[u])){var n=t.props[u];return[Math.min(e[0],n),Math.max(e[1],n)]}return e},f)),s.length){var p="".concat(u,"1"),d="".concat(u,"2");f=s.reduce(function(e,t){if(t.props[l]===r&&fd(t.props,"extendDomain")&&eq(t.props[p])&&eq(t.props[d])){var n=t.props[p],o=t.props[d];return[Math.min(e[0],n,o),Math.max(e[1],n,o)]}return e},f)}return o&&o.length&&(f=o.reduce(function(e,t){return eq(t)?[Math.min(e[0],t),Math.max(e[1],t)]:e},f)),f},dP=r(11117),dA=new(r.n(dP)()),dk="recharts.syncMouseEvents";function dE(e){return(dE="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function dN(e,t,r){return(t=dT(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function dT(e){var t=function(e,t){if("object"!=dE(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=dE(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==dE(t)?t:t+""}var dM=function(){var e,t;return e=function e(){if(!(this instanceof e))throw TypeError("Cannot call a class as a function");dN(this,"activeIndex",0),dN(this,"coordinateList",[]),dN(this,"layout","horizontal")},t=[{key:"setDetails",value:function(e){var t,r=e.coordinateList,n=void 0===r?null:r,o=e.container,i=void 0===o?null:o,a=e.layout,c=void 0===a?null:a,s=e.offset,l=void 0===s?null:s,u=e.mouseHandlerCallback,f=void 0===u?null:u;this.coordinateList=null!=(t=null!=n?n:this.coordinateList)?t:[],this.container=null!=i?i:this.container,this.layout=null!=c?c:this.layout,this.offset=null!=l?l:this.offset,this.mouseHandlerCallback=null!=f?f:this.mouseHandlerCallback,this.activeIndex=Math.min(Math.max(this.activeIndex,0),this.coordinateList.length-1)}},{key:"focus",value:function(){this.spoofMouse()}},{key:"keyboardEvent",value:function(e){if(0!==this.coordinateList.length)switch(e.key){case"ArrowRight":if("horizontal"!==this.layout)return;this.activeIndex=Math.min(this.activeIndex+1,this.coordinateList.length-1),this.spoofMouse();break;case"ArrowLeft":if("horizontal"!==this.layout)return;this.activeIndex=Math.max(this.activeIndex-1,0),this.spoofMouse()}}},{key:"setIndex",value:function(e){this.activeIndex=e}},{key:"spoofMouse",value:function(){if("horizontal"===this.layout&&0!==this.coordinateList.length){var e,t,r=this.container.getBoundingClientRect(),n=r.x,o=r.y,i=r.height,a=this.coordinateList[this.activeIndex].coordinate,c=(null==(e=window)?void 0:e.scrollX)||0,s=(null==(t=window)?void 0:t.scrollY)||0,l=o+this.offset.top+i/2+s;this.mouseHandlerCallback({pageX:n+a+c,pageY:l})}}}],function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,dT(n.key),n)}}(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}();function d_(){}function dC(e,t,r){e._context.bezierCurveTo((2*e._x0+e._x1)/3,(2*e._y0+e._y1)/3,(e._x0+2*e._x1)/3,(e._y0+2*e._y1)/3,(e._x0+4*e._x1+t)/6,(e._y0+4*e._y1+r)/6)}function dD(e){this._context=e}function dI(e){this._context=e}function dB(e){this._context=e}dD.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){switch(this._point){case 3:dC(this,this._x1,this._y1);case 2:this._context.lineTo(this._x1,this._y1)}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e*=1,t*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;break;case 2:this._point=3,this._context.lineTo((5*this._x0+this._x1)/6,(5*this._y0+this._y1)/6);default:dC(this,e,t)}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}},dI.prototype={areaStart:d_,areaEnd:d_,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._y0=this._y1=this._y2=this._y3=this._y4=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:this._context.moveTo(this._x2,this._y2),this._context.closePath();break;case 2:this._context.moveTo((this._x2+2*this._x3)/3,(this._y2+2*this._y3)/3),this._context.lineTo((this._x3+2*this._x2)/3,(this._y3+2*this._y2)/3),this._context.closePath();break;case 3:this.point(this._x2,this._y2),this.point(this._x3,this._y3),this.point(this._x4,this._y4)}},point:function(e,t){switch(e*=1,t*=1,this._point){case 0:this._point=1,this._x2=e,this._y2=t;break;case 1:this._point=2,this._x3=e,this._y3=t;break;case 2:this._point=3,this._x4=e,this._y4=t,this._context.moveTo((this._x0+4*this._x1+e)/6,(this._y0+4*this._y1+t)/6);break;default:dC(this,e,t)}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}},dB.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){(this._line||0!==this._line&&3===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e*=1,t*=1,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3;var r=(this._x0+4*this._x1+e)/6,n=(this._y0+4*this._y1+t)/6;this._line?this._context.lineTo(r,n):this._context.moveTo(r,n);break;case 3:this._point=4;default:dC(this,e,t)}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}};class dR{constructor(e,t){this._context=e,this._x=t}areaStart(){this._line=0}areaEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line}point(e,t){switch(e*=1,t*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:this._x?this._context.bezierCurveTo(this._x0=(this._x0+e)/2,this._y0,this._x0,t,e,t):this._context.bezierCurveTo(this._x0,this._y0=(this._y0+t)/2,e,this._y0,e,t)}this._x0=e,this._y0=t}}function dL(e){this._context=e}function dz(e){this._context=e}function dF(e){return new dz(e)}dL.prototype={areaStart:d_,areaEnd:d_,lineStart:function(){this._point=0},lineEnd:function(){this._point&&this._context.closePath()},point:function(e,t){e*=1,t*=1,this._point?this._context.lineTo(e,t):(this._point=1,this._context.moveTo(e,t))}};function dU(e,t,r){var n=e._x1-e._x0,o=t-e._x1,i=(e._y1-e._y0)/(n||o<0&&-0),a=(r-e._y1)/(o||n<0&&-0);return((i<0?-1:1)+(a<0?-1:1))*Math.min(Math.abs(i),Math.abs(a),.5*Math.abs((i*o+a*n)/(n+o)))||0}function d$(e,t){var r=e._x1-e._x0;return r?(3*(e._y1-e._y0)/r-t)/2:t}function dq(e,t,r){var n=e._x0,o=e._y0,i=e._x1,a=e._y1,c=(i-n)/3;e._context.bezierCurveTo(n+c,o+c*t,i-c,a-c*r,i,a)}function dW(e){this._context=e}function dH(e){this._context=new dX(e)}function dX(e){this._context=e}function dV(e){this._context=e}function dG(e){var t,r,n=e.length-1,o=Array(n),i=Array(n),a=Array(n);for(o[0]=0,i[0]=2,a[0]=e[0]+2*e[1],t=1;t<n-1;++t)o[t]=1,i[t]=4,a[t]=4*e[t]+2*e[t+1];for(o[n-1]=2,i[n-1]=7,a[n-1]=8*e[n-1]+e[n],t=1;t<n;++t)r=o[t]/i[t-1],i[t]-=r,a[t]-=r*a[t-1];for(o[n-1]=a[n-1]/i[n-1],t=n-2;t>=0;--t)o[t]=(a[t]-o[t+1])/i[t];for(t=0,i[n-1]=(e[n]+o[n-1])/2;t<n-1;++t)i[t]=2*e[t+1]-o[t+1];return[o,i]}function dK(e,t){this._context=e,this._t=t}function dY(e){return e[0]}function dZ(e){return e[1]}function dJ(e,t){var r=rj(!0),n=null,o=dF,i=null,a=rE(c);function c(c){var s,l,u,f=(c=sW(c)).length,p=!1;for(null==n&&(i=o(u=a())),s=0;s<=f;++s)!(s<f&&r(l=c[s],s,c))===p&&((p=!p)?i.lineStart():i.lineEnd()),p&&i.point(+e(l,s,c),+t(l,s,c));if(u)return i=null,u+""||null}return e="function"==typeof e?e:void 0===e?dY:rj(e),t="function"==typeof t?t:void 0===t?dZ:rj(t),c.x=function(t){return arguments.length?(e="function"==typeof t?t:rj(+t),c):e},c.y=function(e){return arguments.length?(t="function"==typeof e?e:rj(+e),c):t},c.defined=function(e){return arguments.length?(r="function"==typeof e?e:rj(!!e),c):r},c.curve=function(e){return arguments.length?(o=e,null!=n&&(i=o(n)),c):o},c.context=function(e){return arguments.length?(null==e?n=i=null:i=o(n=e),c):n},c}function dQ(e,t,r){var n=null,o=rj(!0),i=null,a=dF,c=null,s=rE(l);function l(l){var u,f,p,d,h,y=(l=sW(l)).length,m=!1,v=Array(y),b=Array(y);for(null==i&&(c=a(h=s())),u=0;u<=y;++u){if(!(u<y&&o(d=l[u],u,l))===m)if(m=!m)f=u,c.areaStart(),c.lineStart();else{for(c.lineEnd(),c.lineStart(),p=u-1;p>=f;--p)c.point(v[p],b[p]);c.lineEnd(),c.areaEnd()}m&&(v[u]=+e(d,u,l),b[u]=+t(d,u,l),c.point(n?+n(d,u,l):v[u],r?+r(d,u,l):b[u]))}if(h)return c=null,h+""||null}function u(){return dJ().defined(o).curve(a).context(i)}return e="function"==typeof e?e:void 0===e?dY:rj(+e),t="function"==typeof t?t:void 0===t?rj(0):rj(+t),r="function"==typeof r?r:void 0===r?dZ:rj(+r),l.x=function(t){return arguments.length?(e="function"==typeof t?t:rj(+t),n=null,l):e},l.x0=function(t){return arguments.length?(e="function"==typeof t?t:rj(+t),l):e},l.x1=function(e){return arguments.length?(n=null==e?null:"function"==typeof e?e:rj(+e),l):n},l.y=function(e){return arguments.length?(t="function"==typeof e?e:rj(+e),r=null,l):t},l.y0=function(e){return arguments.length?(t="function"==typeof e?e:rj(+e),l):t},l.y1=function(e){return arguments.length?(r=null==e?null:"function"==typeof e?e:rj(+e),l):r},l.lineX0=l.lineY0=function(){return u().x(e).y(t)},l.lineY1=function(){return u().x(e).y(r)},l.lineX1=function(){return u().x(n).y(t)},l.defined=function(e){return arguments.length?(o="function"==typeof e?e:rj(!!e),l):o},l.curve=function(e){return arguments.length?(a=e,null!=i&&(c=a(i)),l):a},l.context=function(e){return arguments.length?(null==e?i=c=null:c=a(i=e),l):i},l}function d0(e){return(d0="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function d1(){return(d1=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function d2(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function d5(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?d2(Object(r),!0).forEach(function(t){var n,o,i;n=e,o=t,i=r[t],(o=function(e){var t=function(e,t){if("object"!=d0(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=d0(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==d0(t)?t:t+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):d2(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}dz.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e*=1,t*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:this._context.lineTo(e,t)}}},dW.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=this._t0=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x1,this._y1);break;case 3:dq(this,this._t0,d$(this,this._t0))}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){var r=NaN;if(t*=1,(e*=1)!==this._x1||t!==this._y1){switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;break;case 2:this._point=3,dq(this,d$(this,r=dU(this,e,t)),r);break;default:dq(this,this._t0,r=dU(this,e,t))}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t,this._t0=r}}},(dH.prototype=Object.create(dW.prototype)).point=function(e,t){dW.prototype.point.call(this,t,e)},dX.prototype={moveTo:function(e,t){this._context.moveTo(t,e)},closePath:function(){this._context.closePath()},lineTo:function(e,t){this._context.lineTo(t,e)},bezierCurveTo:function(e,t,r,n,o,i){this._context.bezierCurveTo(t,e,n,r,i,o)}},dV.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=[],this._y=[]},lineEnd:function(){var e=this._x,t=this._y,r=e.length;if(r)if(this._line?this._context.lineTo(e[0],t[0]):this._context.moveTo(e[0],t[0]),2===r)this._context.lineTo(e[1],t[1]);else for(var n=dG(e),o=dG(t),i=0,a=1;a<r;++i,++a)this._context.bezierCurveTo(n[0][i],o[0][i],n[1][i],o[1][i],e[a],t[a]);(this._line||0!==this._line&&1===r)&&this._context.closePath(),this._line=1-this._line,this._x=this._y=null},point:function(e,t){this._x.push(+e),this._y.push(+t)}},dK.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=this._y=NaN,this._point=0},lineEnd:function(){0<this._t&&this._t<1&&2===this._point&&this._context.lineTo(this._x,this._y),(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line>=0&&(this._t=1-this._t,this._line=1-this._line)},point:function(e,t){switch(e*=1,t*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:if(this._t<=0)this._context.lineTo(this._x,t),this._context.lineTo(e,t);else{var r=this._x*(1-this._t)+e*this._t;this._context.lineTo(r,this._y),this._context.lineTo(r,t)}}this._x=e,this._y=t}};var d4={curveBasisClosed:function(e){return new dI(e)},curveBasisOpen:function(e){return new dB(e)},curveBasis:function(e){return new dD(e)},curveBumpX:function(e){return new dR(e,!0)},curveBumpY:function(e){return new dR(e,!1)},curveLinearClosed:function(e){return new dL(e)},curveLinear:dF,curveMonotoneX:function(e){return new dW(e)},curveMonotoneY:function(e){return new dH(e)},curveNatural:function(e){return new dV(e)},curveStep:function(e){return new dK(e,.5)},curveStepAfter:function(e){return new dK(e,1)},curveStepBefore:function(e){return new dK(e,0)}},d3=function(e){return e.x===+e.x&&e.y===+e.y},d6=function(e){return e.x},d8=function(e){return e.y},d7=function(e,t){if(e5()(e))return e;var r="curve".concat(ra()(e));return("curveMonotone"===r||"curveBump"===r)&&t?d4["".concat(r).concat("vertical"===t?"Y":"X")]:d4[r]||dF},d9=function(e){var t,r=e.type,n=e.points,o=void 0===n?[]:n,i=e.baseLine,a=e.layout,c=e.connectNulls,s=void 0!==c&&c,l=d7(void 0===r?"linear":r,a),u=s?o.filter(function(e){return d3(e)}):o;if(Array.isArray(i)){var f=s?i.filter(function(e){return d3(e)}):i,p=u.map(function(e,t){return d5(d5({},e),{},{base:f[t]})});return(t="vertical"===a?dQ().y(d8).x1(d6).x0(function(e){return e.base.x}):dQ().x(d6).y1(d8).y0(function(e){return e.base.y})).defined(d3).curve(l),t(p)}return(t="vertical"===a&&eq(i)?dQ().y(d8).x1(d6).x0(i):eq(i)?dQ().x(d6).y1(d8).y0(i):dJ().x(d6).y(d8)).defined(d3).curve(l),t(u)},he=function(e){var t=e.className,r=e.points,n=e.path,o=e.pathRef;if((!r||!r.length)&&!n)return null;var i=r&&r.length?d9(e):n;return u().createElement("path",d1({},tg(e,!1),tn(e),{className:(0,eT.A)("recharts-curve",t),d:i,ref:o}))};function ht(e){return(ht="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var hr=["x","y","top","left","width","height","className"];function hn(){return(hn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function ho(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}var hi=function(e){var t=e.x,r=void 0===t?0:t,n=e.y,o=void 0===n?0:n,i=e.top,a=void 0===i?0:i,c=e.left,s=void 0===c?0:c,l=e.width,f=void 0===l?0:l,p=e.height,d=void 0===p?0:p,h=e.className,y=function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ho(Object(r),!0).forEach(function(t){var n,o,i;n=e,o=t,i=r[t],(o=function(e){var t=function(e,t){if("object"!=ht(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=ht(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==ht(t)?t:t+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ho(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}({x:r,y:o,top:a,left:s,width:f,height:d},function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,hr));return eq(r)&&eq(o)&&eq(f)&&eq(d)&&eq(a)&&eq(s)?u().createElement("path",hn({},tg(y,!0),{className:(0,eT.A)("recharts-cross",h),d:"M".concat(r,",").concat(a,"v").concat(d,"M").concat(s,",").concat(o,"h").concat(f)})):null};function ha(e){var t=e.cx,r=e.cy,n=e.radius,o=e.startAngle,i=e.endAngle;return{points:[u6(t,r,n,o),u6(t,r,n,i)],cx:t,cy:r,radius:n,startAngle:o,endAngle:i}}function hc(e){return(hc="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function hs(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function hl(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?hs(Object(r),!0).forEach(function(t){var n,o,i;n=e,o=t,i=r[t],(o=function(e){var t=function(e,t){if("object"!=hc(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=hc(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==hc(t)?t:t+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):hs(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function hu(e){var t,r,n,o,i=e.element,a=e.tooltipEventType,c=e.isActive,s=e.activeCoordinate,u=e.activePayload,f=e.offset,p=e.activeTooltipIndex,d=e.tooltipAxisBandSize,h=e.layout,y=e.chartName,m=null!=(r=i.props.cursor)?r:null==(n=i.type.defaultProps)?void 0:n.cursor;if(!i||!m||!c||!s||"ScatterChart"!==y&&"axis"!==a)return null;var v=he;if("ScatterChart"===y)o=s,v=hi;else if("BarChart"===y)t=d/2,o={stroke:"none",fill:"#ccc",x:"horizontal"===h?s.x-t:f.left+.5,y:"horizontal"===h?f.top+.5:s.y-t,width:"horizontal"===h?d:f.width-1,height:"horizontal"===h?f.height-1:d},v=oN;else if("radial"===h){var b=ha(s),g=b.cx,x=b.cy,w=b.radius;o={cx:g,cy:x,startAngle:b.startAngle,endAngle:b.endAngle,innerRadius:w,outerRadius:w},v=fZ}else o={points:function(e,t,r){var n,o,i,a;if("horizontal"===e)i=n=t.x,o=r.top,a=r.top+r.height;else if("vertical"===e)a=o=t.y,n=r.left,i=r.left+r.width;else if(null!=t.cx&&null!=t.cy)if("centric"!==e)return ha(t);else{var c=t.cx,s=t.cy,l=t.innerRadius,u=t.outerRadius,f=t.angle,p=u6(c,s,l,f),d=u6(c,s,u,f);n=p.x,o=p.y,i=d.x,a=d.y}return[{x:n,y:o},{x:i,y:a}]}(h,s,f)},v=he;var j=hl(hl(hl(hl({stroke:"#ccc",pointerEvents:"none"},f),o),tg(m,!1)),{},{payload:u,payloadIndex:p,className:(0,eT.A)("recharts-tooltip-cursor",m.className)});return(0,l.isValidElement)(m)?(0,l.cloneElement)(m,j):(0,l.createElement)(v,j)}var hf=["item"],hp=["children","className","width","height","style","compact","title","desc"];function hd(e){return(hd="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function hh(){return(hh=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function hy(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,c=[],s=!0,l=!1;try{if(i=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;s=!1}else for(;!(s=(n=i.call(r)).done)&&(c.push(n.value),c.length!==t);s=!0);}catch(e){l=!0,o=e}finally{try{if(!s&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(e,t)||hw(e,t)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function hm(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}function hv(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(hv=function(){return!!e})()}function hb(e){return(hb=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function hg(e,t){return(hg=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function hx(e){return function(e){if(Array.isArray(e))return hj(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||hw(e)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function hw(e,t){if(e){if("string"==typeof e)return hj(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return hj(e,t)}}function hj(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function hO(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function hS(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?hO(Object(r),!0).forEach(function(t){hP(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):hO(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function hP(e,t,r){return(t=hA(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function hA(e){var t=function(e,t){if("object"!=hd(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=hd(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==hd(t)?t:t+""}var hk={xAxis:["bottom","top"],yAxis:["left","right"]},hE={width:"100%",height:"100%"},hN={x:0,y:0};function hT(e){return e}var hM=function(e,t,r,n){var o=t.find(function(e){return e&&e.index===r});if(o){if("horizontal"===e)return{x:o.coordinate,y:n.y};if("vertical"===e)return{x:n.x,y:o.coordinate};if("centric"===e){var i=o.coordinate,a=n.radius;return hS(hS(hS({},n),u6(n.cx,n.cy,a,i)),{},{angle:i,radius:a})}var c=o.coordinate,s=n.angle;return hS(hS(hS({},n),u6(n.cx,n.cy,c,s)),{},{angle:s,radius:c})}return hN},h_=function(e,t){var r=t.graphicalItems,n=t.dataStartIndex,o=t.dataEndIndex,i=(null!=r?r:[]).reduce(function(e,t){var r=t.props.data;return r&&r.length?[].concat(hx(e),hx(r)):e},[]);return i.length>0?i:e&&e.length&&eq(n)&&eq(o)?e.slice(n,o+1):[]};function hC(e){return"number"===e?[0,"auto"]:void 0}var hD=function(e,t,r,n){var o=e.graphicalItems,i=e.tooltipAxis,a=h_(t,e);return r<0||!o||!o.length||r>=a.length?null:o.reduce(function(o,c){var s,l,u=null!=(s=c.props.data)?s:t;return(u&&e.dataStartIndex+e.dataEndIndex!==0&&e.dataEndIndex-e.dataStartIndex>=r&&(u=u.slice(e.dataStartIndex,e.dataEndIndex+1)),l=i.dataKey&&!i.allowDuplicatedCategory?eZ(void 0===u?a:u,i.dataKey,n):u&&u[r]||a[r])?[].concat(hx(o),[uB(c,l)]):o},[])},hI=function(e,t,r,n){var o=n||{x:e.chartX,y:e.chartY},i="horizontal"===r?o.x:"vertical"===r?o.y:"centric"===r?o.angle:o.radius,a=e.orderedTooltipTicks,c=e.tooltipAxis,s=e.tooltipTicks,l=ui(i,a,s,c);if(l>=0&&s){var u=s[l]&&s[l].value,f=hD(e,t,l,u),p=hM(r,a,l,o);return{activeTooltipIndex:l,activeLabel:u,activePayload:f,activeCoordinate:p}}return null},hB=function(e,t){var r=t.axes,n=t.graphicalItems,o=t.axisType,i=t.axisIdKey,a=t.stackGroups,c=t.dataStartIndex,s=t.dataEndIndex,l=e.layout,u=e.children,f=e.stackOffset,p=ud(l,o);return r.reduce(function(t,r){var d=void 0!==r.type.defaultProps?hS(hS({},r.type.defaultProps),r.props):r.props,h=d.type,y=d.dataKey,m=d.allowDataOverflow,v=d.allowDuplicatedCategory,b=d.scale,g=d.ticks,x=d.includeHidden,w=d[i];if(t[w])return t;var j=h_(e.data,{graphicalItems:n.filter(function(e){var t;return(i in e.props?e.props[i]:null==(t=e.type.defaultProps)?void 0:t[i])===w}),dataStartIndex:c,dataEndIndex:s}),O=j.length;(function(e,t,r){if("number"===r&&!0===t&&Array.isArray(e)){var n=null==e?void 0:e[0],o=null==e?void 0:e[1];if(n&&o&&eq(n)&&eq(o))return!0}return!1})(d.domain,m,h)&&(A=uC(d.domain,null,m),p&&("number"===h||"auto"!==b)&&(E=uo(j,y,"category")));var S=hC(h);if(!A||0===A.length){var P,A,k,E,N,T=null!=(N=d.domain)?N:S;if(y){if(A=uo(j,y,h),"category"===h&&p){var M=eK(A);v&&M?(k=A,A=no()(0,O)):v||(A=uI(T,A,r).reduce(function(e,t){return e.indexOf(t)>=0?e:[].concat(hx(e),[t])},[]))}else if("category"===h)A=v?A.filter(function(e){return""!==e&&!e1()(e)}):uI(T,A,r).reduce(function(e,t){return e.indexOf(t)>=0||""===t||e1()(t)?e:[].concat(hx(e),[t])},[]);else if("number"===h){var _=uf(j,n.filter(function(e){var t,r,n=i in e.props?e.props[i]:null==(t=e.type.defaultProps)?void 0:t[i],o="hide"in e.props?e.props.hide:null==(r=e.type.defaultProps)?void 0:r.hide;return n===w&&(x||!o)}),y,o,l);_&&(A=_)}p&&("number"===h||"auto"!==b)&&(E=uo(j,y,"category"))}else A=p?no()(0,O):a&&a[w]&&a[w].hasStack&&"number"===h?"expand"===f?[0,1]:uT(a[w].stackGroups,c,s):up(j,n.filter(function(e){var t=i in e.props?e.props[i]:e.type.defaultProps[i],r="hide"in e.props?e.props.hide:e.type.defaultProps.hide;return t===w&&(x||!r)}),h,l,!0);"number"===h?(A=dS(u,A,w,o,g),T&&(A=uC(T,A,m))):"category"===h&&T&&A.every(function(e){return T.indexOf(e)>=0})&&(A=T)}return hS(hS({},t),{},hP({},w,hS(hS({},d),{},{axisType:o,domain:A,categoricalDomain:E,duplicateDomain:k,originalDomain:null!=(P=d.domain)?P:S,isCategorical:p,layout:l})))},{})},hR=function(e,t){var r=t.graphicalItems,n=t.Axis,o=t.axisType,i=t.axisIdKey,a=t.stackGroups,c=t.dataStartIndex,s=t.dataEndIndex,l=e.layout,u=e.children,f=h_(e.data,{graphicalItems:r,dataStartIndex:c,dataEndIndex:s}),p=f.length,d=ud(l,o),h=-1;return r.reduce(function(e,t){var y,m=(void 0!==t.type.defaultProps?hS(hS({},t.type.defaultProps),t.props):t.props)[i],v=hC("number");return e[m]?e:(h++,y=d?no()(0,p):a&&a[m]&&a[m].hasStack?dS(u,y=uT(a[m].stackGroups,c,s),m,o):dS(u,y=uC(v,up(f,r.filter(function(e){var t,r,n=i in e.props?e.props[i]:null==(t=e.type.defaultProps)?void 0:t[i],o="hide"in e.props?e.props.hide:null==(r=e.type.defaultProps)?void 0:r.hide;return n===m&&!o}),"number",l),n.defaultProps.allowDataOverflow),m,o),hS(hS({},e),{},hP({},m,hS(hS({axisType:o},n.defaultProps),{},{hide:!0,orientation:eL()(hk,"".concat(o,".").concat(h%2),null),domain:y,originalDomain:v,isCategorical:d,layout:l}))))},{})},hL=function(e,t){var r=t.axisType,n=void 0===r?"xAxis":r,o=t.AxisComp,i=t.graphicalItems,a=t.stackGroups,c=t.dataStartIndex,s=t.dataEndIndex,l=e.children,u="".concat(n,"Id"),f=th(l,o),p={};return f&&f.length?p=hB(e,{axes:f,graphicalItems:i,axisType:n,axisIdKey:u,stackGroups:a,dataStartIndex:c,dataEndIndex:s}):i&&i.length&&(p=hR(e,{Axis:o,graphicalItems:i,axisType:n,axisIdKey:u,stackGroups:a,dataStartIndex:c,dataEndIndex:s})),p},hz=function(e){var t=eG(e),r=uy(t,!1,!0);return{tooltipTicks:r,orderedTooltipTicks:tT()(r,function(e){return e.coordinate}),tooltipAxis:t,tooltipAxisBandSize:uD(t,r)}},hF=function(e){var t=e.children,r=e.defaultShowTooltip,n=ty(t,u1),o=0,i=0;return e.data&&0!==e.data.length&&(i=e.data.length-1),n&&n.props&&(n.props.startIndex>=0&&(o=n.props.startIndex),n.props.endIndex>=0&&(i=n.props.endIndex)),{chartX:0,chartY:0,dataStartIndex:o,dataEndIndex:i,activeTooltipIndex:-1,isTooltipActive:!!r}},hU=function(e){return"horizontal"===e?{numericAxisName:"yAxis",cateAxisName:"xAxis"}:"vertical"===e?{numericAxisName:"xAxis",cateAxisName:"yAxis"}:"centric"===e?{numericAxisName:"radiusAxis",cateAxisName:"angleAxis"}:{numericAxisName:"angleAxis",cateAxisName:"radiusAxis"}},h$=function(e,t){var r=e.props,n=e.graphicalItems,o=e.xAxisMap,i=void 0===o?{}:o,a=e.yAxisMap,c=void 0===a?{}:a,s=r.width,l=r.height,u=r.children,f=r.margin||{},p=ty(u,u1),d=ty(u,r4),h=Object.keys(c).reduce(function(e,t){var r=c[t],n=r.orientation;return r.mirror||r.hide?e:hS(hS({},e),{},hP({},n,e[n]+r.width))},{left:f.left||0,right:f.right||0}),y=Object.keys(i).reduce(function(e,t){var r=i[t],n=r.orientation;return r.mirror||r.hide?e:hS(hS({},e),{},hP({},n,eL()(e,"".concat(n))+r.height))},{top:f.top||0,bottom:f.bottom||0}),m=hS(hS({},y),h),v=m.bottom;p&&(m.bottom+=p.props.height||u1.defaultProps.height),d&&t&&(m=ul(m,n,r,t));var b=s-m.left-m.right,g=l-m.top-m.bottom;return hS(hS({brushBottom:v},m),{},{width:Math.max(b,0),height:Math.max(g,0)})},hq=["type","layout","connectNulls","ref"],hW=["key"];function hH(e){return(hH="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function hX(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}function hV(){return(hV=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function hG(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function hK(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?hG(Object(r),!0).forEach(function(t){h2(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):hG(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function hY(e){return function(e){if(Array.isArray(e))return hZ(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return hZ(e,void 0);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return hZ(e,t)}}(e)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function hZ(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function hJ(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,h5(n.key),n)}}function hQ(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(hQ=function(){return!!e})()}function h0(e){return(h0=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function h1(e,t){return(h1=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function h2(e,t,r){return(t=h5(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function h5(e){var t=function(e,t){if("object"!=hH(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=hH(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==hH(t)?t:t+""}var h4=function(e){var t,r;function n(){var e,t,r;if(!(this instanceof n))throw TypeError("Cannot call a class as a function");for(var o=arguments.length,i=Array(o),a=0;a<o;a++)i[a]=arguments[a];return t=n,r=[].concat(i),t=h0(t),h2(e=function(e,t){if(t&&("object"===hH(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");var r=e;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,hQ()?Reflect.construct(t,r||[],h0(this).constructor):t.apply(this,r)),"state",{isAnimationFinished:!0,totalLength:0}),h2(e,"generateSimpleStrokeDasharray",function(e,t){return"".concat(t,"px ").concat(e-t,"px")}),h2(e,"getStrokeDasharray",function(t,r,o){var i=o.reduce(function(e,t){return e+t});if(!i)return e.generateSimpleStrokeDasharray(r,t);for(var a=Math.floor(t/i),c=t%i,s=r-t,l=[],u=0,f=0;u<o.length;f+=o[u],++u)if(f+o[u]>c){l=[].concat(hY(o.slice(0,u)),[c-f]);break}var p=l.length%2==0?[0,s]:[s];return[].concat(hY(n.repeat(o,a)),hY(l),p).map(function(e){return"".concat(e,"px")}).join(", ")}),h2(e,"id",eX("recharts-line-")),h2(e,"pathRef",function(t){e.mainCurve=t}),h2(e,"handleAnimationEnd",function(){e.setState({isAnimationFinished:!0}),e.props.onAnimationEnd&&e.props.onAnimationEnd()}),h2(e,"handleAnimationStart",function(){e.setState({isAnimationFinished:!1}),e.props.onAnimationStart&&e.props.onAnimationStart()}),e}if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");return n.prototype=Object.create(e&&e.prototype,{constructor:{value:n,writable:!0,configurable:!0}}),Object.defineProperty(n,"prototype",{writable:!1}),e&&h1(n,e),t=[{key:"componentDidMount",value:function(){if(this.props.isAnimationActive){var e=this.getTotalLength();this.setState({totalLength:e})}}},{key:"componentDidUpdate",value:function(){if(this.props.isAnimationActive){var e=this.getTotalLength();e!==this.state.totalLength&&this.setState({totalLength:e})}}},{key:"getTotalLength",value:function(){var e=this.mainCurve;try{return e&&e.getTotalLength&&e.getTotalLength()||0}catch(e){return 0}}},{key:"renderErrorBar",value:function(e,t){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var r=this.props,n=r.points,o=r.xAxis,i=r.yAxis,a=r.layout,c=th(r.children,l2);if(!c)return null;var s=function(e,t){return{x:e.x,y:e.y,value:e.value,errorVal:un(e.payload,t)}};return u().createElement(ns,{clipPath:e?"url(#clipPath-".concat(t,")"):null},c.map(function(e){return u().cloneElement(e,{key:"bar-".concat(e.props.dataKey),data:n,xAxis:o,yAxis:i,layout:a,dataPointFormatter:s})}))}},{key:"renderDots",value:function(e,t,r){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var o=this.props,i=o.dot,a=o.points,c=o.dataKey,s=tg(this.props,!1),l=tg(i,!0),f=a.map(function(e,t){var r=hK(hK(hK({key:"dot-".concat(t),r:3},s),l),{},{index:t,cx:e.x,cy:e.y,value:e.value,dataKey:c,payload:e.payload,points:a});return n.renderDotItem(i,r)}),p={clipPath:e?"url(#clipPath-".concat(t?"":"dots-").concat(r,")"):null};return u().createElement(ns,hV({className:"recharts-line-dots",key:"dots"},p),f)}},{key:"renderCurveStatically",value:function(e,t,r,n){var o=this.props,i=o.type,a=o.layout,c=o.connectNulls,s=hK(hK(hK({},tg((o.ref,hX(o,hq)),!0)),{},{fill:"none",className:"recharts-line-curve",clipPath:t?"url(#clipPath-".concat(r,")"):null,points:e},n),{},{type:i,layout:a,connectNulls:c});return u().createElement(he,hV({},s,{pathRef:this.pathRef}))}},{key:"renderCurveWithAnimation",value:function(e,t){var r=this,n=this.props,o=n.points,i=n.strokeDasharray,a=n.isAnimationActive,c=n.animationBegin,s=n.animationDuration,l=n.animationEasing,f=n.animationId,p=n.animateNewValues,d=n.width,h=n.height,y=this.state,m=y.prevPoints,v=y.totalLength;return u().createElement(ox,{begin:c,duration:s,isActive:a,easing:l,from:{t:0},to:{t:1},key:"line-".concat(f),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},function(n){var a,c=n.t;if(m){var s=m.length/o.length,l=o.map(function(e,t){var r=Math.floor(t*s);if(m[r]){var n=m[r],o=eY(n.x,e.x),i=eY(n.y,e.y);return hK(hK({},e),{},{x:o(c),y:i(c)})}if(p){var a=eY(2*d,e.x),l=eY(h/2,e.y);return hK(hK({},e),{},{x:a(c),y:l(c)})}return hK(hK({},e),{},{x:e.x,y:e.y})});return r.renderCurveStatically(l,e,t)}var u=eY(0,v)(c);if(i){var f="".concat(i).split(/[,\s]+/gim).map(function(e){return parseFloat(e)});a=r.getStrokeDasharray(u,v,f)}else a=r.generateSimpleStrokeDasharray(v,u);return r.renderCurveStatically(o,e,t,{strokeDasharray:a})})}},{key:"renderCurve",value:function(e,t){var r=this.props,n=r.points,o=r.isAnimationActive,i=this.state,a=i.prevPoints,c=i.totalLength;return o&&n&&n.length&&(!a&&c>0||!le()(a,n))?this.renderCurveWithAnimation(e,t):this.renderCurveStatically(n,e,t)}},{key:"render",value:function(){var e,t=this.props,r=t.hide,n=t.dot,o=t.points,i=t.className,a=t.xAxis,c=t.yAxis,s=t.top,l=t.left,f=t.width,p=t.height,d=t.isAnimationActive,h=t.id;if(r||!o||!o.length)return null;var y=this.state.isAnimationFinished,m=1===o.length,v=(0,eT.A)("recharts-line",i),b=a&&a.allowDataOverflow,g=c&&c.allowDataOverflow,x=b||g,w=e1()(h)?this.id:h,j=null!=(e=tg(n,!1))?e:{r:3,strokeWidth:2},O=j.r,S=j.strokeWidth,P=(n&&"object"===ts(n)&&"clipDot"in n?n:{}).clipDot,A=void 0===P||P,k=2*(void 0===O?3:O)+(void 0===S?2:S);return u().createElement(ns,{className:v},b||g?u().createElement("defs",null,u().createElement("clipPath",{id:"clipPath-".concat(w)},u().createElement("rect",{x:b?l:l-f/2,y:g?s:s-p/2,width:b?f:2*f,height:g?p:2*p})),!A&&u().createElement("clipPath",{id:"clipPath-dots-".concat(w)},u().createElement("rect",{x:l-k/2,y:s-k/2,width:f+k,height:p+k}))):null,!m&&this.renderCurve(x,w),this.renderErrorBar(x,w),(m||n)&&this.renderDots(x,A,w),(!d||y)&&fT.renderCallByParent(this.props,o))}}],r=[{key:"getDerivedStateFromProps",value:function(e,t){return e.animationId!==t.prevAnimationId?{prevAnimationId:e.animationId,curPoints:e.points,prevPoints:t.curPoints}:e.points!==t.curPoints?{curPoints:e.points}:null}},{key:"repeat",value:function(e,t){for(var r=e.length%2!=0?[].concat(hY(e),[0]):e,n=[],o=0;o<t;++o)n=[].concat(hY(n),hY(r));return n}},{key:"renderDotItem",value:function(e,t){var r;if(u().isValidElement(e))r=u().cloneElement(e,t);else if(e5()(e))r=e(t);else{var n=t.key,o=hX(t,hW),i=(0,eT.A)("recharts-line-dot","boolean"!=typeof e?e.className:"");r=u().createElement(nu,hV({key:n},o,{className:i}))}return r}}],t&&hJ(n.prototype,t),r&&hJ(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(l.PureComponent);function h3(e,t,r){if(t<1)return[];if(1===t&&void 0===r)return e;for(var n=[],o=0;o<e.length;o+=t)if(void 0!==r&&!0!==r(e[o]))return;else n.push(e[o]);return n}function h6(e,t,r,n,o){if(e*t<e*n||e*t>e*o)return!1;var i=r();return e*(t-e*i/2-n)>=0&&e*(t+e*i/2-o)<=0}function h8(e){return(h8="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function h7(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function h9(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?h7(Object(r),!0).forEach(function(t){var n,o,i;n=e,o=t,i=r[t],(o=function(e){var t=function(e,t){if("object"!=h8(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=h8(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==h8(t)?t:t+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):h7(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function ye(e,t,r){var n,o,i,a,c,s=e.tick,l=e.ticks,u=e.viewBox,f=e.minTickGap,p=e.orientation,d=e.interval,h=e.tickFormatter,y=e.unit,m=e.angle;if(!l||!l.length||!s)return[];if(eq(d)||tJ.isSsr)return h3(l,("number"==typeof d&&eq(d)?d:0)+1);var v=[],b="top"===p||"bottom"===p?"width":"height",g=y&&"width"===b?oX(y,{fontSize:t,letterSpacing:r}):{width:0,height:0},x=function(e,n){var o,i=e5()(h)?h(e.value,n):e.value;return"width"===b?(o=oX(i,{fontSize:t,letterSpacing:r}),pN({width:o.width+g.width,height:o.height+g.height},m)):oX(i,{fontSize:t,letterSpacing:r})[b]},w=l.length>=2?eU(l[1].coordinate-l[0].coordinate):1,j=(n="width"===b,o=u.x,i=u.y,a=u.width,c=u.height,1===w?{start:n?o:i,end:n?o+a:i+c}:{start:n?o+a:i+c,end:n?o:i});return"equidistantPreserveStart"===d?function(e,t,r,n,o){for(var i,a=(n||[]).slice(),c=t.start,s=t.end,l=0,u=1,f=c;u<=a.length;)if(i=function(){var t,i=null==n?void 0:n[l];if(void 0===i)return{v:h3(n,u)};var a=l,p=function(){return void 0===t&&(t=r(i,a)),t},d=i.coordinate,h=0===l||h6(e,d,p,f,s);h||(l=0,f=c,u+=1),h&&(f=d+e*(p()/2+o),l+=u)}())return i.v;return[]}(w,j,x,l,f):("preserveStart"===d||"preserveStartEnd"===d?function(e,t,r,n,o,i){var a=(n||[]).slice(),c=a.length,s=t.start,l=t.end;if(i){var u=n[c-1],f=r(u,c-1),p=e*(u.coordinate+e*f/2-l);a[c-1]=u=h9(h9({},u),{},{tickCoord:p>0?u.coordinate-p*e:u.coordinate}),h6(e,u.tickCoord,function(){return f},s,l)&&(l=u.tickCoord-e*(f/2+o),a[c-1]=h9(h9({},u),{},{isShow:!0}))}for(var d=i?c-1:c,h=function(t){var n,i=a[t],c=function(){return void 0===n&&(n=r(i,t)),n};if(0===t){var u=e*(i.coordinate-e*c()/2-s);a[t]=i=h9(h9({},i),{},{tickCoord:u<0?i.coordinate-u*e:i.coordinate})}else a[t]=i=h9(h9({},i),{},{tickCoord:i.coordinate});h6(e,i.tickCoord,c,s,l)&&(s=i.tickCoord+e*(c()/2+o),a[t]=h9(h9({},i),{},{isShow:!0}))},y=0;y<d;y++)h(y);return a}(w,j,x,l,f,"preserveStartEnd"===d):function(e,t,r,n,o){for(var i=(n||[]).slice(),a=i.length,c=t.start,s=t.end,l=function(t){var n,l=i[t],u=function(){return void 0===n&&(n=r(l,t)),n};if(t===a-1){var f=e*(l.coordinate+e*u()/2-s);i[t]=l=h9(h9({},l),{},{tickCoord:f>0?l.coordinate-f*e:l.coordinate})}else i[t]=l=h9(h9({},l),{},{tickCoord:l.coordinate});h6(e,l.tickCoord,u,c,s)&&(s=l.tickCoord-e*(u()/2+o),i[t]=h9(h9({},l),{},{isShow:!0}))},u=a-1;u>=0;u--)l(u);return i}(w,j,x,l,f)).filter(function(e){return e.isShow})}h2(h4,"displayName","Line"),h2(h4,"defaultProps",{xAxisId:0,yAxisId:0,connectNulls:!1,activeDot:!0,dot:!0,legendType:"line",stroke:"#3182bd",strokeWidth:1,fill:"#fff",points:[],isAnimationActive:!tJ.isSsr,animateNewValues:!0,animationBegin:0,animationDuration:1500,animationEasing:"ease",hide:!1,label:!1}),h2(h4,"getComposedData",function(e){var t=e.props,r=e.xAxis,n=e.yAxis,o=e.xAxisTicks,i=e.yAxisTicks,a=e.dataKey,c=e.bandSize,s=e.displayedData,l=e.offset,u=t.layout;return hK({points:s.map(function(e,t){var s=un(e,a);return"horizontal"===u?{x:uA({axis:r,ticks:o,bandSize:c,entry:e,index:t}),y:e1()(s)?null:n.scale(s),value:s,payload:e}:{x:e1()(s)?null:r.scale(s),y:uA({axis:n,ticks:i,bandSize:c,entry:e,index:t}),value:s,payload:e}}),layout:u},l)});var yt=["viewBox"],yr=["viewBox"],yn=["ticks"];function yo(e){return(yo="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function yi(){return(yi=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function ya(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function yc(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ya(Object(r),!0).forEach(function(t){yd(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ya(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function ys(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}function yl(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,yh(n.key),n)}}function yu(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(yu=function(){return!!e})()}function yf(e){return(yf=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function yp(e,t){return(yp=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function yd(e,t,r){return(t=yh(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function yh(e){var t=function(e,t){if("object"!=yo(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=yo(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==yo(t)?t:t+""}var yy=function(e){var t,r;function n(e){var t,r,o;if(!(this instanceof n))throw TypeError("Cannot call a class as a function");return r=n,o=[e],r=yf(r),(t=function(e,t){if(t&&("object"===yo(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");var r=e;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,yu()?Reflect.construct(r,o||[],yf(this).constructor):r.apply(this,o))).state={fontSize:"",letterSpacing:""},t}if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");return n.prototype=Object.create(e&&e.prototype,{constructor:{value:n,writable:!0,configurable:!0}}),Object.defineProperty(n,"prototype",{writable:!1}),e&&yp(n,e),t=[{key:"shouldComponentUpdate",value:function(e,t){var r=e.viewBox,n=ys(e,yt),o=this.props,i=o.viewBox,a=ys(o,yr);return!e8(r,i)||!e8(n,a)||!e8(t,this.state)}},{key:"componentDidMount",value:function(){var e=this.layerReference;if(e){var t=e.getElementsByClassName("recharts-cartesian-axis-tick-value")[0];t&&this.setState({fontSize:window.getComputedStyle(t).fontSize,letterSpacing:window.getComputedStyle(t).letterSpacing})}}},{key:"getTickLineCoord",value:function(e){var t,r,n,o,i,a,c=this.props,s=c.x,l=c.y,u=c.width,f=c.height,p=c.orientation,d=c.tickSize,h=c.mirror,y=c.tickMargin,m=h?-1:1,v=e.tickSize||d,b=eq(e.tickCoord)?e.tickCoord:e.coordinate;switch(p){case"top":t=r=e.coordinate,a=(n=(o=l+!h*f)-m*v)-m*y,i=b;break;case"left":n=o=e.coordinate,i=(t=(r=s+!h*u)-m*v)-m*y,a=b;break;case"right":n=o=e.coordinate,i=(t=(r=s+h*u)+m*v)+m*y,a=b;break;default:t=r=e.coordinate,a=(n=(o=l+h*f)+m*v)+m*y,i=b}return{line:{x1:t,y1:n,x2:r,y2:o},tick:{x:i,y:a}}}},{key:"getTickTextAnchor",value:function(){var e,t=this.props,r=t.orientation,n=t.mirror;switch(r){case"left":e=n?"start":"end";break;case"right":e=n?"end":"start";break;default:e="middle"}return e}},{key:"getTickVerticalAnchor",value:function(){var e=this.props,t=e.orientation,r=e.mirror,n="end";switch(t){case"left":case"right":n="middle";break;case"top":n=r?"start":"end";break;default:n=r?"end":"start"}return n}},{key:"renderAxisLine",value:function(){var e=this.props,t=e.x,r=e.y,n=e.width,o=e.height,i=e.orientation,a=e.mirror,c=e.axisLine,s=yc(yc(yc({},tg(this.props,!1)),tg(c,!1)),{},{fill:"none"});if("top"===i||"bottom"===i){var l=+("top"===i&&!a||"bottom"===i&&a);s=yc(yc({},s),{},{x1:t,y1:r+l*o,x2:t+n,y2:r+l*o})}else{var f=+("left"===i&&!a||"right"===i&&a);s=yc(yc({},s),{},{x1:t+f*n,y1:r,x2:t+f*n,y2:r+o})}return u().createElement("line",yi({},s,{className:(0,eT.A)("recharts-cartesian-axis-line",eL()(c,"className"))}))}},{key:"renderTicks",value:function(e,t,r){var o=this,i=this.props,a=i.tickLine,c=i.stroke,s=i.tick,l=i.tickFormatter,f=i.unit,p=ye(yc(yc({},this.props),{},{ticks:e}),t,r),d=this.getTickTextAnchor(),h=this.getTickVerticalAnchor(),y=tg(this.props,!1),m=tg(s,!1),v=yc(yc({},y),{},{fill:"none"},tg(a,!1)),b=p.map(function(e,t){var r=o.getTickLineCoord(e),i=r.line,b=r.tick,g=yc(yc(yc(yc({textAnchor:d,verticalAnchor:h},y),{},{stroke:"none",fill:c},m),b),{},{index:t,payload:e,visibleTicksCount:p.length,tickFormatter:l});return u().createElement(ns,yi({className:"recharts-cartesian-axis-tick",key:"tick-".concat(e.value,"-").concat(e.coordinate,"-").concat(e.tickCoord)},to(o.props,e,t)),a&&u().createElement("line",yi({},v,i,{className:(0,eT.A)("recharts-cartesian-axis-tick-line",eL()(a,"className"))})),s&&n.renderTickItem(s,g,"".concat(e5()(l)?l(e.value,t):e.value).concat(f||"")))});return u().createElement("g",{className:"recharts-cartesian-axis-ticks"},b)}},{key:"render",value:function(){var e=this,t=this.props,r=t.axisLine,n=t.width,o=t.height,i=t.ticksGenerator,a=t.className;if(t.hide)return null;var c=this.props,s=c.ticks,l=ys(c,yn),f=s;return(e5()(i)&&(f=i(s&&s.length>0?this.props:l)),n<=0||o<=0||!f||!f.length)?null:u().createElement(ns,{className:(0,eT.A)("recharts-cartesian-axis",a),ref:function(t){e.layerReference=t}},r&&this.renderAxisLine(),this.renderTicks(f,this.state.fontSize,this.state.letterSpacing),ff.renderCallByParent(this.props))}}],r=[{key:"renderTickItem",value:function(e,t,r){var n;return u().isValidElement(e)?u().cloneElement(e,t):e5()(e)?e(t):u().createElement(iu,yi({},t,{className:"recharts-cartesian-axis-tick-value"}),r)}}],t&&yl(n.prototype,t),r&&yl(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(l.Component);function ym(e){return(ym="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}yd(yy,"displayName","CartesianAxis"),yd(yy,"defaultProps",{x:0,y:0,width:0,height:0,viewBox:{x:0,y:0,width:0,height:0},orientation:"bottom",ticks:[],stroke:"#666",tickLine:!0,axisLine:!0,tick:!0,mirror:!1,minTickGap:5,tickSize:6,tickMargin:2,interval:"preserveEnd"});function yv(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(yv=function(){return!!e})()}function yb(e){return(yb=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function yg(e,t){return(yg=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function yx(e,t,r){return(t=yw(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function yw(e){var t=function(e,t){if("object"!=ym(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=ym(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==ym(t)?t:t+""}function yj(){return(yj=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function yO(e){var t=e.xAxisId,r=p4(),n=p3(),o=p1(t);return null==o?null:u().createElement(yy,yj({},o,{className:(0,eT.A)("recharts-".concat(o.axisType," ").concat(o.axisType),o.className),viewBox:{x:0,y:0,width:r,height:n},ticksGenerator:function(e){return uy(e,!0)}}))}var yS=function(e){var t;function r(){var e,t;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return e=r,t=arguments,e=yb(e),function(e,t){if(t&&("object"===ym(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");var r=e;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,yv()?Reflect.construct(e,t||[],yb(this).constructor):e.apply(this,t))}if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(e&&e.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),e&&yg(r,e),t=[{key:"render",value:function(){return u().createElement(yO,this.props)}}],function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,yw(n.key),n)}}(r.prototype,t),Object.defineProperty(r,"prototype",{writable:!1}),r}(u().Component);function yP(e){return(yP="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}yx(yS,"displayName","XAxis"),yx(yS,"defaultProps",{allowDecimals:!0,hide:!1,orientation:"bottom",width:0,height:30,mirror:!1,xAxisId:0,tickCount:5,type:"category",padding:{left:0,right:0},allowDataOverflow:!1,scale:"auto",reversed:!1,allowDuplicatedCategory:!0});function yA(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(yA=function(){return!!e})()}function yk(e){return(yk=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function yE(e,t){return(yE=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function yN(e,t,r){return(t=yT(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function yT(e){var t=function(e,t){if("object"!=yP(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=yP(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==yP(t)?t:t+""}function yM(){return(yM=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}var y_=function(e){var t=e.yAxisId,r=p4(),n=p3(),o=p5(t);return null==o?null:u().createElement(yy,yM({},o,{className:(0,eT.A)("recharts-".concat(o.axisType," ").concat(o.axisType),o.className),viewBox:{x:0,y:0,width:r,height:n},ticksGenerator:function(e){return uy(e,!0)}}))},yC=function(e){var t;function r(){var e,t;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return e=r,t=arguments,e=yk(e),function(e,t){if(t&&("object"===yP(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");var r=e;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,yA()?Reflect.construct(e,t||[],yk(this).constructor):e.apply(this,t))}if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(e&&e.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),e&&yE(r,e),t=[{key:"render",value:function(){return u().createElement(y_,this.props)}}],function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,yT(n.key),n)}}(r.prototype,t),Object.defineProperty(r,"prototype",{writable:!1}),r}(u().Component);yN(yC,"displayName","YAxis"),yN(yC,"defaultProps",{allowDuplicatedCategory:!0,allowDecimals:!0,hide:!1,orientation:"left",width:60,height:0,mirror:!1,yAxisId:0,tickCount:5,type:"number",padding:{top:0,bottom:0},allowDataOverflow:!1,scale:"auto",reversed:!1});var yD=function(e){var t=e.chartName,r=e.GraphicalChild,n=e.defaultTooltipEventType,o=void 0===n?"axis":n,i=e.validateTooltipEventTypes,a=void 0===i?["axis"]:i,c=e.axisComponents,s=e.legendContent,f=e.formatAxisMap,p=e.defaultProps,d=function(e,t){var r=t.graphicalItems,n=t.stackGroups,o=t.offset,i=t.updateId,a=t.dataStartIndex,s=t.dataEndIndex,l=e.barSize,u=e.layout,f=e.barGap,p=e.barCategoryGap,d=e.maxBarSize,h=hU(u),y=h.numericAxisName,m=h.cateAxisName,v=!!r&&!!r.length&&r.some(function(e){var t=tu(e&&e.type);return t&&t.indexOf("Bar")>=0}),b=[];return r.forEach(function(r,h){var g=h_(e.data,{graphicalItems:[r],dataStartIndex:a,dataEndIndex:s}),x=void 0!==r.type.defaultProps?hS(hS({},r.type.defaultProps),r.props):r.props,w=x.dataKey,j=x.maxBarSize,O=x["".concat(y,"Id")],S=x["".concat(m,"Id")],P=c.reduce(function(e,r){var n=t["".concat(r.axisType,"Map")],o=x["".concat(r.axisType,"Id")];n&&n[o]||"zAxis"===r.axisType||ni(!1);var i=n[o];return hS(hS({},e),{},hP(hP({},r.axisType,i),"".concat(r.axisType,"Ticks"),uy(i)))},{}),A=P[m],k=P["".concat(m,"Ticks")],E=n&&n[O]&&n[O].hasStack&&uN(r,n[O].stackGroups),N=tu(r.type).indexOf("Bar")>=0,T=uD(A,k),M=[],_=v&&uc({barSize:l,stackGroups:n,totalSize:"xAxis"===m?P[m].width:"yAxis"===m?P[m].height:void 0});if(N){var C,D,I=e1()(j)?d:j,B=null!=(C=null!=(D=uD(A,k,!0))?D:I)?C:0;M=us({barGap:f,barCategoryGap:p,bandSize:B!==T?B:T,sizeList:_[S],maxBarSize:I}),B!==T&&(M=M.map(function(e){return hS(hS({},e),{},{position:hS(hS({},e.position),{},{offset:e.position.offset-B/2})})}))}var R=r&&r.type&&r.type.getComposedData;R&&b.push({props:hS(hS({},R(hS(hS({},P),{},{displayedData:g,props:e,dataKey:w,item:r,bandSize:T,barPosition:M,offset:o,stackedData:E,layout:u,dataStartIndex:a,dataEndIndex:s}))),{},hP(hP(hP({key:r.key||"item-".concat(h)},y,P[y]),m,P[m]),"animationId",i)),childIndex:td(e.children).indexOf(r),item:r})}),b},h=function(e,n){var o=e.props,i=e.dataStartIndex,a=e.dataEndIndex,s=e.updateId;if(!tm({props:o}))return null;var l=o.children,u=o.layout,p=o.stackOffset,h=o.data,y=o.reverseStackOrder,m=hU(u),v=m.numericAxisName,b=m.cateAxisName,g=th(l,r),x=uS(h,g,"".concat(v,"Id"),"".concat(b,"Id"),p,y),w=c.reduce(function(e,t){var r="".concat(t.axisType,"Map");return hS(hS({},e),{},hP({},r,hL(o,hS(hS({},t),{},{graphicalItems:g,stackGroups:t.axisType===v&&x,dataStartIndex:i,dataEndIndex:a}))))},{}),j=h$(hS(hS({},w),{},{props:o,graphicalItems:g}),null==n?void 0:n.legendBBox);Object.keys(w).forEach(function(e){w[e]=f(o,w[e],j,e.replace("Map",""),t)});var O=hz(w["".concat(b,"Map")]),S=d(o,hS(hS({},w),{},{dataStartIndex:i,dataEndIndex:a,updateId:s,graphicalItems:g,stackGroups:x,offset:j}));return hS(hS({formattedGraphicalItems:S,graphicalItems:g,offset:j,stackGroups:x},O),w)},y=function(e){var r;function n(e){var r,o,i,a,c;if(!(this instanceof n))throw TypeError("Cannot call a class as a function");return a=n,c=[e],a=hb(a),hP(i=function(e,t){if(t&&("object"===hd(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");var r=e;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,hv()?Reflect.construct(a,c||[],hb(this).constructor):a.apply(this,c)),"eventEmitterSymbol",Symbol("rechartsEventEmitter")),hP(i,"accessibilityManager",new dM),hP(i,"handleLegendBBoxUpdate",function(e){if(e){var t=i.state,r=t.dataStartIndex,n=t.dataEndIndex,o=t.updateId;i.setState(hS({legendBBox:e},h({props:i.props,dataStartIndex:r,dataEndIndex:n,updateId:o},hS(hS({},i.state),{},{legendBBox:e}))))}}),hP(i,"handleReceiveSyncEvent",function(e,t,r){i.props.syncId===e&&(r!==i.eventEmitterSymbol||"function"==typeof i.props.syncMethod)&&i.applySyncEvent(t)}),hP(i,"handleBrushChange",function(e){var t=e.startIndex,r=e.endIndex;if(t!==i.state.dataStartIndex||r!==i.state.dataEndIndex){var n=i.state.updateId;i.setState(function(){return hS({dataStartIndex:t,dataEndIndex:r},h({props:i.props,dataStartIndex:t,dataEndIndex:r,updateId:n},i.state))}),i.triggerSyncEvent({dataStartIndex:t,dataEndIndex:r})}}),hP(i,"handleMouseEnter",function(e){var t=i.getMouseInfo(e);if(t){var r=hS(hS({},t),{},{isTooltipActive:!0});i.setState(r),i.triggerSyncEvent(r);var n=i.props.onMouseEnter;e5()(n)&&n(r,e)}}),hP(i,"triggeredAfterMouseMove",function(e){var t=i.getMouseInfo(e),r=t?hS(hS({},t),{},{isTooltipActive:!0}):{isTooltipActive:!1};i.setState(r),i.triggerSyncEvent(r);var n=i.props.onMouseMove;e5()(n)&&n(r,e)}),hP(i,"handleItemMouseEnter",function(e){i.setState(function(){return{isTooltipActive:!0,activeItem:e,activePayload:e.tooltipPayload,activeCoordinate:e.tooltipPosition||{x:e.cx,y:e.cy}}})}),hP(i,"handleItemMouseLeave",function(){i.setState(function(){return{isTooltipActive:!1}})}),hP(i,"handleMouseMove",function(e){e.persist(),i.throttleTriggeredAfterMouseMove(e)}),hP(i,"handleMouseLeave",function(e){i.throttleTriggeredAfterMouseMove.cancel();var t={isTooltipActive:!1};i.setState(t),i.triggerSyncEvent(t);var r=i.props.onMouseLeave;e5()(r)&&r(t,e)}),hP(i,"handleOuterEvent",function(e){var t,r,n=tO(e),o=eL()(i.props,"".concat(n));n&&e5()(o)&&o(null!=(t=/.*touch.*/i.test(n)?i.getMouseInfo(e.changedTouches[0]):i.getMouseInfo(e))?t:{},e)}),hP(i,"handleClick",function(e){var t=i.getMouseInfo(e);if(t){var r=hS(hS({},t),{},{isTooltipActive:!0});i.setState(r),i.triggerSyncEvent(r);var n=i.props.onClick;e5()(n)&&n(r,e)}}),hP(i,"handleMouseDown",function(e){var t=i.props.onMouseDown;e5()(t)&&t(i.getMouseInfo(e),e)}),hP(i,"handleMouseUp",function(e){var t=i.props.onMouseUp;e5()(t)&&t(i.getMouseInfo(e),e)}),hP(i,"handleTouchMove",function(e){null!=e.changedTouches&&e.changedTouches.length>0&&i.throttleTriggeredAfterMouseMove(e.changedTouches[0])}),hP(i,"handleTouchStart",function(e){null!=e.changedTouches&&e.changedTouches.length>0&&i.handleMouseDown(e.changedTouches[0])}),hP(i,"handleTouchEnd",function(e){null!=e.changedTouches&&e.changedTouches.length>0&&i.handleMouseUp(e.changedTouches[0])}),hP(i,"handleDoubleClick",function(e){var t=i.props.onDoubleClick;e5()(t)&&t(i.getMouseInfo(e),e)}),hP(i,"handleContextMenu",function(e){var t=i.props.onContextMenu;e5()(t)&&t(i.getMouseInfo(e),e)}),hP(i,"triggerSyncEvent",function(e){void 0!==i.props.syncId&&dA.emit(dk,i.props.syncId,e,i.eventEmitterSymbol)}),hP(i,"applySyncEvent",function(e){var t=i.props,r=t.layout,n=t.syncMethod,o=i.state.updateId,a=e.dataStartIndex,c=e.dataEndIndex;if(void 0!==e.dataStartIndex||void 0!==e.dataEndIndex)i.setState(hS({dataStartIndex:a,dataEndIndex:c},h({props:i.props,dataStartIndex:a,dataEndIndex:c,updateId:o},i.state)));else if(void 0!==e.activeTooltipIndex){var s=e.chartX,l=e.chartY,u=e.activeTooltipIndex,f=i.state,p=f.offset,d=f.tooltipTicks;if(!p)return;if("function"==typeof n)u=n(d,e);else if("value"===n){u=-1;for(var y=0;y<d.length;y++)if(d[y].value===e.activeLabel){u=y;break}}var m=hS(hS({},p),{},{x:p.left,y:p.top}),v=Math.min(s,m.x+m.width),b=Math.min(l,m.y+m.height),g=d[u]&&d[u].value,x=hD(i.state,i.props.data,u),w=d[u]?{x:"horizontal"===r?d[u].coordinate:v,y:"horizontal"===r?b:d[u].coordinate}:hN;i.setState(hS(hS({},e),{},{activeLabel:g,activeCoordinate:w,activePayload:x,activeTooltipIndex:u}))}else i.setState(e)}),hP(i,"renderCursor",function(e){var r,n=i.state,o=n.isTooltipActive,a=n.activeCoordinate,c=n.activePayload,s=n.offset,l=n.activeTooltipIndex,f=n.tooltipAxisBandSize,p=i.getTooltipEventType(),d=null!=(r=e.props.active)?r:o,h=i.props.layout,y=e.key||"_recharts-cursor";return u().createElement(hu,{key:y,activeCoordinate:a,activePayload:c,activeTooltipIndex:l,chartName:t,element:e,isActive:d,layout:h,offset:s,tooltipAxisBandSize:f,tooltipEventType:p})}),hP(i,"renderPolarAxis",function(e,t,r){var n=eL()(e,"type.axisType"),o=eL()(i.state,"".concat(n,"Map")),a=e.type.defaultProps,c=void 0!==a?hS(hS({},a),e.props):e.props,s=o&&o[c["".concat(n,"Id")]];return(0,l.cloneElement)(e,hS(hS({},s),{},{className:(0,eT.A)(n,s.className),key:e.key||"".concat(t,"-").concat(r),ticks:uy(s,!0)}))}),hP(i,"renderPolarGrid",function(e){var t=e.props,r=t.radialLines,n=t.polarAngles,o=t.polarRadius,a=i.state,c=a.radiusAxisMap,s=a.angleAxisMap,u=eG(c),f=eG(s),p=f.cx,d=f.cy,h=f.innerRadius,y=f.outerRadius;return(0,l.cloneElement)(e,{polarAngles:Array.isArray(n)?n:uy(f,!0).map(function(e){return e.coordinate}),polarRadius:Array.isArray(o)?o:uy(u,!0).map(function(e){return e.coordinate}),cx:p,cy:d,innerRadius:h,outerRadius:y,key:e.key||"polar-grid",radialLines:r})}),hP(i,"renderLegend",function(){var e=i.state.formattedGraphicalItems,t=i.props,r=t.children,n=t.width,o=t.height,a=i.props.margin||{},c=l6({children:r,formattedGraphicalItems:e,legendWidth:n-(a.left||0)-(a.right||0),legendContent:s});if(!c)return null;var u=c.item,f=hm(c,hf);return(0,l.cloneElement)(u,hS(hS({},f),{},{chartWidth:n,chartHeight:o,margin:a,onBBoxUpdate:i.handleLegendBBoxUpdate}))}),hP(i,"renderTooltip",function(){var e,t=i.props,r=t.children,n=t.accessibilityLayer,o=ty(r,rt);if(!o)return null;var a=i.state,c=a.isTooltipActive,s=a.activeCoordinate,u=a.activePayload,f=a.activeLabel,p=a.offset,d=null!=(e=o.props.active)?e:c;return(0,l.cloneElement)(o,{viewBox:hS(hS({},p),{},{x:p.left,y:p.top}),active:d,label:f,payload:d?u:[],coordinate:s,accessibilityLayer:n})}),hP(i,"renderBrush",function(e){var t=i.props,r=t.margin,n=t.data,o=i.state,a=o.offset,c=o.dataStartIndex,s=o.dataEndIndex,u=o.updateId;return(0,l.cloneElement)(e,{key:e.key||"_recharts-brush",onChange:uv(i.handleBrushChange,e.props.onChange),data:n,x:eq(e.props.x)?e.props.x:a.left,y:eq(e.props.y)?e.props.y:a.top+a.height+a.brushBottom-(r.bottom||0),width:eq(e.props.width)?e.props.width:a.width,startIndex:c,endIndex:s,updateId:"brush-".concat(u)})}),hP(i,"renderReferenceElement",function(e,t,r){if(!e)return null;var n=i.clipPathId,o=i.state,a=o.xAxisMap,c=o.yAxisMap,s=o.offset,u=e.type.defaultProps||{},f=e.props,p=f.xAxisId,d=void 0===p?u.xAxisId:p,h=f.yAxisId,y=void 0===h?u.yAxisId:h;return(0,l.cloneElement)(e,{key:e.key||"".concat(t,"-").concat(r),xAxis:a[d],yAxis:c[y],viewBox:{x:s.left,y:s.top,width:s.width,height:s.height},clipPathId:n})}),hP(i,"renderActivePoints",function(e){var t=e.item,r=e.activePoint,o=e.basePoint,i=e.childIndex,a=e.isRange,c=[],s=t.props.key,l=void 0!==t.item.type.defaultProps?hS(hS({},t.item.type.defaultProps),t.item.props):t.item.props,u=l.activeDot,f=hS(hS({index:i,dataKey:l.dataKey,cx:r.x,cy:r.y,r:4,fill:ua(t.item),strokeWidth:2,stroke:"#fff",payload:r.payload,value:r.value},tg(u,!1)),tn(u));return c.push(n.renderActiveDot(u,f,"".concat(s,"-activePoint-").concat(i))),o?c.push(n.renderActiveDot(u,hS(hS({},f),{},{cx:o.x,cy:o.y}),"".concat(s,"-basePoint-").concat(i))):a&&c.push(null),c}),hP(i,"renderGraphicChild",function(e,t,r){var n=i.filterFormatItem(e,t,r);if(!n)return null;var o=i.getTooltipEventType(),a=i.state,c=a.isTooltipActive,s=a.tooltipAxis,u=a.activeTooltipIndex,f=a.activeLabel,p=ty(i.props.children,rt),d=n.props,h=d.points,y=d.isRange,m=d.baseLine,v=void 0!==n.item.type.defaultProps?hS(hS({},n.item.type.defaultProps),n.item.props):n.item.props,b=v.activeDot,g=v.hide,x=v.activeBar,w=v.activeShape,j=!!(!g&&c&&p&&(b||x||w)),O={};"axis"!==o&&p&&"click"===p.props.trigger?O={onClick:uv(i.handleItemMouseEnter,e.props.onClick)}:"axis"!==o&&(O={onMouseLeave:uv(i.handleItemMouseLeave,e.props.onMouseLeave),onMouseEnter:uv(i.handleItemMouseEnter,e.props.onMouseEnter)});var S=(0,l.cloneElement)(e,hS(hS({},n.props),O));if(j)if(u>=0){if(s.dataKey&&!s.allowDuplicatedCategory){var P="function"==typeof s.dataKey?function(e){return"function"==typeof s.dataKey?s.dataKey(e.payload):null}:"payload.".concat(s.dataKey.toString());k=eZ(h,P,f),E=y&&m&&eZ(m,P,f)}else k=null==h?void 0:h[u],E=y&&m&&m[u];if(w||x){var A=void 0!==e.props.activeIndex?e.props.activeIndex:u;return[(0,l.cloneElement)(e,hS(hS(hS({},n.props),O),{},{activeIndex:A})),null,null]}if(!e1()(k))return[S].concat(hx(i.renderActivePoints({item:n,activePoint:k,basePoint:E,childIndex:u,isRange:y})))}else{var k,E,N,T=(null!=(N=i.getItemByXY(i.state.activeCoordinate))?N:{graphicalItem:S}).graphicalItem,M=T.item,_=void 0===M?e:M,C=T.childIndex,D=hS(hS(hS({},n.props),O),{},{activeIndex:C});return[(0,l.cloneElement)(_,D),null,null]}return y?[S,null,null]:[S,null]}),hP(i,"renderCustomized",function(e,t,r){return(0,l.cloneElement)(e,hS(hS({key:"recharts-customized-".concat(r)},i.props),i.state))}),hP(i,"renderMap",{CartesianGrid:{handler:hT,once:!0},ReferenceArea:{handler:i.renderReferenceElement},ReferenceLine:{handler:hT},ReferenceDot:{handler:i.renderReferenceElement},XAxis:{handler:hT},YAxis:{handler:hT},Brush:{handler:i.renderBrush,once:!0},Bar:{handler:i.renderGraphicChild},Line:{handler:i.renderGraphicChild},Area:{handler:i.renderGraphicChild},Radar:{handler:i.renderGraphicChild},RadialBar:{handler:i.renderGraphicChild},Scatter:{handler:i.renderGraphicChild},Pie:{handler:i.renderGraphicChild},Funnel:{handler:i.renderGraphicChild},Tooltip:{handler:i.renderCursor,once:!0},PolarGrid:{handler:i.renderPolarGrid,once:!0},PolarAngleAxis:{handler:i.renderPolarAxis},PolarRadiusAxis:{handler:i.renderPolarAxis},Customized:{handler:i.renderCustomized}}),i.clipPathId="".concat(null!=(r=e.id)?r:eX("recharts"),"-clip"),i.throttleTriggeredAfterMouseMove=e_()(i.triggeredAfterMouseMove,null!=(o=e.throttleDelay)?o:1e3/60),i.state={},i}if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");return n.prototype=Object.create(e&&e.prototype,{constructor:{value:n,writable:!0,configurable:!0}}),Object.defineProperty(n,"prototype",{writable:!1}),e&&hg(n,e),r=[{key:"componentDidMount",value:function(){var e,t;this.addListener(),this.accessibilityManager.setDetails({container:this.container,offset:{left:null!=(e=this.props.margin.left)?e:0,top:null!=(t=this.props.margin.top)?t:0},coordinateList:this.state.tooltipTicks,mouseHandlerCallback:this.triggeredAfterMouseMove,layout:this.props.layout}),this.displayDefaultTooltip()}},{key:"displayDefaultTooltip",value:function(){var e=this.props,t=e.children,r=e.data,n=e.height,o=e.layout,i=ty(t,rt);if(i){var a=i.props.defaultIndex;if("number"==typeof a&&!(a<0)&&!(a>this.state.tooltipTicks.length-1)){var c=this.state.tooltipTicks[a]&&this.state.tooltipTicks[a].value,s=hD(this.state,r,a,c),l=this.state.tooltipTicks[a].coordinate,u=(this.state.offset.top+n)/2,f="horizontal"===o?{x:l,y:u}:{y:l,x:u},p=this.state.formattedGraphicalItems.find(function(e){return"Scatter"===e.item.type.name});p&&(f=hS(hS({},f),p.props.points[a].tooltipPosition),s=p.props.points[a].tooltipPayload);var d={activeTooltipIndex:a,isTooltipActive:!0,activeLabel:c,activePayload:s,activeCoordinate:f};this.setState(d),this.renderCursor(i),this.accessibilityManager.setIndex(a)}}}},{key:"getSnapshotBeforeUpdate",value:function(e,t){if(!this.props.accessibilityLayer)return null;if(this.state.tooltipTicks!==t.tooltipTicks&&this.accessibilityManager.setDetails({coordinateList:this.state.tooltipTicks}),this.props.layout!==e.layout&&this.accessibilityManager.setDetails({layout:this.props.layout}),this.props.margin!==e.margin){var r,n;this.accessibilityManager.setDetails({offset:{left:null!=(r=this.props.margin.left)?r:0,top:null!=(n=this.props.margin.top)?n:0}})}return null}},{key:"componentDidUpdate",value:function(e){tx([ty(e.children,rt)],[ty(this.props.children,rt)])||this.displayDefaultTooltip()}},{key:"componentWillUnmount",value:function(){this.removeListener(),this.throttleTriggeredAfterMouseMove.cancel()}},{key:"getTooltipEventType",value:function(){var e=ty(this.props.children,rt);if(e&&"boolean"==typeof e.props.shared){var t=e.props.shared?"axis":"item";return a.indexOf(t)>=0?t:o}return o}},{key:"getMouseInfo",value:function(e){if(!this.container)return null;var t=this.container,r=t.getBoundingClientRect(),n={top:r.top+window.scrollY-document.documentElement.clientTop,left:r.left+window.scrollX-document.documentElement.clientLeft},o={chartX:Math.round(e.pageX-n.left),chartY:Math.round(e.pageY-n.top)},i=r.width/t.offsetWidth||1,a=this.inRange(o.chartX,o.chartY,i);if(!a)return null;var c=this.state,s=c.xAxisMap,l=c.yAxisMap,u=this.getTooltipEventType(),f=hI(this.state,this.props.data,this.props.layout,a);if("axis"!==u&&s&&l){var p=eG(s).scale,d=eG(l).scale,h=p&&p.invert?p.invert(o.chartX):null,y=d&&d.invert?d.invert(o.chartY):null;return hS(hS({},o),{},{xValue:h,yValue:y},f)}return f?hS(hS({},o),f):null}},{key:"inRange",value:function(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,n=this.props.layout,o=e/r,i=t/r;if("horizontal"===n||"vertical"===n){var a=this.state.offset;return o>=a.left&&o<=a.left+a.width&&i>=a.top&&i<=a.top+a.height?{x:o,y:i}:null}var c=this.state,s=c.angleAxisMap,l=c.radiusAxisMap;return s&&l?fe({x:o,y:i},eG(s)):null}},{key:"parseEventsOfWrapper",value:function(){var e=this.props.children,t=this.getTooltipEventType(),r=ty(e,rt),n={};return r&&"axis"===t&&(n="click"===r.props.trigger?{onClick:this.handleClick}:{onMouseEnter:this.handleMouseEnter,onDoubleClick:this.handleDoubleClick,onMouseMove:this.handleMouseMove,onMouseLeave:this.handleMouseLeave,onTouchMove:this.handleTouchMove,onTouchStart:this.handleTouchStart,onTouchEnd:this.handleTouchEnd,onContextMenu:this.handleContextMenu}),hS(hS({},tn(this.props,this.handleOuterEvent)),n)}},{key:"addListener",value:function(){dA.on(dk,this.handleReceiveSyncEvent)}},{key:"removeListener",value:function(){dA.removeListener(dk,this.handleReceiveSyncEvent)}},{key:"filterFormatItem",value:function(e,t,r){for(var n=this.state.formattedGraphicalItems,o=0,i=n.length;o<i;o++){var a=n[o];if(a.item===e||a.props.key===e.key||t===tu(a.item.type)&&r===a.childIndex)return a}return null}},{key:"renderClipPath",value:function(){var e=this.clipPathId,t=this.state.offset,r=t.left,n=t.top,o=t.height,i=t.width;return u().createElement("defs",null,u().createElement("clipPath",{id:e},u().createElement("rect",{x:r,y:n,height:o,width:i})))}},{key:"getXScales",value:function(){var e=this.state.xAxisMap;return e?Object.entries(e).reduce(function(e,t){var r=hy(t,2),n=r[0],o=r[1];return hS(hS({},e),{},hP({},n,o.scale))},{}):null}},{key:"getYScales",value:function(){var e=this.state.yAxisMap;return e?Object.entries(e).reduce(function(e,t){var r=hy(t,2),n=r[0],o=r[1];return hS(hS({},e),{},hP({},n,o.scale))},{}):null}},{key:"getXScaleByAxisId",value:function(e){var t;return null==(t=this.state.xAxisMap)||null==(t=t[e])?void 0:t.scale}},{key:"getYScaleByAxisId",value:function(e){var t;return null==(t=this.state.yAxisMap)||null==(t=t[e])?void 0:t.scale}},{key:"getItemByXY",value:function(e){var t=this.state,r=t.formattedGraphicalItems,n=t.activeItem;if(r&&r.length)for(var o=0,i=r.length;o<i;o++){var a=r[o],c=a.props,s=a.item,l=void 0!==s.type.defaultProps?hS(hS({},s.type.defaultProps),s.props):s.props,u=tu(s.type);if("Bar"===u){var f=(c.data||[]).find(function(t){return ok(e,t)});if(f)return{graphicalItem:a,payload:f}}else if("RadialBar"===u){var p=(c.data||[]).find(function(t){return fe(e,t)});if(p)return{graphicalItem:a,payload:p}}else if(f4(a,n)||f3(a,n)||f6(a,n)){var d=function(e){var t,r,n,o=e.activeTooltipItem,i=e.graphicalItem,a=e.itemData,c=(f4(i,o)?t="trapezoids":f3(i,o)?t="sectors":f6(i,o)&&(t="points"),t),s=f4(i,o)?null==(r=o.tooltipPayload)||null==(r=r[0])||null==(r=r.payload)?void 0:r.payload:f3(i,o)?null==(n=o.tooltipPayload)||null==(n=n[0])||null==(n=n.payload)?void 0:n.payload:f6(i,o)?o.payload:{},l=a.filter(function(e,t){var r=le()(s,e),n=i.props[c].filter(function(e){var t;return(f4(i,o)?t=f8:f3(i,o)?t=f7:f6(i,o)&&(t=f9),t)(e,o)}),a=i.props[c].indexOf(n[n.length-1]);return r&&t===a});return a.indexOf(l[l.length-1])}({graphicalItem:a,activeTooltipItem:n,itemData:l.data}),h=void 0===l.activeIndex?d:l.activeIndex;return{graphicalItem:hS(hS({},a),{},{childIndex:h}),payload:f6(a,n)?l.data[d]:a.props.data[d]}}}return null}},{key:"render",value:function(){var e,t,r=this;if(!tm(this))return null;var n=this.props,o=n.children,i=n.className,a=n.width,c=n.height,s=n.style,l=n.compact,f=n.title,p=n.desc,d=tg(hm(n,hp),!1);if(l)return u().createElement(p0,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},u().createElement(ro,hh({},d,{width:a,height:c,title:f,desc:p}),this.renderClipPath(),tj(o,this.renderMap)));this.props.accessibilityLayer&&(d.tabIndex=null!=(e=this.props.tabIndex)?e:0,d.role=null!=(t=this.props.role)?t:"application",d.onKeyDown=function(e){r.accessibilityManager.keyboardEvent(e)},d.onFocus=function(){r.accessibilityManager.focus()});var h=this.parseEventsOfWrapper();return u().createElement(p0,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},u().createElement("div",hh({className:(0,eT.A)("recharts-wrapper",i),style:hS({position:"relative",cursor:"default",width:a,height:c},s)},h,{ref:function(e){r.container=e}}),u().createElement(ro,hh({},d,{width:a,height:c,title:f,desc:p,style:hE}),this.renderClipPath(),tj(o,this.renderMap)),this.renderLegend(),this.renderTooltip()))}}],function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,hA(n.key),n)}}(n.prototype,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(l.Component);hP(y,"displayName",t),hP(y,"defaultProps",hS({layout:"horizontal",stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index"},p)),hP(y,"getDerivedStateFromProps",function(e,t){var r=e.dataKey,n=e.data,o=e.children,i=e.width,a=e.height,c=e.layout,s=e.stackOffset,l=e.margin,u=t.dataStartIndex,f=t.dataEndIndex;if(void 0===t.updateId){var p=hF(e);return hS(hS(hS({},p),{},{updateId:0},h(hS(hS({props:e},p),{},{updateId:0}),t)),{},{prevDataKey:r,prevData:n,prevWidth:i,prevHeight:a,prevLayout:c,prevStackOffset:s,prevMargin:l,prevChildren:o})}if(r!==t.prevDataKey||n!==t.prevData||i!==t.prevWidth||a!==t.prevHeight||c!==t.prevLayout||s!==t.prevStackOffset||!e8(l,t.prevMargin)){var d=hF(e),y={chartX:t.chartX,chartY:t.chartY,isTooltipActive:t.isTooltipActive},m=hS(hS({},hI(t,n,c)),{},{updateId:t.updateId+1}),v=hS(hS(hS({},d),y),m);return hS(hS(hS({},v),h(hS({props:e},v),t)),{},{prevDataKey:r,prevData:n,prevWidth:i,prevHeight:a,prevLayout:c,prevStackOffset:s,prevMargin:l,prevChildren:o})}if(!tx(o,t.prevChildren)){var b,g,x,w,j=ty(o,u1),O=j&&null!=(b=null==(g=j.props)?void 0:g.startIndex)?b:u,S=j&&null!=(x=null==(w=j.props)?void 0:w.endIndex)?x:f,P=e1()(n)||O!==u||S!==f?t.updateId+1:t.updateId;return hS(hS({updateId:P},h(hS(hS({props:e},t),{},{updateId:P,dataStartIndex:O,dataEndIndex:S}),t)),{},{prevChildren:o,dataStartIndex:O,dataEndIndex:S})}return null}),hP(y,"renderActiveDot",function(e,t,r){var n;return n=(0,l.isValidElement)(e)?(0,l.cloneElement)(e,t):e5()(e)?e(t):u().createElement(nu,t),u().createElement(ns,{className:"recharts-active-dot",key:r},n)});var m=(0,l.forwardRef)(function(e,t){return u().createElement(y,hh({},e,{ref:t}))});return m.displayName=y.displayName,m}({chartName:"LineChart",GraphicalChild:h4,axisComponents:[{axisType:"xAxis",AxisComp:yS},{axisType:"yAxis",AxisComp:yC}],formatAxisMap:function(e,t,r,n,o){var i=e.width,a=e.height,c=e.layout,s=e.children,l=Object.keys(t),u={left:r.left,leftMirror:r.left,right:i-r.right,rightMirror:i-r.right,top:r.top,topMirror:r.top,bottom:a-r.bottom,bottomMirror:a-r.bottom},f=!!ty(s,pg);return l.reduce(function(i,a){var s,l,p,d,h,y=t[a],m=y.orientation,v=y.domain,b=y.padding,g=void 0===b?{}:b,x=y.mirror,w=y.reversed,j="".concat(m).concat(x?"Mirror":"");if("number"===y.type&&("gap"===y.padding||"no-gap"===y.padding)){var O=v[1]-v[0],S=1/0,P=y.categoricalDomain.sort(eJ);if(P.forEach(function(e,t){t>0&&(S=Math.min((e||0)-(P[t-1]||0),S))}),Number.isFinite(S)){var A=S/O,k="vertical"===y.layout?r.height:r.width;if("gap"===y.padding&&(s=A*k/2),"no-gap"===y.padding){var E=eV(e.barCategoryGap,A*k),N=A*k/2;s=N-E-(N-E)/k*E}}}l="xAxis"===n?[r.left+(g.left||0)+(s||0),r.left+r.width-(g.right||0)-(s||0)]:"yAxis"===n?"horizontal"===c?[r.top+r.height-(g.bottom||0),r.top+(g.top||0)]:[r.top+(g.top||0)+(s||0),r.top+r.height-(g.bottom||0)-(s||0)]:y.range,w&&(l=[l[1],l[0]]);var T=ub(y,o,f),M=T.scale,_=T.realScaleType;M.domain(v).range(l),ug(M);var C=uP(M,pO(pO({},y),{},{realScaleType:_}));"xAxis"===n?(h="top"===m&&!x||"bottom"===m&&x,p=r.left,d=u[j]-h*y.height):"yAxis"===n&&(h="left"===m&&!x||"right"===m&&x,p=u[j]-h*y.width,d=r.top);var D=pO(pO(pO({},y),C),{},{realScaleType:_,x:p,y:d,scale:M,width:"xAxis"===n?r.width:y.width,height:"yAxis"===n?r.height:y.height});return D.bandSize=uD(D,C),y.hide||"xAxis"!==n?y.hide||(u[j]+=(h?-1:1)*D.width):u[j]+=(h?-1:1)*D.height,pO(pO({},i),{},pS({},a,D))},{})}}),yI=["x1","y1","x2","y2","key"],yB=["offset"];function yR(e){return(yR="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function yL(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function yz(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?yL(Object(r),!0).forEach(function(t){var n,o,i;n=e,o=t,i=r[t],(o=function(e){var t=function(e,t){if("object"!=yR(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=yR(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==yR(t)?t:t+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):yL(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function yF(){return(yF=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function yU(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}var y$=function(e){var t=e.fill;if(!t||"none"===t)return null;var r=e.fillOpacity,n=e.x,o=e.y,i=e.width,a=e.height,c=e.ry;return u().createElement("rect",{x:n,y:o,ry:c,width:i,height:a,stroke:"none",fill:t,fillOpacity:r,className:"recharts-cartesian-grid-bg"})};function yq(e,t){var r;if(u().isValidElement(e))r=u().cloneElement(e,t);else if(e5()(e))r=e(t);else{var n=t.x1,o=t.y1,i=t.x2,a=t.y2,c=t.key,s=tg(yU(t,yI),!1),l=(s.offset,yU(s,yB));r=u().createElement("line",yF({},l,{x1:n,y1:o,x2:i,y2:a,fill:"none",key:c}))}return r}function yW(e){var t=e.x,r=e.width,n=e.horizontal,o=void 0===n||n,i=e.horizontalPoints;if(!o||!i||!i.length)return null;var a=i.map(function(n,i){return yq(o,yz(yz({},e),{},{x1:t,y1:n,x2:t+r,y2:n,key:"line-".concat(i),index:i}))});return u().createElement("g",{className:"recharts-cartesian-grid-horizontal"},a)}function yH(e){var t=e.y,r=e.height,n=e.vertical,o=void 0===n||n,i=e.verticalPoints;if(!o||!i||!i.length)return null;var a=i.map(function(n,i){return yq(o,yz(yz({},e),{},{x1:n,y1:t,x2:n,y2:t+r,key:"line-".concat(i),index:i}))});return u().createElement("g",{className:"recharts-cartesian-grid-vertical"},a)}function yX(e){var t=e.horizontalFill,r=e.fillOpacity,n=e.x,o=e.y,i=e.width,a=e.height,c=e.horizontalPoints,s=e.horizontal;if(!(void 0===s||s)||!t||!t.length)return null;var l=c.map(function(e){return Math.round(e+o-o)}).sort(function(e,t){return e-t});o!==l[0]&&l.unshift(0);var f=l.map(function(e,c){var s=l[c+1]?l[c+1]-e:o+a-e;if(s<=0)return null;var f=c%t.length;return u().createElement("rect",{key:"react-".concat(c),y:e,x:n,height:s,width:i,stroke:"none",fill:t[f],fillOpacity:r,className:"recharts-cartesian-grid-bg"})});return u().createElement("g",{className:"recharts-cartesian-gridstripes-horizontal"},f)}function yV(e){var t=e.vertical,r=e.verticalFill,n=e.fillOpacity,o=e.x,i=e.y,a=e.width,c=e.height,s=e.verticalPoints;if(!(void 0===t||t)||!r||!r.length)return null;var l=s.map(function(e){return Math.round(e+o-o)}).sort(function(e,t){return e-t});o!==l[0]&&l.unshift(0);var f=l.map(function(e,t){var s=l[t+1]?l[t+1]-e:o+a-e;if(s<=0)return null;var f=t%r.length;return u().createElement("rect",{key:"react-".concat(t),x:e,y:i,width:s,height:c,stroke:"none",fill:r[f],fillOpacity:n,className:"recharts-cartesian-grid-bg"})});return u().createElement("g",{className:"recharts-cartesian-gridstripes-vertical"},f)}var yG=function(e,t){var r=e.xAxis,n=e.width,o=e.height,i=e.offset;return uh(ye(yz(yz(yz({},yy.defaultProps),r),{},{ticks:uy(r,!0),viewBox:{x:0,y:0,width:n,height:o}})),i.left,i.left+i.width,t)},yK=function(e,t){var r=e.yAxis,n=e.width,o=e.height,i=e.offset;return uh(ye(yz(yz(yz({},yy.defaultProps),r),{},{ticks:uy(r,!0),viewBox:{x:0,y:0,width:n,height:o}})),i.top,i.top+i.height,t)},yY={horizontal:!0,vertical:!0,stroke:"#ccc",fill:"none",verticalFill:[],horizontalFill:[]};function yZ(e){var t,r,n,o,i,a,c=p4(),s=p3(),f=(0,l.useContext)(pY),p=yz(yz({},e),{},{stroke:null!=(t=e.stroke)?t:yY.stroke,fill:null!=(r=e.fill)?r:yY.fill,horizontal:null!=(n=e.horizontal)?n:yY.horizontal,horizontalFill:null!=(o=e.horizontalFill)?o:yY.horizontalFill,vertical:null!=(i=e.vertical)?i:yY.vertical,verticalFill:null!=(a=e.verticalFill)?a:yY.verticalFill,x:eq(e.x)?e.x:f.left,y:eq(e.y)?e.y:f.top,width:eq(e.width)?e.width:f.width,height:eq(e.height)?e.height:f.height}),d=p.x,h=p.y,y=p.width,m=p.height,v=p.syncWithTicks,b=p.horizontalValues,g=p.verticalValues,x=eG((0,l.useContext)(pV)),w=p2();if(!eq(y)||y<=0||!eq(m)||m<=0||!eq(d)||d!==+d||!eq(h)||h!==+h)return null;var j=p.verticalCoordinatesGenerator||yG,O=p.horizontalCoordinatesGenerator||yK,S=p.horizontalPoints,P=p.verticalPoints;if((!S||!S.length)&&e5()(O)){var A=b&&b.length,k=O({yAxis:w?yz(yz({},w),{},{ticks:A?b:w.ticks}):void 0,width:c,height:s,offset:f},!!A||v);eQ(Array.isArray(k),"horizontalCoordinatesGenerator should return Array but instead it returned [".concat(yR(k),"]")),Array.isArray(k)&&(S=k)}if((!P||!P.length)&&e5()(j)){var E=g&&g.length,N=j({xAxis:x?yz(yz({},x),{},{ticks:E?g:x.ticks}):void 0,width:c,height:s,offset:f},!!E||v);eQ(Array.isArray(N),"verticalCoordinatesGenerator should return Array but instead it returned [".concat(yR(N),"]")),Array.isArray(N)&&(P=N)}return u().createElement("g",{className:"recharts-cartesian-grid"},u().createElement(y$,{fill:p.fill,fillOpacity:p.fillOpacity,x:p.x,y:p.y,width:p.width,height:p.height,ry:p.ry}),u().createElement(yW,yF({},p,{offset:f,horizontalPoints:S,xAxis:x,yAxis:w})),u().createElement(yH,yF({},p,{offset:f,verticalPoints:P,xAxis:x,yAxis:w})),u().createElement(yX,yF({},p,{horizontalPoints:S})),u().createElement(yV,yF({},p,{verticalPoints:P})))}function yJ(){let{data:e,isLoading:t,error:r,refetch:n}=et(()=>J(),{maxRetries:3,initialDelay:500,deps:[Math.floor(Date.now()/12e4)]}),[i,a]=(0,l.useState)([]),[c,s]=(0,l.useState)(!1),u=async()=>{s(!0),await n(),s(!1)};return(0,o.jsx)(ec,{children:(0,o.jsxs)(L.Zp,{className:"shadow-md",children:[(0,o.jsxs)(L.aR,{className:"pb-2 p-5",children:[(0,o.jsx)(L.ZB,{className:"text-xl font-semibold text-primary",children:"Performance Statistics"}),(0,o.jsx)(L.BT,{children:"Database performance over time"})]}),(0,o.jsx)(L.Wu,{className:"p-5",children:t||c?(0,o.jsxs)("div",{className:"space-y-3",children:[(0,o.jsx)(ee.E,{className:"h-[200px] w-full"}),(0,o.jsxs)("div",{className:"grid grid-cols-3 gap-4",children:[(0,o.jsx)(ee.E,{className:"h-16 w-full"}),(0,o.jsx)(ee.E,{className:"h-16 w-full"}),(0,o.jsx)(ee.E,{className:"h-16 w-full"})]})]}):r?(0,o.jsx)(ei,{message:r,onRetry:n}):e&&i.length>0?(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("div",{className:"h-[200px] mt-4",children:(0,o.jsx)(r7,{config:{queryTime:{label:"Avg Query Time (ms)",color:"hsl(var(--chart-1))"},connections:{label:"Connections",color:"hsl(var(--chart-2))"}},children:(0,o.jsxs)(yD,{data:i,margin:{left:12,right:12},children:[(0,o.jsx)(r4,{content:(0,o.jsx)(nt,{})}),(0,o.jsx)(yZ,{vertical:!1}),(0,o.jsx)(yS,{dataKey:"time",tickFormatter:e=>{let t=new Date(e);return`${t.getHours()}:00`}}),(0,o.jsx)(yC,{}),(0,o.jsx)(rt,{content:(0,o.jsx)(ne,{})}),(0,o.jsx)(h4,{type:"monotone",dataKey:"queryTime",stroke:"var(--color-queryTime)",strokeWidth:2,dot:!1}),(0,o.jsx)(h4,{type:"monotone",dataKey:"connections",stroke:"var(--color-connections)",strokeWidth:2,dot:!1})]})})}),(0,o.jsxs)("div",{className:"grid grid-cols-3 gap-4 mt-6",children:[(0,o.jsxs)("div",{className:"border rounded-md p-3 text-center",children:[(0,o.jsxs)("div",{className:"text-2xl font-bold",children:[e.avgQueryTime.toFixed(2),"ms"]}),(0,o.jsx)("div",{className:"text-xs text-muted-foreground",children:"Current Query Time"})]}),(0,o.jsxs)("div",{className:"border rounded-md p-3 text-center",children:[(0,o.jsx)("div",{className:"text-2xl font-bold",children:e.connectionCount}),(0,o.jsx)("div",{className:"text-xs text-muted-foreground",children:"Current Connections"})]}),(0,o.jsxs)("div",{className:"border rounded-md p-3 text-center",children:[(0,o.jsxs)("div",{className:"text-2xl font-bold",children:[e.cacheHitRate.indexHitRate.toFixed(1),"%"]}),(0,o.jsx)("div",{className:"text-xs text-muted-foreground",children:"Cache Hit Rate"})]})]}),e.timestamp&&(0,o.jsxs)("div",{className:"text-xs text-muted-foreground text-center mt-4",children:["Last updated: ",new Date(e.timestamp).toLocaleString()]})]}):(0,o.jsxs)("div",{className:"p-8 text-center text-muted-foreground",children:[(0,o.jsx)(eN.A,{className:"mx-auto h-8 w-8 mb-2 text-muted-foreground/50"}),(0,o.jsx)("p",{children:"No performance data available"})]})}),(0,o.jsx)(L.wL,{className:"p-5",children:(0,o.jsx)(W.r,{actionType:"tertiary",size:"sm",className:"w-full",onClick:u,isLoading:c||t,loadingText:"Refreshing...",icon:(0,o.jsx)(q.A,{className:"h-4 w-4"}),children:"Refresh Statistics"})})]})})}yZ.displayName="CartesianGrid";let yQ=(0,eS.A)("Database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]]);var y0=r(24501);function y1(){return(0,o.jsxs)(L.Zp,{className:"border-none shadow-none",children:[(0,o.jsxs)(L.aR,{className:"px-0 pt-0",children:[(0,o.jsx)(L.ZB,{className:"text-2xl font-bold",children:"Supabase Diagnostics"}),(0,o.jsx)(L.BT,{children:"Monitor and troubleshoot your Supabase database connection"})]}),(0,o.jsx)(L.Wu,{className:"px-0",children:(0,o.jsxs)(P,{defaultValue:"connection",className:"w-full",children:[(0,o.jsxs)(I,{className:"grid w-full grid-cols-4",children:[(0,o.jsxs)(B,{value:"connection",className:"flex items-center",children:[(0,o.jsx)(yQ,{className:"mr-2 h-4 w-4"}),(0,o.jsx)("span",{className:"hidden sm:inline",children:"Connection"})]}),(0,o.jsxs)(B,{value:"health",className:"flex items-center",children:[(0,o.jsx)(y0.A,{className:"mr-2 h-4 w-4"}),(0,o.jsx)("span",{className:"hidden sm:inline",children:"Health"})]}),(0,o.jsxs)(B,{value:"errors",className:"flex items-center",children:[(0,o.jsx)(ea.A,{className:"mr-2 h-4 w-4"}),(0,o.jsx)("span",{className:"hidden sm:inline",children:"Errors"})]}),(0,o.jsxs)(B,{value:"performance",className:"flex items-center",children:[(0,o.jsx)(eN.A,{className:"mr-2 h-4 w-4"}),(0,o.jsx)("span",{className:"hidden sm:inline",children:"Performance"})]})]}),(0,o.jsx)(R,{value:"connection",className:"mt-4",children:(0,o.jsx)(es,{})}),(0,o.jsx)(R,{value:"health",className:"mt-4",children:(0,o.jsx)(eO,{})}),(0,o.jsx)(R,{value:"errors",className:"mt-4",children:(0,o.jsx)(eE,{})}),(0,o.jsx)(R,{value:"performance",className:"mt-4",children:(0,o.jsx)(yJ,{})})]})})]})}function y2(){return(0,o.jsxs)("div",{className:"space-y-6",children:[(0,o.jsx)(i.z,{title:"Admin Dashboard",description:"System administration and diagnostics",icon:a.A}),(0,o.jsxs)(en.Fc,{children:[(0,o.jsx)(c.A,{className:"h-4 w-4"}),(0,o.jsx)(en.XL,{children:"Information"}),(0,o.jsx)(en.TN,{children:"This admin dashboard provides system diagnostics and monitoring tools. No authentication is required for demonstration purposes."})]}),(0,o.jsxs)(P,{defaultValue:"diagnostics",className:"w-full",children:[(0,o.jsxs)(I,{className:"grid w-full grid-cols-1 md:grid-cols-3",children:[(0,o.jsx)(B,{value:"diagnostics",children:"Supabase Diagnostics"}),(0,o.jsx)(B,{value:"system",children:"System Status"}),(0,o.jsx)(B,{value:"docs",children:"Documentation"})]}),(0,o.jsx)(R,{value:"diagnostics",className:"mt-6",children:(0,o.jsx)(y1,{})}),(0,o.jsx)(R,{value:"system",className:"mt-6",children:(0,o.jsxs)(L.Zp,{className:"shadow-md",children:[(0,o.jsxs)(L.aR,{className:"p-5",children:[(0,o.jsx)(L.ZB,{className:"text-xl font-semibold text-primary",children:"System Status"}),(0,o.jsx)(L.BT,{children:"Overview of system components and services"})]}),(0,o.jsx)(L.Wu,{className:"p-5",children:(0,o.jsxs)("div",{className:"grid gap-6 md:grid-cols-2 lg:grid-cols-3",children:[(0,o.jsxs)(L.Zp,{className:"shadow-sm",children:[(0,o.jsxs)(L.aR,{className:"pb-2 p-4",children:[(0,o.jsx)(L.ZB,{className:"text-base font-semibold",children:"Backend API"}),(0,o.jsx)(L.BT,{className:"text-xs",children:"Node.js API Server"})]}),(0,o.jsxs)(L.Wu,{className:"p-4 pt-0",children:[(0,o.jsx)("div",{className:"text-xl font-bold text-green-500",children:"Online"}),(0,o.jsx)("p",{className:"text-xs text-muted-foreground",children:"Version: 1.0.0"})]})]}),(0,o.jsxs)(L.Zp,{className:"shadow-sm",children:[(0,o.jsxs)(L.aR,{className:"pb-2 p-4",children:[(0,o.jsx)(L.ZB,{className:"text-base font-semibold",children:"Frontend"}),(0,o.jsx)(L.BT,{className:"text-xs",children:"Next.js Application"})]}),(0,o.jsxs)(L.Wu,{className:"p-4 pt-0",children:[(0,o.jsx)("div",{className:"text-xl font-bold text-green-500",children:"Online"}),(0,o.jsx)("p",{className:"text-xs text-muted-foreground",children:"Version: 1.0.0"})]})]}),(0,o.jsxs)(L.Zp,{className:"shadow-sm",children:[(0,o.jsxs)(L.aR,{className:"pb-2 p-4",children:[(0,o.jsx)(L.ZB,{className:"text-base font-semibold",children:"Socket Service"}),(0,o.jsx)(L.BT,{className:"text-xs",children:"Real-time Updates"})]}),(0,o.jsxs)(L.Wu,{className:"p-4 pt-0",children:[(0,o.jsx)("div",{className:"text-xl font-bold text-green-500",children:"Online"}),(0,o.jsx)("p",{className:"text-xs text-muted-foreground",children:"Active Connections: 3"})]})]})]})})]})}),(0,o.jsx)(R,{value:"docs",className:"mt-6",children:(0,o.jsxs)(L.Zp,{className:"shadow-md",children:[(0,o.jsxs)(L.aR,{className:"p-5",children:[(0,o.jsx)(L.ZB,{className:"text-xl font-semibold text-primary",children:"Admin Documentation"}),(0,o.jsx)(L.BT,{children:"How to extend and customize the admin dashboard"})]}),(0,o.jsxs)(L.Wu,{className:"space-y-4 p-5",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Extending the Admin Dashboard"}),(0,o.jsx)("p",{className:"text-muted-foreground text-sm",children:"The admin dashboard is designed to be easily extensible. Follow these steps to add new features:"}),(0,o.jsxs)("ol",{className:"list-decimal list-inside mt-2 space-y-1 text-muted-foreground text-sm",children:[(0,o.jsxs)("li",{children:["Create new component(s) in the"," ",(0,o.jsx)("code",{className:"bg-muted px-1 rounded",children:"frontend/src/components/admin"})," ","directory"]}),(0,o.jsx)("li",{children:"Add new tab(s) to the main admin page or to specific diagnostic sections"}),(0,o.jsx)("li",{children:"Create new API endpoints in the backend if needed"}),(0,o.jsx)("li",{children:"Update the admin service with new functions to fetch data"})]})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Supabase Integration"}),(0,o.jsx)("p",{className:"text-muted-foreground text-sm",children:"The Supabase diagnostics section communicates with the backend API, which then connects to Supabase. This follows the established pattern of not connecting directly to Supabase from the frontend."}),(0,o.jsx)("p",{className:"text-muted-foreground mt-2 text-sm",children:"To add more Supabase-related features:"}),(0,o.jsxs)("ol",{className:"list-decimal list-inside mt-2 space-y-1 text-muted-foreground text-sm",children:[(0,o.jsx)("li",{children:"Add new endpoints to the backend API"}),(0,o.jsxs)("li",{children:["Create corresponding service functions in"," ",(0,o.jsx)("code",{className:"bg-muted px-1 rounded",children:"adminService.ts"})]}),(0,o.jsx)("li",{children:"Build new UI components that consume these services"})]})]})]}),(0,o.jsx)(L.wL,{className:"p-5",children:(0,o.jsx)(W.r,{actionType:"tertiary",className:"w-full",icon:(0,o.jsx)(s.A,{className:"h-4 w-4"}),children:"Add New Feature (Placeholder)"})})]})})]})]})}},7112:(e,t,r)=>{Promise.resolve().then(r.bind(r,6788))},7383:(e,t,r)=>{var n=r(67009),o=r(32269),i=r(38428),a=r(55048);e.exports=function(e,t,r){if(!a(r))return!1;var c=typeof t;return("number"==c?!!(o(r)&&i(t,r.length)):"string"==c&&t in r)&&n(r[t],e)}},7651:(e,t,r)=>{var n=r(82038),o=r(52931),i=r(32269);e.exports=function(e){return i(e)?n(e):o(e)}},8336:(e,t,r)=>{var n=r(45803);e.exports=function(e,t){var r=e.__data__;return n(t)?r["string"==typeof t?"string":"hash"]:r.map}},8852:(e,t,r)=>{var n=r(1707);e.exports=function(e){return function(t){return n(t,e)}}},9587:(e,t,r)=>{Promise.resolve().then(r.bind(r,22538))},10090:(e,t,r)=>{var n=r(80458),o=r(89624),i=r(47282),a=i&&i.isTypedArray;e.exports=a?o(a):n},10653:(e,t,r)=>{var n=r(21456),o=r(63979),i=r(7651);e.exports=function(e){return n(e,i,o)}},10663:e=>{e.exports="object"==typeof global&&global&&global.Object===Object&&global},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11117:e=>{"use strict";var t=Object.prototype.hasOwnProperty,r="~";function n(){}function o(e,t,r){this.fn=e,this.context=t,this.once=r||!1}function i(e,t,n,i,a){if("function"!=typeof n)throw TypeError("The listener must be a function");var c=new o(n,i||e,a),s=r?r+t:t;return e._events[s]?e._events[s].fn?e._events[s]=[e._events[s],c]:e._events[s].push(c):(e._events[s]=c,e._eventsCount++),e}function a(e,t){0==--e._eventsCount?e._events=new n:delete e._events[t]}function c(){this._events=new n,this._eventsCount=0}Object.create&&(n.prototype=Object.create(null),new n().__proto__||(r=!1)),c.prototype.eventNames=function(){var e,n,o=[];if(0===this._eventsCount)return o;for(n in e=this._events)t.call(e,n)&&o.push(r?n.slice(1):n);return Object.getOwnPropertySymbols?o.concat(Object.getOwnPropertySymbols(e)):o},c.prototype.listeners=function(e){var t=r?r+e:e,n=this._events[t];if(!n)return[];if(n.fn)return[n.fn];for(var o=0,i=n.length,a=Array(i);o<i;o++)a[o]=n[o].fn;return a},c.prototype.listenerCount=function(e){var t=r?r+e:e,n=this._events[t];return n?n.fn?1:n.length:0},c.prototype.emit=function(e,t,n,o,i,a){var c=r?r+e:e;if(!this._events[c])return!1;var s,l,u=this._events[c],f=arguments.length;if(u.fn){switch(u.once&&this.removeListener(e,u.fn,void 0,!0),f){case 1:return u.fn.call(u.context),!0;case 2:return u.fn.call(u.context,t),!0;case 3:return u.fn.call(u.context,t,n),!0;case 4:return u.fn.call(u.context,t,n,o),!0;case 5:return u.fn.call(u.context,t,n,o,i),!0;case 6:return u.fn.call(u.context,t,n,o,i,a),!0}for(l=1,s=Array(f-1);l<f;l++)s[l-1]=arguments[l];u.fn.apply(u.context,s)}else{var p,d=u.length;for(l=0;l<d;l++)switch(u[l].once&&this.removeListener(e,u[l].fn,void 0,!0),f){case 1:u[l].fn.call(u[l].context);break;case 2:u[l].fn.call(u[l].context,t);break;case 3:u[l].fn.call(u[l].context,t,n);break;case 4:u[l].fn.call(u[l].context,t,n,o);break;default:if(!s)for(p=1,s=Array(f-1);p<f;p++)s[p-1]=arguments[p];u[l].fn.apply(u[l].context,s)}}return!0},c.prototype.on=function(e,t,r){return i(this,e,t,r,!1)},c.prototype.once=function(e,t,r){return i(this,e,t,r,!0)},c.prototype.removeListener=function(e,t,n,o){var i=r?r+e:e;if(!this._events[i])return this;if(!t)return a(this,i),this;var c=this._events[i];if(c.fn)c.fn!==t||o&&!c.once||n&&c.context!==n||a(this,i);else{for(var s=0,l=[],u=c.length;s<u;s++)(c[s].fn!==t||o&&!c[s].once||n&&c[s].context!==n)&&l.push(c[s]);l.length?this._events[i]=1===l.length?l[0]:l:a(this,i)}return this},c.prototype.removeAllListeners=function(e){var t;return e?(t=r?r+e:e,this._events[t]&&a(this,t)):(this._events=new n,this._eventsCount=0),this},c.prototype.off=c.prototype.removeListener,c.prototype.addListener=c.prototype.on,c.prefixed=r,c.EventEmitter=c,e.exports=c},11424:(e,t,r)=>{var n=r(47603);e.exports=r(66400)(n)},11539:(e,t,r)=>{var n=r(37643),o=r(55048),i=r(49227),a=0/0,c=/^[-+]0x[0-9a-f]+$/i,s=/^0b[01]+$/i,l=/^0o[0-7]+$/i,u=parseInt;e.exports=function(e){if("number"==typeof e)return e;if(i(e))return a;if(o(e)){var t="function"==typeof e.valueOf?e.valueOf():e;e=o(t)?t+"":t}if("string"!=typeof e)return 0===e?e:+e;e=n(e);var r=s.test(e);return r||l.test(e)?u(e.slice(2),r?2:8):c.test(e)?a:+e}},11997:e=>{"use strict";e.exports=require("punycode")},12290:e=>{var t=Function.prototype.toString;e.exports=function(e){if(null!=e){try{return t.call(e)}catch(e){}try{return e+""}catch(e){}}return""}},12344:(e,t,r)=>{e.exports=r(65984)()},14675:e=>{e.exports=function(e){return function(){return e}}},15079:(e,t,r)=>{"use strict";r.d(t,{bq:()=>p,eb:()=>m,gC:()=>y,l6:()=>u,yv:()=>f});var n=r(60687),o=r(43210),i=r(22670),a=r(61662),c=r(89743),s=r(58450),l=r(4780);let u=i.bL;i.YJ;let f=i.WT,p=o.forwardRef(({className:e,children:t,...r},o)=>(0,n.jsxs)(i.l9,{ref:o,className:(0,l.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...r,children:[t,(0,n.jsx)(i.In,{asChild:!0,children:(0,n.jsx)(a.A,{className:"h-4 w-4 opacity-50"})})]}));p.displayName=i.l9.displayName;let d=o.forwardRef(({className:e,...t},r)=>(0,n.jsx)(i.PP,{ref:r,className:(0,l.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,n.jsx)(c.A,{className:"h-4 w-4"})}));d.displayName=i.PP.displayName;let h=o.forwardRef(({className:e,...t},r)=>(0,n.jsx)(i.wn,{ref:r,className:(0,l.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,n.jsx)(a.A,{className:"h-4 w-4"})}));h.displayName=i.wn.displayName;let y=o.forwardRef(({className:e,children:t,position:r="popper",...o},a)=>(0,n.jsx)(i.ZL,{children:(0,n.jsxs)(i.UC,{ref:a,className:(0,l.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===r&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:r,...o,children:[(0,n.jsx)(d,{}),(0,n.jsx)(i.LM,{className:(0,l.cn)("p-1","popper"===r&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:t}),(0,n.jsx)(h,{})]})}));y.displayName=i.UC.displayName,o.forwardRef(({className:e,...t},r)=>(0,n.jsx)(i.JU,{ref:r,className:(0,l.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",e),...t})).displayName=i.JU.displayName;let m=o.forwardRef(({className:e,children:t,...r},o)=>(0,n.jsxs)(i.q7,{ref:o,className:(0,l.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...r,children:[(0,n.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,n.jsx)(i.VF,{children:(0,n.jsx)(s.A,{className:"h-4 w-4"})})}),(0,n.jsx)(i.p4,{children:t})]}));m.displayName=i.q7.displayName,o.forwardRef(({className:e,...t},r)=>(0,n.jsx)(i.wv,{ref:r,className:(0,l.cn)("-mx-1 my-1 h-px bg-muted",e),...t})).displayName=i.wv.displayName},15451:(e,t,r)=>{var n=r(29395),o=r(27467);e.exports=function(e){return o(e)&&"[object Arguments]"==n(e)}},15871:(e,t,r)=>{var n=r(36341),o=r(27467);e.exports=function e(t,r,i,a,c){return t===r||(null!=t&&null!=r&&(o(t)||o(r))?n(t,r,i,a,e,c):t!=t&&r!=r)}},15883:(e,t,r)=>{var n=r(2984),o=r(46063),i=r(48169);e.exports=function(e){return e&&e.length?n(e,i,o):void 0}},15909:(e,t,r)=>{var n=r(87506),o=r(66930),i=r(658);e.exports=function(){this.size=0,this.__data__={hash:new n,map:new(i||o),string:new n}}},16854:e=>{e.exports=function(e){return this.__data__.has(e)}},17518:(e,t,r)=>{var n=r(21367),o=r(1707),i=r(22),a=r(54765),c=r(43378),s=r(89624),l=r(65727),u=r(48169),f=r(40542);e.exports=function(e,t,r){t=t.length?n(t,function(e){return f(e)?function(t){return o(t,1===e.length?e[0]:e)}:e}):[u];var p=-1;return t=n(t,s(i)),c(a(e,function(e,r,o){return{criteria:n(t,function(t){return t(e)}),index:++p,value:e}}),function(e,t){return l(e,t,r)})}},17830:(e,t,r)=>{e.exports=r(41547)(r(85718),"WeakMap")},18234:(e,t,r)=>{var n=r(91290),o=r(22),i=r(84482),a=Math.max;e.exports=function(e,t,r){var c=null==e?0:e.length;if(!c)return -1;var s=null==r?0:i(r);return s<0&&(s=a(c+s,0)),n(e,o(t,3),s)}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19976:(e,t,r)=>{var n=r(8336);e.exports=function(e,t){var r=n(this,e),o=r.size;return r.set(e,t),this.size+=+(r.size!=o),this}},20540:(e,t,r)=>{var n=r(55048),o=r(70151),i=r(11539),a=Math.max,c=Math.min;e.exports=function(e,t,r){var s,l,u,f,p,d,h=0,y=!1,m=!1,v=!0;if("function"!=typeof e)throw TypeError("Expected a function");function b(t){var r=s,n=l;return s=l=void 0,h=t,f=e.apply(n,r)}function g(e){var r=e-d,n=e-h;return void 0===d||r>=t||r<0||m&&n>=u}function x(){var e,r,n,i=o();if(g(i))return w(i);p=setTimeout(x,(e=i-d,r=i-h,n=t-e,m?c(n,u-r):n))}function w(e){return(p=void 0,v&&s)?b(e):(s=l=void 0,f)}function j(){var e,r=o(),n=g(r);if(s=arguments,l=this,d=r,n){if(void 0===p)return h=e=d,p=setTimeout(x,t),y?b(e):f;if(m)return clearTimeout(p),p=setTimeout(x,t),b(d)}return void 0===p&&(p=setTimeout(x,t)),f}return t=i(t)||0,n(r)&&(y=!!r.leading,u=(m="maxWait"in r)?a(i(r.maxWait)||0,t):u,v="trailing"in r?!!r.trailing:v),j.cancel=function(){void 0!==p&&clearTimeout(p),h=0,s=d=l=p=void 0},j.flush=function(){return void 0===p?f:w(o())},j}},20623:(e,t,r)=>{var n=r(15871),o=r(40491),i=r(2896),a=r(67619),c=r(34883),s=r(41132),l=r(46436);e.exports=function(e,t){return a(e)&&c(t)?s(l(e),t):function(r){var a=o(r,e);return void 0===a&&a===t?i(r,e):n(t,a,3)}}},21367:e=>{e.exports=function(e,t){for(var r=-1,n=null==e?0:e.length,o=Array(n);++r<n;)o[r]=t(e[r],r,e);return o}},21456:(e,t,r)=>{var n=r(41693),o=r(40542);e.exports=function(e,t,r){var i=t(e);return o(e)?i:n(i,r(e))}},21592:(e,t,r)=>{var n=r(42205),o=r(61837);e.exports=function(e,t){return n(o(e,t),1)}},21630:(e,t,r)=>{var n=r(10653),o=Object.prototype.hasOwnProperty;e.exports=function(e,t,r,i,a,c){var s=1&r,l=n(e),u=l.length;if(u!=n(t).length&&!s)return!1;for(var f=u;f--;){var p=l[f];if(!(s?p in t:o.call(t,p)))return!1}var d=c.get(e),h=c.get(t);if(d&&h)return d==t&&h==e;var y=!0;c.set(e,t),c.set(t,e);for(var m=s;++f<u;){var v=e[p=l[f]],b=t[p];if(i)var g=s?i(b,v,p,t,e,c):i(v,b,p,e,t,c);if(!(void 0===g?v===b||a(v,b,r,i,c):g)){y=!1;break}m||(m="constructor"==p)}if(y&&!m){var x=e.constructor,w=t.constructor;x!=w&&"constructor"in e&&"constructor"in t&&!("function"==typeof x&&x instanceof x&&"function"==typeof w&&w instanceof w)&&(y=!1)}return c.delete(e),c.delete(t),y}},22538:(e,t,r)=>{"use strict";r.d(t,{Breadcrumb:()=>o,BreadcrumbItem:()=>a,BreadcrumbLink:()=>c,BreadcrumbList:()=>i,BreadcrumbPage:()=>s,BreadcrumbSeparator:()=>l});var n=r(12907);let o=(0,n.registerClientReference)(function(){throw Error("Attempted to call Breadcrumb() from the server but Breadcrumb is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\components\\ui\\breadcrumb.tsx","Breadcrumb"),i=(0,n.registerClientReference)(function(){throw Error("Attempted to call BreadcrumbList() from the server but BreadcrumbList is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\components\\ui\\breadcrumb.tsx","BreadcrumbList"),a=(0,n.registerClientReference)(function(){throw Error("Attempted to call BreadcrumbItem() from the server but BreadcrumbItem is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\components\\ui\\breadcrumb.tsx","BreadcrumbItem"),c=(0,n.registerClientReference)(function(){throw Error("Attempted to call BreadcrumbLink() from the server but BreadcrumbLink is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\components\\ui\\breadcrumb.tsx","BreadcrumbLink"),s=(0,n.registerClientReference)(function(){throw Error("Attempted to call BreadcrumbPage() from the server but BreadcrumbPage is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\components\\ui\\breadcrumb.tsx","BreadcrumbPage"),l=(0,n.registerClientReference)(function(){throw Error("Attempted to call BreadcrumbSeparator() from the server but BreadcrumbSeparator is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\components\\ui\\breadcrumb.tsx","BreadcrumbSeparator");(0,n.registerClientReference)(function(){throw Error("Attempted to call BreadcrumbEllipsis() from the server but BreadcrumbEllipsis is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\components\\ui\\breadcrumb.tsx","BreadcrumbEllipsis")},22964:(e,t,r)=>{e.exports=r(23729)(r(18234))},23729:(e,t,r)=>{var n=r(22),o=r(32269),i=r(7651);e.exports=function(e){return function(t,r,a){var c=Object(t);if(!o(t)){var s=n(r,3);t=i(t),r=function(e){return s(c[e],e,c)}}var l=e(t,r,a);return l>-1?c[s?t[l]:l]:void 0}}},24501:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(82614).A)("Gauge",[["path",{d:"m12 14 4-4",key:"9kzdfg"}],["path",{d:"M3.34 19a10 10 0 1 1 17.32 0",key:"19p75a"}]])},25118:e=>{e.exports=function(e){return this.__data__.has(e)}},27006:(e,t,r)=>{var n=r(46328),o=r(99525),i=r(58276);e.exports=function(e,t,r,a,c,s){var l=1&r,u=e.length,f=t.length;if(u!=f&&!(l&&f>u))return!1;var p=s.get(e),d=s.get(t);if(p&&d)return p==t&&d==e;var h=-1,y=!0,m=2&r?new n:void 0;for(s.set(e,t),s.set(t,e);++h<u;){var v=e[h],b=t[h];if(a)var g=l?a(b,v,h,t,e,s):a(v,b,h,e,t,s);if(void 0!==g){if(g)continue;y=!1;break}if(m){if(!o(t,function(e,t){if(!i(m,t)&&(v===e||c(v,e,r,a,s)))return m.push(t)})){y=!1;break}}else if(!(v===b||c(v,b,r,a,s))){y=!1;break}}return s.delete(e),s.delete(t),y}},27467:e=>{e.exports=function(e){return null!=e&&"object"==typeof e}},27669:e=>{e.exports=function(){this.__data__=[],this.size=0}},27805:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(82614).A)("Activity",[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]])},27910:e=>{"use strict";e.exports=require("stream")},28837:(e,t,r)=>{var n=r(57797),o=Array.prototype.splice;e.exports=function(e){var t=this.__data__,r=n(t,e);return!(r<0)&&(r==t.length-1?t.pop():o.call(t,r,1),--this.size,!0)}},28977:(e,t,r)=>{var n=r(11539),o=1/0;e.exports=function(e){return e?(e=n(e))===o||e===-o?(e<0?-1:1)*17976931348623157e292:e==e?e:0:0===e?e:0}},29205:(e,t,r)=>{var n=r(8336);e.exports=function(e){var t=n(this,e).delete(e);return this.size-=!!t,t}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29395:(e,t,r)=>{var n=r(79474),o=r(70222),i=r(84713),a=n?n.toStringTag:void 0;e.exports=function(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":a&&a in Object(e)?o(e):i(e)}},29508:(e,t,r)=>{var n=r(8336);e.exports=function(e){return n(this,e).get(e)}},29632:(e,t,r)=>{"use strict";e.exports=r(97668)},30316:(e,t,r)=>{var n=r(67554);e.exports=function(e,t){var r=!0;return n(e,function(e,n,o){return r=!!t(e,n,o)}),r}},30401:(e,t,r)=>{e.exports=r(41547)(r(85718),"Promise")},30854:(e,t,r)=>{var n=r(66930),o=r(658),i=r(95746);e.exports=function(e,t){var r=this.__data__;if(r instanceof n){var a=r.__data__;if(!o||a.length<199)return a.push([e,t]),this.size=++r.size,this;r=this.__data__=new i(a)}return r.set(e,t),this.size=r.size,this}},32269:(e,t,r)=>{var n=r(5231),o=r(69619);e.exports=function(e){return null!=e&&o(e.length)&&!n(e)}},33873:e=>{"use strict";e.exports=require("path")},34117:e=>{var t=RegExp("[\\u200d\ud800-\udfff\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff\\ufe0e\\ufe0f]");e.exports=function(e){return t.test(e)}},34452:e=>{"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},34631:e=>{"use strict";e.exports=require("tls")},34746:e=>{e.exports=function(e){return this.__data__.get(e)}},34772:(e,t,r)=>{e.exports=r(41547)(r(85718),"Set")},34883:(e,t,r)=>{var n=r(55048);e.exports=function(e){return e==e&&!n(e)}},34990:(e,t,r)=>{e.exports=r(87321)()},35142:(e,t,r)=>{var n=r(40542),o=r(67619),i=r(51449),a=r(42403);e.exports=function(e,t){return n(e)?e:o(e,t)?[e]:i(a(e))}},35163:(e,t,r)=>{var n=r(15451),o=r(27467),i=Object.prototype,a=i.hasOwnProperty,c=i.propertyIsEnumerable;e.exports=n(function(){return arguments}())?n:function(e){return o(e)&&a.call(e,"callee")&&!c.call(e,"callee")}},35697:(e,t,r)=>{var n=r(79474),o=r(4999),i=r(67009),a=r(27006),c=r(59774),s=r(2408),l=n?n.prototype:void 0,u=l?l.valueOf:void 0;e.exports=function(e,t,r,n,l,f,p){switch(r){case"[object DataView]":if(e.byteLength!=t.byteLength||e.byteOffset!=t.byteOffset)break;e=e.buffer,t=t.buffer;case"[object ArrayBuffer]":if(e.byteLength!=t.byteLength||!f(new o(e),new o(t)))break;return!0;case"[object Boolean]":case"[object Date]":case"[object Number]":return i(+e,+t);case"[object Error]":return e.name==t.name&&e.message==t.message;case"[object RegExp]":case"[object String]":return e==t+"";case"[object Map]":var d=c;case"[object Set]":var h=1&n;if(d||(d=s),e.size!=t.size&&!h)break;var y=p.get(e);if(y)return y==t;n|=2,p.set(e,t);var m=a(d(e),d(t),n,l,f,p);return p.delete(e),m;case"[object Symbol]":if(u)return u.call(e)==u.call(t)}return!1}},35800:(e,t,r)=>{var n=r(57797);e.exports=function(e){return n(this.__data__,e)>-1}},36315:(e,t,r)=>{var n=r(22),o=r(92662);e.exports=function(e,t){return e&&e.length?o(e,n(t,2)):[]}},36341:(e,t,r)=>{var n=r(67200),o=r(27006),i=r(35697),a=r(21630),c=r(1566),s=r(40542),l=r(80329),u=r(10090),f="[object Arguments]",p="[object Array]",d="[object Object]",h=Object.prototype.hasOwnProperty;e.exports=function(e,t,r,y,m,v){var b=s(e),g=s(t),x=b?p:c(e),w=g?p:c(t);x=x==f?d:x,w=w==f?d:w;var j=x==d,O=w==d,S=x==w;if(S&&l(e)){if(!l(t))return!1;b=!0,j=!1}if(S&&!j)return v||(v=new n),b||u(e)?o(e,t,r,y,m,v):i(e,t,x,r,y,m,v);if(!(1&r)){var P=j&&h.call(e,"__wrapped__"),A=O&&h.call(t,"__wrapped__");if(P||A){var k=P?e.value():e,E=A?t.value():t;return v||(v=new n),m(k,E,r,y,v)}}return!!S&&(v||(v=new n),a(e,t,r,y,m,v))}},36959:e=>{e.exports=function(){}},37456:e=>{e.exports=function(e){return null==e}},37575:(e,t,r)=>{var n=r(66930);e.exports=function(){this.__data__=new n,this.size=0}},37643:(e,t,r)=>{var n=r(6053),o=/^\s+/;e.exports=function(e){return e?e.slice(0,n(e)+1).replace(o,""):e}},38404:(e,t,r)=>{var n=r(29395),o=r(65932),i=r(27467),a=Object.prototype,c=Function.prototype.toString,s=a.hasOwnProperty,l=c.call(Object);e.exports=function(e){if(!i(e)||"[object Object]"!=n(e))return!1;var t=o(e);if(null===t)return!0;var r=s.call(t,"constructor")&&t.constructor;return"function"==typeof r&&r instanceof r&&c.call(r)==l}},38428:e=>{var t=/^(?:0|[1-9]\d*)$/;e.exports=function(e,r){var n=typeof e;return!!(r=null==r?0x1fffffffffffff:r)&&("number"==n||"symbol"!=n&&t.test(e))&&e>-1&&e%1==0&&e<r}},39672:(e,t,r)=>{var n=r(58141);e.exports=function(e,t){var r=this.__data__;return this.size+=+!this.has(e),r[e]=n&&void 0===t?"__lodash_hash_undefined__":t,this}},39774:e=>{e.exports=function(e){return e!=e}},40491:(e,t,r)=>{var n=r(1707);e.exports=function(e,t,r){var o=null==e?void 0:n(e,t);return void 0===o?r:o}},40542:e=>{e.exports=Array.isArray},41011:(e,t,r)=>{var n=r(41353);e.exports=function(e,t,r){var o=e.length;return r=void 0===r?o:r,!t&&r>=o?e:n(e,t,r)}},41132:e=>{e.exports=function(e,t){return function(r){return null!=r&&r[e]===t&&(void 0!==t||e in Object(r))}}},41157:(e,t,r)=>{var n=r(91928);e.exports=function(e,t,r){"__proto__"==t&&n?n(e,t,{configurable:!0,enumerable:!0,value:r,writable:!0}):e[t]=r}},41353:e=>{e.exports=function(e,t,r){var n=-1,o=e.length;t<0&&(t=-t>o?0:o+t),(r=r>o?o:r)<0&&(r+=o),o=t>r?0:r-t>>>0,t>>>=0;for(var i=Array(o);++n<o;)i[n]=e[n+t];return i}},41547:(e,t,r)=>{var n=r(61548),o=r(90851);e.exports=function(e,t){var r=o(e,t);return n(r)?r:void 0}},41693:e=>{e.exports=function(e,t){for(var r=-1,n=t.length,o=e.length;++r<n;)e[o+r]=t[r];return e}},42082:e=>{e.exports=function(e){return function(t){return null==t?void 0:t[e]}}},42205:(e,t,r)=>{var n=r(41693),o=r(85450);e.exports=function e(t,r,i,a,c){var s=-1,l=t.length;for(i||(i=o),c||(c=[]);++s<l;){var u=t[s];r>0&&i(u)?r>1?e(u,r-1,i,a,c):n(c,u):a||(c[c.length]=u)}return c}},42403:(e,t,r)=>{var n=r(80195);e.exports=function(e){return null==e?"":n(e)}},42692:(e,t,r)=>{"use strict";r.d(t,{F:()=>c});var n=r(60687),o=r(43210),i=r(68123),a=r(4780);let c=o.forwardRef(({className:e,children:t,...r},o)=>(0,n.jsxs)(i.bL,{ref:o,className:(0,a.cn)("relative overflow-hidden",e),...r,children:[(0,n.jsx)(i.LM,{className:"h-full w-full rounded-[inherit]",children:t}),(0,n.jsx)(s,{}),(0,n.jsx)(i.OK,{})]}));c.displayName=i.bL.displayName;let s=o.forwardRef(({className:e,orientation:t="vertical",...r},o)=>(0,n.jsx)(i.VM,{ref:o,orientation:t,className:(0,a.cn)("flex touch-none select-none transition-colors","vertical"===t&&"h-full w-2.5 border-l border-l-transparent p-[1px]","horizontal"===t&&"h-2.5 flex-col border-t border-t-transparent p-[1px]",e),...r,children:(0,n.jsx)(i.lr,{className:"relative flex-1 rounded-full bg-border"})}));s.displayName=i.VM.displayName},43378:e=>{e.exports=function(e,t){var r=e.length;for(e.sort(t);r--;)e[r]=e[r].value;return e}},43560:(e,t,r)=>{Promise.resolve().then(r.bind(r,1132))},45058:(e,t,r)=>{var n=r(42082),o=r(8852),i=r(67619),a=r(46436);e.exports=function(e){return i(e)?n(a(e)):o(e)}},45603:(e,t,r)=>{var n=r(20540),o=r(55048);e.exports=function(e,t,r){var i=!0,a=!0;if("function"!=typeof e)throw TypeError("Expected a function");return o(r)&&(i="leading"in r?!!r.leading:i,a="trailing"in r?!!r.trailing:a),n(e,t,{leading:i,maxWait:t,trailing:a})}},45803:e=>{e.exports=function(e){var t=typeof e;return"string"==t||"number"==t||"symbol"==t||"boolean"==t?"__proto__"!==e:null===e}},46063:e=>{e.exports=function(e,t){return e<t}},46229:(e,t,r)=>{var n=r(48169),o=r(66354),i=r(11424);e.exports=function(e,t){return i(o(e,t,n),e+"")}},46328:(e,t,r)=>{var n=r(95746),o=r(89185),i=r(16854);function a(e){var t=-1,r=null==e?0:e.length;for(this.__data__=new n;++t<r;)this.add(e[t])}a.prototype.add=a.prototype.push=o,a.prototype.has=i,e.exports=a},46436:(e,t,r)=>{var n=r(49227),o=1/0;e.exports=function(e){if("string"==typeof e||n(e))return e;var t=e+"";return"0"==t&&1/e==-o?"-0":t}},46539:(e,t,r)=>{Promise.resolve().then(r.bind(r,70640))},47212:(e,t,r)=>{var n=r(87270),o=r(30316),i=r(22),a=r(40542),c=r(7383);e.exports=function(e,t,r){var s=a(e)?n:o;return r&&c(e,t,r)&&(t=void 0),s(e,i(t,3))}},47282:(e,t,r)=>{e=r.nmd(e);var n=r(10663),o=t&&!t.nodeType&&t,i=o&&e&&!e.nodeType&&e,a=i&&i.exports===o&&n.process,c=function(){try{var e=i&&i.require&&i.require("util").types;if(e)return e;return a&&a.binding&&a.binding("util")}catch(e){}}();e.exports=c},47603:(e,t,r)=>{var n=r(14675),o=r(91928),i=r(48169);e.exports=o?function(e,t){return o(e,"toString",{configurable:!0,enumerable:!1,value:n(t),writable:!0})}:i},48041:(e,t,r)=>{"use strict";r.d(t,{z:()=>o});var n=r(60687);function o({title:e,description:t,icon:r,children:o}){return(0,n.jsxs)("div",{className:"mb-6 pb-4 border-b border-border/50 flex justify-between items-center",children:[(0,n.jsxs)("div",{children:[(0,n.jsxs)("div",{className:"flex items-center gap-3",children:[r&&(0,n.jsx)(r,{className:"h-8 w-8 text-primary"}),(0,n.jsx)("h1",{className:"text-3xl font-bold tracking-tight text-foreground",children:e})]}),t&&(0,n.jsx)("p",{className:"text-muted-foreground mt-1",children:t})]}),o&&(0,n.jsx)("div",{className:"flex items-center gap-2",children:o})]})}r(43210)},48169:e=>{e.exports=function(e){return e}},48385:e=>{var t="\ud800-\udfff",r="[\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff]",n="\ud83c[\udffb-\udfff]",o="[^"+t+"]",i="(?:\ud83c[\udde6-\uddff]){2}",a="[\ud800-\udbff][\udc00-\udfff]",c="(?:"+r+"|"+n+")?",s="[\\ufe0e\\ufe0f]?",l="(?:\\u200d(?:"+[o,i,a].join("|")+")"+s+c+")*",u=RegExp(n+"(?="+n+")|"+("(?:"+[o+r+"?",r,i,a,"["+t+"]"].join("|"))+")"+(s+c+l),"g");e.exports=function(e){return e.match(u)||[]}},49227:(e,t,r)=>{var n=r(29395),o=r(27467);e.exports=function(e){return"symbol"==typeof e||o(e)&&"[object Symbol]"==n(e)}},51449:(e,t,r)=>{var n=r(85745),o=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,i=/\\(\\)?/g;e.exports=n(function(e){var t=[];return 46===e.charCodeAt(0)&&t.push(""),e.replace(o,function(e,r,n,o){t.push(n?o.replace(i,"$1"):r||e)}),t})},52599:e=>{e.exports=function(e,t){for(var r=-1,n=null==e?0:e.length,o=0,i=[];++r<n;){var a=e[r];t(a,r,e)&&(i[o++]=a)}return i}},52823:(e,t,r)=>{var n=r(85406),o=function(){var e=/[^.]+$/.exec(n&&n.keys&&n.keys.IE_PROTO||"");return e?"Symbol(src)_1."+e:""}();e.exports=function(e){return!!o&&o in e}},52931:(e,t,r)=>{var n=r(77834),o=r(89605),i=Object.prototype.hasOwnProperty;e.exports=function(e){if(!n(e))return o(e);var t=[];for(var r in Object(e))i.call(e,r)&&"constructor"!=r&&t.push(r);return t}},54765:(e,t,r)=>{var n=r(67554),o=r(32269);e.exports=function(e,t){var r=-1,i=o(e)?Array(e.length):[];return n(e,function(e,n,o){i[++r]=t(e,n,o)}),i}},55048:e=>{e.exports=function(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56506:(e,t,r)=>{var n=r(32269);e.exports=function(e,t){return function(r,o){if(null==r)return r;if(!n(r))return e(r,o);for(var i=r.length,a=t?i:-1,c=Object(r);(t?a--:++a<i)&&!1!==o(c[a],a,c););return r}}},57202:e=>{e.exports=function(e,t){for(var r=-1,n=Array(e);++r<e;)n[r]=t(r);return n}},57797:(e,t,r)=>{var n=r(67009);e.exports=function(e,t){for(var r=e.length;r--;)if(n(e[r][0],t))return r;return -1}},58141:(e,t,r)=>{e.exports=r(41547)(Object,"create")},58276:e=>{e.exports=function(e,t){return e.has(t)}},58744:(e,t,r)=>{var n=r(57797);e.exports=function(e,t){var r=this.__data__,o=n(r,e);return o<0?(++this.size,r.push([e,t])):r[o][1]=t,this}},59467:(e,t,r)=>{var n=r(35142),o=r(35163),i=r(40542),a=r(38428),c=r(69619),s=r(46436);e.exports=function(e,t,r){t=n(t,e);for(var l=-1,u=t.length,f=!1;++l<u;){var p=s(t[l]);if(!(f=null!=e&&r(e,p)))break;e=e[p]}return f||++l!=u?f:!!(u=null==e?0:e.length)&&c(u)&&a(p,u)&&(i(e)||o(e))}},59774:e=>{e.exports=function(e){var t=-1,r=Array(e.size);return e.forEach(function(e,n){r[++t]=[n,e]}),r}},61320:(e,t,r)=>{var n=r(8336);e.exports=function(e){return n(this,e).has(e)}},61548:(e,t,r)=>{var n=r(5231),o=r(52823),i=r(55048),a=r(12290),c=/^\[object .+?Constructor\]$/,s=Object.prototype,l=Function.prototype.toString,u=s.hasOwnProperty,f=RegExp("^"+l.call(u).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");e.exports=function(e){return!(!i(e)||o(e))&&(n(e)?f:c).test(a(e))}},61837:(e,t,r)=>{var n=r(21367),o=r(22),i=r(54765),a=r(40542);e.exports=function(e,t){return(a(e)?n:i)(e,o(t,3))}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63866:(e,t,r)=>{var n=r(29395),o=r(40542),i=r(27467);e.exports=function(e){return"string"==typeof e||!o(e)&&i(e)&&"[object String]"==n(e)}},63979:(e,t,r)=>{var n=r(52599),o=r(6330),i=Object.prototype.propertyIsEnumerable,a=Object.getOwnPropertySymbols;e.exports=a?function(e){return null==e?[]:n(a(e=Object(e)),function(t){return i.call(e,t)})}:o},65662:e=>{e.exports=function(e,t){return function(r){return e(t(r))}}},65727:(e,t,r)=>{var n=r(81957);e.exports=function(e,t,r){for(var o=-1,i=e.criteria,a=t.criteria,c=i.length,s=r.length;++o<c;){var l=n(i[o],a[o]);if(l){if(o>=s)return l;return l*("desc"==r[o]?-1:1)}}return e.index-t.index}},65932:(e,t,r)=>{e.exports=r(65662)(Object.getPrototypeOf,Object)},65984:e=>{e.exports=function(e){return function(t,r,n){for(var o=-1,i=Object(t),a=n(t),c=a.length;c--;){var s=a[e?c:++o];if(!1===r(i[s],s,i))break}return t}}},66354:(e,t,r)=>{var n=r(85244),o=Math.max;e.exports=function(e,t,r){return t=o(void 0===t?e.length-1:t,0),function(){for(var i=arguments,a=-1,c=o(i.length-t,0),s=Array(c);++a<c;)s[a]=i[t+a];a=-1;for(var l=Array(t+1);++a<t;)l[a]=i[a];return l[t]=r(s),n(e,this,l)}}},66400:e=>{var t=Date.now;e.exports=function(e){var r=0,n=0;return function(){var o=t(),i=16-(o-n);if(n=o,i>0){if(++r>=800)return arguments[0]}else r=0;return e.apply(void 0,arguments)}}},66713:(e,t,r)=>{var n=r(3105),o=r(34117),i=r(48385);e.exports=function(e){return o(e)?i(e):n(e)}},66837:(e,t,r)=>{var n=r(58141);e.exports=function(){this.__data__=n?n(null):{},this.size=0}},66930:(e,t,r)=>{var n=r(27669),o=r(28837),i=r(94388),a=r(35800),c=r(58744);function s(e){var t=-1,r=null==e?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}s.prototype.clear=n,s.prototype.delete=o,s.prototype.get=i,s.prototype.has=a,s.prototype.set=c,e.exports=s},67009:e=>{e.exports=function(e,t){return e===t||e!=e&&t!=t}},67200:(e,t,r)=>{var n=r(66930),o=r(37575),i=r(75411),a=r(34746),c=r(25118),s=r(30854);function l(e){var t=this.__data__=new n(e);this.size=t.size}l.prototype.clear=o,l.prototype.delete=i,l.prototype.get=a,l.prototype.has=c,l.prototype.set=s,e.exports=l},67367:(e,t,r)=>{var n=r(99525),o=r(22),i=r(75847),a=r(40542),c=r(7383);e.exports=function(e,t,r){var s=a(e)?n:i;return r&&c(e,t,r)&&(t=void 0),s(e,o(t,3))}},67554:(e,t,r)=>{var n=r(99114);e.exports=r(56506)(n)},67619:(e,t,r)=>{var n=r(40542),o=r(49227),i=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,a=/^\w*$/;e.exports=function(e,t){if(n(e))return!1;var r=typeof e;return!!("number"==r||"symbol"==r||"boolean"==r||null==e||o(e))||a.test(e)||!i.test(e)||null!=t&&e in Object(t)}},68752:(e,t,r)=>{"use strict";r.d(t,{r:()=>l});var n=r(60687),o=r(43210),i=r.n(o),a=r(29523),c=r(11516),s=r(4780);let l=i().forwardRef(({actionType:e="primary",icon:t,isLoading:r=!1,loadingText:o,className:i,children:l,disabled:u,asChild:f=!1,...p},d)=>{let{variant:h,className:y}={primary:{variant:"default",className:"shadow-md"},secondary:{variant:"secondary",className:""},tertiary:{variant:"outline",className:""},danger:{variant:"destructive",className:"shadow-md"}}[e];return(0,n.jsx)(a.$,{ref:d,variant:h,className:(0,s.cn)(y,i),disabled:r||u,asChild:f,...p,children:r?(0,n.jsxs)("span",{className:"inline-flex items-center",children:[" ",(0,n.jsx)(c.A,{className:"mr-2 h-4 w-4 animate-spin"}),o||l]}):(0,n.jsxs)("span",{className:"inline-flex items-center",children:[" ",t&&(0,n.jsx)("span",{className:"mr-2",children:t}),l]})})});l.displayName="ActionButton"},69433:(e,t,r)=>{e.exports=r(5566)("toUpperCase")},69619:e=>{e.exports=function(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=0x1fffffffffffff}},69691:(e,t,r)=>{var n=r(41157),o=r(99114),i=r(22);e.exports=function(e,t){var r={};return t=i(t,3),o(e,function(e,o,i){n(r,o,t(e,o,i))}),r}},69795:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(82614).A)("Ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},70151:(e,t,r)=>{var n=r(85718);e.exports=function(){return n.Date.now()}},70222:(e,t,r)=>{var n=r(79474),o=Object.prototype,i=o.hasOwnProperty,a=o.toString,c=n?n.toStringTag:void 0;e.exports=function(e){var t=i.call(e,c),r=e[c];try{e[c]=void 0;var n=!0}catch(e){}var o=a.call(e);return n&&(t?e[c]=r:delete e[c]),o}},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});var n=r(31658);let o=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,n.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},70640:(e,t,r)=>{"use strict";r.d(t,{Breadcrumb:()=>s,BreadcrumbItem:()=>u,BreadcrumbLink:()=>f,BreadcrumbList:()=>l,BreadcrumbPage:()=>p,BreadcrumbSeparator:()=>d});var n=r(60687),o=r(43210),i=r(74158),a=(r(69795),r(8730)),c=r(4780);let s=o.forwardRef(({className:e,...t},r)=>(0,n.jsx)("nav",{ref:r,"aria-label":"breadcrumb",className:(0,c.cn)("flex flex-wrap items-center gap-1.5 break-words text-sm text-muted-foreground",e),...t}));s.displayName="Breadcrumb";let l=o.forwardRef(({className:e,...t},r)=>(0,n.jsx)("ol",{ref:r,className:(0,c.cn)("flex flex-wrap items-center gap-1.5 break-words text-sm",e),...t}));l.displayName="BreadcrumbList";let u=o.forwardRef(({className:e,...t},r)=>(0,n.jsx)("li",{ref:r,className:(0,c.cn)("inline-flex items-center gap-1.5",e),...t}));u.displayName="BreadcrumbItem";let f=o.forwardRef(({asChild:e,className:t,...r},o)=>{let i=e?a.DX:"a";return(0,n.jsx)(i,{ref:o,className:(0,c.cn)("transition-colors hover:text-foreground",t),...r})});f.displayName="BreadcrumbLink";let p=o.forwardRef(({className:e,...t},r)=>(0,n.jsx)("span",{ref:r,role:"link","aria-current":"page","aria-disabled":"true",className:(0,c.cn)("font-normal text-foreground",e),...t}));p.displayName="BreadcrumbPage";let d=({children:e,className:t,...r})=>(0,n.jsx)("span",{role:"presentation","aria-hidden":"true",className:(0,c.cn)("[&>svg]:size-3.5",t),...r,children:e??(0,n.jsx)(i.A,{className:"h-4 w-4"})});d.displayName="BreadcrumbSeparator"},71960:e=>{e.exports=function(e,t,r){for(var n=-1,o=null==e?0:e.length;++n<o;)if(r(t,e[n]))return!0;return!1}},71967:(e,t,r)=>{var n=r(15871);e.exports=function(e,t){return n(e,t)}},74075:e=>{"use strict";e.exports=require("zlib")},74610:e=>{e.exports=function(e,t,r){for(var n=r-1,o=e.length;++n<o;)if(e[n]===t)return n;return -1}},75254:(e,t,r)=>{var n=r(78418),o=r(93311),i=r(41132);e.exports=function(e){var t=o(e);return 1==t.length&&t[0][2]?i(t[0][0],t[0][1]):function(r){return r===e||n(r,e,t)}}},75411:e=>{e.exports=function(e){var t=this.__data__,r=t.delete(e);return this.size=t.size,r}},75847:(e,t,r)=>{var n=r(67554);e.exports=function(e,t){var r;return n(e,function(e,n,o){return!(r=t(e,n,o))}),!!r}},77368:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(82614).A)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},77822:(e,t,r)=>{var n=r(93490);e.exports=function(e){return n(e)&&e!=+e}},77834:e=>{var t=Object.prototype;e.exports=function(e){var r=e&&e.constructor;return e===("function"==typeof r&&r.prototype||t)}},78418:(e,t,r)=>{var n=r(67200),o=r(15871);e.exports=function(e,t,r,i){var a=r.length,c=a,s=!i;if(null==e)return!c;for(e=Object(e);a--;){var l=r[a];if(s&&l[2]?l[1]!==e[l[0]]:!(l[0]in e))return!1}for(;++a<c;){var u=(l=r[a])[0],f=e[u],p=l[1];if(s&&l[2]){if(void 0===f&&!(u in e))return!1}else{var d=new n;if(i)var h=i(f,p,u,e,t,d);if(!(void 0===h?o(p,f,3,i,d):h))return!1}}return!0}},79428:e=>{"use strict";e.exports=require("buffer")},79474:(e,t,r)=>{e.exports=r(85718).Symbol},79551:e=>{"use strict";e.exports=require("url")},80195:(e,t,r)=>{var n=r(79474),o=r(21367),i=r(40542),a=r(49227),c=1/0,s=n?n.prototype:void 0,l=s?s.toString:void 0;e.exports=function e(t){if("string"==typeof t)return t;if(i(t))return o(t,e)+"";if(a(t))return l?l.call(t):"";var r=t+"";return"0"==r&&1/t==-c?"-0":r}},80329:(e,t,r)=>{e=r.nmd(e);var n=r(85718),o=r(1944),i=t&&!t.nodeType&&t,a=i&&e&&!e.nodeType&&e,c=a&&a.exports===i?n.Buffer:void 0,s=c?c.isBuffer:void 0;e.exports=s||o},80458:(e,t,r)=>{var n=r(29395),o=r(69619),i=r(27467),a={};a["[object Float32Array]"]=a["[object Float64Array]"]=a["[object Int8Array]"]=a["[object Int16Array]"]=a["[object Int32Array]"]=a["[object Uint8Array]"]=a["[object Uint8ClampedArray]"]=a["[object Uint16Array]"]=a["[object Uint32Array]"]=!0,a["[object Arguments]"]=a["[object Array]"]=a["[object ArrayBuffer]"]=a["[object Boolean]"]=a["[object DataView]"]=a["[object Date]"]=a["[object Error]"]=a["[object Function]"]=a["[object Map]"]=a["[object Number]"]=a["[object Object]"]=a["[object RegExp]"]=a["[object Set]"]=a["[object String]"]=a["[object WeakMap]"]=!1,e.exports=function(e){return i(e)&&o(e.length)&&!!a[n(e)]}},80704:(e,t,r)=>{var n=r(96678);e.exports=function(e,t){return!!(null==e?0:e.length)&&n(e,t,0)>-1}},81488:e=>{e.exports=function(e,t){return null!=e&&t in Object(e)}},81630:e=>{"use strict";e.exports=require("http")},81957:(e,t,r)=>{var n=r(49227);e.exports=function(e,t){if(e!==t){var r=void 0!==e,o=null===e,i=e==e,a=n(e),c=void 0!==t,s=null===t,l=t==t,u=n(t);if(!s&&!u&&!a&&e>t||a&&c&&l&&!s&&!u||o&&c&&l||!r&&l||!i)return 1;if(!o&&!a&&!u&&e<t||u&&r&&i&&!o&&!a||s&&r&&i||!c&&i||!l)return -1}return 0}},82038:(e,t,r)=>{var n=r(57202),o=r(35163),i=r(40542),a=r(80329),c=r(38428),s=r(10090),l=Object.prototype.hasOwnProperty;e.exports=function(e,t){var r=i(e),u=!r&&o(e),f=!r&&!u&&a(e),p=!r&&!u&&!f&&s(e),d=r||u||f||p,h=d?n(e.length,String):[],y=h.length;for(var m in e)(t||l.call(e,m))&&!(d&&("length"==m||f&&("offset"==m||"parent"==m)||p&&("buffer"==m||"byteLength"==m||"byteOffset"==m)||c(m,y)))&&h.push(m);return h}},84031:(e,t,r)=>{"use strict";var n=r(34452);function o(){}function i(){}i.resetWarningCache=o,e.exports=function(){function e(e,t,r,o,i,a){if(a!==n){var c=Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw c.name="Invariant Violation",c}}function t(){return e}e.isRequired=e;var r={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:i,resetWarningCache:o};return r.PropTypes=r,r}},84261:e=>{e.exports=function(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=!!t,t}},84482:(e,t,r)=>{var n=r(28977);e.exports=function(e){var t=n(e),r=t%1;return t==t?r?t-r:t:0}},84713:e=>{var t=Object.prototype.toString;e.exports=function(e){return t.call(e)}},85244:e=>{e.exports=function(e,t,r){switch(r.length){case 0:return e.call(t);case 1:return e.call(t,r[0]);case 2:return e.call(t,r[0],r[1]);case 3:return e.call(t,r[0],r[1],r[2])}return e.apply(t,r)}},85406:(e,t,r)=>{e.exports=r(85718)["__core-js_shared__"]},85450:(e,t,r)=>{var n=r(79474),o=r(35163),i=r(40542),a=n?n.isConcatSpreadable:void 0;e.exports=function(e){return i(e)||o(e)||!!(a&&e&&e[a])}},85718:(e,t,r)=>{var n=r(10663),o="object"==typeof self&&self&&self.Object===Object&&self;e.exports=n||o||Function("return this")()},85726:(e,t,r)=>{"use strict";r.d(t,{E:()=>i});var n=r(60687),o=r(4780);function i({className:e,...t}){return(0,n.jsx)("div",{className:(0,o.cn)("animate-pulse rounded-md bg-muted",e),...t})}},85745:(e,t,r)=>{var n=r(86451);e.exports=function(e){var t=n(e,function(e){return 500===r.size&&r.clear(),e}),r=t.cache;return t}},85938:(e,t,r)=>{var n=r(42205),o=r(17518),i=r(46229),a=r(7383);e.exports=i(function(e,t){if(null==e)return[];var r=t.length;return r>1&&a(e,t[0],t[1])?t=[]:r>2&&a(t[0],t[1],t[2])&&(t=[t[0]]),o(e,n(t,1),[])})},86451:(e,t,r)=>{var n=r(95746);function o(e,t){if("function"!=typeof e||null!=t&&"function"!=typeof t)throw TypeError("Expected a function");var r=function(){var n=arguments,o=t?t.apply(this,n):n[0],i=r.cache;if(i.has(o))return i.get(o);var a=e.apply(this,n);return r.cache=i.set(o,a)||i,a};return r.cache=new(o.Cache||n),r}o.Cache=n,e.exports=o},87270:e=>{e.exports=function(e,t){for(var r=-1,n=null==e?0:e.length;++r<n;)if(!t(e[r],r,e))return!1;return!0}},87321:(e,t,r)=>{var n=r(98798),o=r(7383),i=r(28977);e.exports=function(e){return function(t,r,a){return a&&"number"!=typeof a&&o(t,r,a)&&(r=a=void 0),t=i(t),void 0===r?(r=t,t=0):r=i(r),a=void 0===a?t<r?1:-1:i(a),n(t,r,a,e)}}},87506:(e,t,r)=>{var n=r(66837),o=r(84261),i=r(89492),a=r(90200),c=r(39672);function s(e){var t=-1,r=null==e?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}s.prototype.clear=n,s.prototype.delete=o,s.prototype.get=i,s.prototype.has=a,s.prototype.set=c,e.exports=s},87955:(e,t,r)=>{e.exports=r(84031)()},89167:(e,t,r)=>{e.exports=r(41547)(r(85718),"DataView")},89185:e=>{e.exports=function(e){return this.__data__.set(e,"__lodash_hash_undefined__"),this}},89492:(e,t,r)=>{var n=r(58141),o=Object.prototype.hasOwnProperty;e.exports=function(e){var t=this.__data__;if(n){var r=t[e];return"__lodash_hash_undefined__"===r?void 0:r}return o.call(t,e)?t[e]:void 0}},89605:(e,t,r)=>{e.exports=r(65662)(Object.keys,Object)},89624:e=>{e.exports=function(e){return function(t){return e(t)}}},90200:(e,t,r)=>{var n=r(58141),o=Object.prototype.hasOwnProperty;e.exports=function(e){var t=this.__data__;return n?void 0!==t[e]:o.call(t,e)}},90453:(e,t,r)=>{var n=r(2984),o=r(99180),i=r(48169);e.exports=function(e){return e&&e.length?n(e,i,o):void 0}},90851:e=>{e.exports=function(e,t){return null==e?void 0:e[t]}},91273:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>a.a,__next_app__:()=>f,pages:()=>u,routeModule:()=>p,tree:()=>l});var n=r(65239),o=r(48088),i=r(88170),a=r.n(i),c=r(30893),s={};for(let e in c)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(s[e]=()=>c[e]);r.d(t,s);let l={children:["",{children:["admin",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,1132)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\admin\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,99111)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\admin\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,u=["C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\admin\\page.tsx"],f={require:r,loadChunk:()=>Promise.resolve()},p=new n.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/admin/page",pathname:"/admin",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},91290:e=>{e.exports=function(e,t,r,n){for(var o=e.length,i=r+(n?1:-1);n?i--:++i<o;)if(t(e[i],i,e))return i;return -1}},91645:e=>{"use strict";e.exports=require("net")},91928:(e,t,r)=>{var n=r(41547);e.exports=function(){try{var e=n(Object,"defineProperty");return e({},"",{}),e}catch(e){}}()},92662:(e,t,r)=>{var n=r(46328),o=r(80704),i=r(71960),a=r(58276),c=r(95308),s=r(2408);e.exports=function(e,t,r){var l=-1,u=o,f=e.length,p=!0,d=[],h=d;if(r)p=!1,u=i;else if(f>=200){var y=t?null:c(e);if(y)return s(y);p=!1,u=a,h=new n}else h=t?[]:d;t:for(;++l<f;){var m=e[l],v=t?t(m):m;if(m=r||0!==m?m:0,p&&v==v){for(var b=h.length;b--;)if(h[b]===v)continue t;t&&h.push(v),d.push(m)}else u(h,v,r)||(h!==d&&h.push(v),d.push(m))}return d}},93311:(e,t,r)=>{var n=r(34883),o=r(7651);e.exports=function(e){for(var t=o(e),r=t.length;r--;){var i=t[r],a=e[i];t[r]=[i,a,n(a)]}return t}},93490:(e,t,r)=>{var n=r(29395),o=r(27467);e.exports=function(e){return"number"==typeof e||o(e)&&"[object Number]"==n(e)}},94388:(e,t,r)=>{var n=r(57797);e.exports=function(e){var t=this.__data__,r=n(t,e);return r<0?void 0:t[r][1]}},94735:e=>{"use strict";e.exports=require("events")},95308:(e,t,r)=>{var n=r(34772),o=r(36959),i=r(2408);e.exports=n&&1/i(new n([,-0]))[1]==1/0?function(e){return new n(e)}:o},95746:(e,t,r)=>{var n=r(15909),o=r(29205),i=r(29508),a=r(61320),c=r(19976);function s(e){var t=-1,r=null==e?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}s.prototype.clear=n,s.prototype.delete=o,s.prototype.get=i,s.prototype.has=a,s.prototype.set=c,e.exports=s},96678:(e,t,r)=>{var n=r(91290),o=r(39774),i=r(74610);e.exports=function(e,t,r){return t==t?i(e,t,r):n(e,o,r)}},97025:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(82614).A)("CircleX",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},97668:(e,t)=>{"use strict";var r,n=Symbol.for("react.element"),o=Symbol.for("react.portal"),i=Symbol.for("react.fragment"),a=Symbol.for("react.strict_mode"),c=Symbol.for("react.profiler"),s=Symbol.for("react.provider"),l=Symbol.for("react.context"),u=Symbol.for("react.server_context"),f=Symbol.for("react.forward_ref"),p=Symbol.for("react.suspense"),d=Symbol.for("react.suspense_list"),h=Symbol.for("react.memo"),y=Symbol.for("react.lazy");Symbol.for("react.offscreen");Symbol.for("react.module.reference"),t.isFragment=function(e){return function(e){if("object"==typeof e&&null!==e){var t=e.$$typeof;switch(t){case n:switch(e=e.type){case i:case c:case a:case p:case d:return e;default:switch(e=e&&e.$$typeof){case u:case l:case f:case y:case h:case s:return e;default:return t}}case o:return t}}}(e)===i}},98451:(e,t,r)=>{var n=r(29395),o=r(27467);e.exports=function(e){return!0===e||!1===e||o(e)&&"[object Boolean]"==n(e)}},98798:e=>{var t=Math.ceil,r=Math.max;e.exports=function(e,n,o,i){for(var a=-1,c=r(t((n-e)/(o||1)),0),s=Array(c);c--;)s[i?c:++a]=e,e+=o;return s}},99111:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a,metadata:()=>i});var n=r(37413),o=r(22538);let i={title:"Admin Dashboard - WorkHub",description:"Administrative dashboard for WorkHub system"};function a({children:e}){return(0,n.jsxs)("div",{className:"space-y-6",children:[(0,n.jsx)("div",{className:"flex items-center",children:(0,n.jsx)(o.Breadcrumb,{className:"mb-4",children:(0,n.jsxs)(o.BreadcrumbList,{children:[(0,n.jsx)(o.BreadcrumbItem,{children:(0,n.jsx)(o.BreadcrumbLink,{href:"/",children:"Home"})}),(0,n.jsx)(o.BreadcrumbSeparator,{}),(0,n.jsx)(o.BreadcrumbItem,{children:(0,n.jsx)(o.BreadcrumbPage,{children:"Admin"})})]})})}),(0,n.jsx)("div",{className:"flex-1",children:e})]})}},99114:(e,t,r)=>{var n=r(12344),o=r(7651);e.exports=function(e,t){return e&&n(e,t,o)}},99180:e=>{e.exports=function(e,t){return e>t}},99525:e=>{e.exports=function(e,t){for(var r=-1,n=null==e?0:e.length;++r<n;)if(t(e[r],r,e))return!0;return!1}}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[4447,3622,1658,2729,8054,8141],()=>r(91273));module.exports=n})();