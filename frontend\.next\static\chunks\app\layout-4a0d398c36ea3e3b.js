(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7177],{4523:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>F});var r=s(95155);s(52728);var a=s(6874),n=s.n(a),i=s(10469),o=s(28328),l=s(3638),d=s(57082),c=s(35079),u=s(79239),f=s(18271),m=s(83173),x=s(30285),h=s(35695),p=s(59434),g=s(12115),v=s(36936),b=s(68027),j=s(51362),N=s(44838);function y(){let{setTheme:e}=(0,j.D)();return(0,r.jsxs)(N.rI,{children:[(0,r.jsx)(N.ty,{asChild:!0,children:(0,r.jsxs)(x.$,{variant:"ghost",size:"icon",className:"text-foreground hover:bg-accent hover:text-accent-foreground",children:[(0,r.jsx)(v.A,{className:"h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0"}),(0,r.jsx)(b.A,{className:"absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100"}),(0,r.jsx)("span",{className:"sr-only",children:"Toggle theme"})]})}),(0,r.jsxs)(N.SQ,{align:"end",children:[(0,r.jsx)(N._2,{onClick:()=>e("light"),children:"Light"}),(0,r.jsx)(N._2,{onClick:()=>e("dark"),children:"Dark"}),(0,r.jsx)(N._2,{onClick:()=>e("system"),children:"System"})]})]})}function w(){let e=(0,h.usePathname)(),t=[{href:"/",label:"Dashboard",icon:i.A},{href:"/vehicles",label:"Assets",icon:o.A},{href:"/service-history",label:"Maintenance",icon:l.A},{href:"/delegations",label:"Projects",icon:d.A},{href:"/tasks",label:"Tasks",icon:c.A},{href:"/employees",label:"Team",icon:u.A},{href:"/admin",label:"Admin",icon:f.A}];return(0,r.jsx)("header",{className:"bg-card text-card-foreground shadow-md no-print border-b border-border",children:(0,r.jsx)("nav",{className:"container mx-auto px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"flex items-center justify-between h-16",children:[(0,r.jsx)("div",{className:"flex items-center",children:(0,r.jsxs)(n(),{href:"/",className:"flex items-center space-x-2 text-xl font-semibold hover:opacity-80 transition-opacity",children:[(0,r.jsx)(m.A,{className:"h-7 w-7 text-primary"}),(0,r.jsx)("span",{children:"WorkHub"})]})}),(0,r.jsxs)("div",{className:"flex items-center space-x-1 md:space-x-2",children:[t.map(t=>(0,r.jsx)(x.$,{variant:"ghost",asChild:!0,className:(0,p.cn)("hover:bg-accent hover:text-accent-foreground px-2 md:px-3",e===t.href||"/"!==t.href&&e.startsWith(t.href)?"bg-accent text-accent-foreground":"text-foreground"),children:(0,r.jsxs)(n(),{href:t.href,className:"flex items-center",children:[(0,r.jsx)(t.icon,{className:"mr-0 h-4 w-4 md:mr-2"}),(0,r.jsx)("span",{className:"hidden md:inline",children:t.label})]})},t.href)),(0,r.jsx)(y,{})]})]})})})}var A=s(87481),T=s(26621),S=s(74466),k=s(25318);let _=T.Kq,E=g.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)(T.LM,{ref:t,className:(0,p.cn)("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",s),...a})});E.displayName=T.LM.displayName;let O=(0,S.F)("group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background text-foreground",destructive:"destructive group border-destructive bg-destructive text-destructive-foreground"}},defaultVariants:{variant:"default"}}),D=g.forwardRef((e,t)=>{let{className:s,variant:a,...n}=e;return(0,r.jsx)(T.bL,{ref:t,className:(0,p.cn)(O({variant:a}),s),...n})});D.displayName=T.bL.displayName,g.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)(T.rc,{ref:t,className:(0,p.cn)("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",s),...a})}).displayName=T.rc.displayName;let I=g.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)(T.bm,{ref:t,className:(0,p.cn)("absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",s),"toast-close":"",...a,children:(0,r.jsx)(k.A,{className:"h-4 w-4"})})});I.displayName=T.bm.displayName;let M=g.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)(T.hE,{ref:t,className:(0,p.cn)("text-sm font-semibold",s),...a})});M.displayName=T.hE.displayName;let C=g.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)(T.VY,{ref:t,className:(0,p.cn)("text-sm opacity-90",s),...a})});function R(){let{toasts:e}=(0,A.dj)();return(0,r.jsxs)(_,{children:[e.map(function(e){let{id:t,title:s,description:a,action:n,...i}=e;return(0,r.jsxs)(D,{...i,children:[(0,r.jsxs)("div",{className:"grid gap-1",children:[s&&(0,r.jsx)(M,{children:s}),a&&(0,r.jsx)(C,{children:a})]}),n,(0,r.jsx)(I,{})]},t)}),(0,r.jsx)(E,{})]})}function V(e){let{children:t,...s}=e;return(0,r.jsx)(j.N,{...s,children:t})}C.displayName=T.VY.displayName;var P=s(63554);s(68413);var L=s(93342);function W(e){let{children:t}=e,s=(0,h.usePathname)();return["/auth-test","/supabase-diagnostics"].some(e=>s.startsWith(e))?(0,r.jsx)(r.Fragment,{children:t}):(0,r.jsx)(L.OV,{requireEmailVerification:!0,children:t})}function F(e){let{children:t}=e;return(0,r.jsxs)("html",{lang:"en",className:"h-full",suppressHydrationWarning:!0,children:[(0,r.jsx)("head",{children:(0,r.jsx)(P.default,{src:"/fix-findindex-error.js",strategy:"beforeInteractive"})}),(0,r.jsx)("body",{className:"font-sans antialiased flex flex-col min-h-screen",children:(0,r.jsx)(V,{attribute:"class",defaultTheme:"system",enableSystem:!0,disableTransitionOnChange:!0,children:(0,r.jsx)(L.OJ,{children:(0,r.jsxs)(W,{children:[(0,r.jsx)(w,{}),(0,r.jsx)("main",{className:"flex-grow container mx-auto px-4 sm:px-6 lg:px-8 py-8",children:t}),(0,r.jsx)(R,{}),(0,r.jsx)("footer",{className:"bg-card text-card-foreground py-4 text-center text-sm no-print border-t border-border",children:(0,r.jsxs)("p",{children:["\xa9 ",new Date().getFullYear()," WorkHub. All rights reserved."]})})]})})})})]})}},52728:()=>{},87481:(e,t,s)=>{"use strict";s.d(t,{dj:()=>f});var r=s(12115);let a=0,n=new Map,i=e=>{if(n.has(e))return;let t=setTimeout(()=>{n.delete(e),c({type:"REMOVE_TOAST",toastId:e})},1e6);n.set(e,t)},o=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:s}=t;return s?i(s):e.toasts.forEach(e=>{i(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===s||void 0===s?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},l=[],d={toasts:[]};function c(e){d=o(d,e),l.forEach(e=>{e(d)})}function u(e){let{...t}=e,s=(a=(a+1)%Number.MAX_SAFE_INTEGER).toString(),r=()=>c({type:"DISMISS_TOAST",toastId:s});return c({type:"ADD_TOAST",toast:{...t,id:s,open:!0,onOpenChange:e=>{e||r()}}}),{id:s,dismiss:r,update:e=>c({type:"UPDATE_TOAST",toast:{...e,id:s}})}}function f(){let[e,t]=r.useState(d);return r.useEffect(()=>(l.push(t),()=>{let e=l.indexOf(t);e>-1&&l.splice(e,1)}),[e]),{...e,toast:u,dismiss:e=>c({type:"DISMISS_TOAST",toastId:e})}}},91288:(e,t,s)=>{Promise.resolve().then(s.bind(s,4523))}},e=>{var t=t=>e(e.s=t);e.O(0,[7690,9484,3796,6113,2688,2512,7529,8782,5033,5787,8162,5933,3342,8441,1684,7358],()=>t(91288)),_N_E=e.O()}]);