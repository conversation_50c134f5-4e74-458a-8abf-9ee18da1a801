(()=>{var e={};e.id=5298,e.ids=[5298],e.modules={35:(e,r,s)=>{Promise.resolve().then(s.bind(s,54008))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},24011:(e,r,s)=>{Promise.resolve().then(s.bind(s,93906))},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},54008:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>w});var t=s(60687),a=s(76180),i=s.n(a),n=s(43210),o=s(16189),l=s(28840),d=s(57697),c=s(30474),p=s(18578),m=s(48041),x=s(44493),h=s(52027),u=s(24847),b=s(44610),j=s(24920),v=s(85814),y=s.n(v),f=s(29523),g=s(68752),N=s(95758);function w(){let e=(0,o.useParams)(),[r,s]=(0,n.useState)(null),[a,v]=(0,n.useState)([]),[w,k]=(0,n.useState)(!0),[S,C]=(0,n.useState)(null),[P,q]=(0,n.useState)(0),T=e.id;(0,n.useCallback)(async()=>{if(k(!0),C(null),!T){C("No Vehicle ID provided."),k(!1);return}try{let e=await (0,l.getVehicleById)(Number(T));if(!e){C("Vehicle not found."),k(!1);return}s(e),document.title=`${e.make} ${e.model} - Service History Report`;let r=await (0,d._X)(Number(T));v(r)}catch(e){console.error("Error fetching vehicle service history:",e),C(e instanceof Error?e.message:"Failed to load vehicle data.")}finally{k(!1)}},[T]);let $=(0,n.useCallback)(()=>{q(e=>e+1)},[]);return w?(0,t.jsx)(N.A,{children:(0,t.jsx)("div",{className:"max-w-5xl mx-auto p-4",children:(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)(h.jt,{variant:"card",count:1}),(0,t.jsx)(h.jt,{variant:"table",count:5})]})})}):S||!r?(0,t.jsx)(N.A,{children:(0,t.jsx)("div",{className:"max-w-5xl mx-auto p-4",children:(0,t.jsx)(x.Zp,{className:"shadow-md",children:(0,t.jsxs)(x.Wu,{className:"p-6",children:[(0,t.jsx)("h2",{className:"text-xl font-semibold text-red-600 mb-2",children:"Error"}),(0,t.jsx)("p",{className:"text-gray-700 mb-4",children:S||"Vehicle not found"}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsx)(f.$,{onClick:$,children:"Try Again"}),(0,t.jsx)(f.$,{variant:"outline",asChild:!0,children:(0,t.jsx)(y(),{href:"/vehicles",children:"Back to Vehicles"})})]})]})})})}):(0,t.jsx)(N.A,{children:(0,t.jsxs)("div",{className:"jsx-1952bdba77817484 max-w-5xl mx-auto p-4 space-y-6 print-container",children:[(0,t.jsx)(m.z,{title:"Vehicle Service History",description:`${r.make} ${r.model} (${r.year})`,icon:b.A,children:(0,t.jsxs)("div",{className:"jsx-1952bdba77817484 flex gap-2 no-print",children:[(0,t.jsx)(g.r,{actionType:"tertiary",asChild:!0,icon:(0,t.jsx)(j.A,{className:"h-4 w-4"}),children:(0,t.jsx)(y(),{href:`/vehicles/${T}`,children:"View Vehicle"})}),(0,t.jsx)(p.k,{reportContentId:"#vehicle-service-history-content",reportType:"vehicle-service-history",entityId:T,tableId:"#service-history-table",fileName:`vehicle-service-history-${r.make}-${r.model}`,enableCsv:a.length>0})]})}),(0,t.jsxs)("div",{id:"vehicle-service-history-content",className:"jsx-1952bdba77817484 report-content",children:[(0,t.jsx)(x.Zp,{className:"shadow-md mb-6 card-print",children:(0,t.jsx)(x.Wu,{className:"p-4",children:(0,t.jsxs)("div",{className:"jsx-1952bdba77817484 grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,t.jsxs)("div",{className:"jsx-1952bdba77817484 col-span-1 md:col-span-2",children:[(0,t.jsx)("h2",{className:"jsx-1952bdba77817484 text-xl font-semibold text-gray-800 mb-4",children:"Vehicle Details"}),(0,t.jsxs)("div",{className:"jsx-1952bdba77817484 grid grid-cols-2 gap-x-4 gap-y-2 text-sm",children:[(0,t.jsxs)("div",{className:"jsx-1952bdba77817484",children:[(0,t.jsx)("span",{className:"jsx-1952bdba77817484 font-medium",children:"Make:"})," ",r.make]}),(0,t.jsxs)("div",{className:"jsx-1952bdba77817484",children:[(0,t.jsx)("span",{className:"jsx-1952bdba77817484 font-medium",children:"Model:"})," ",r.model]}),(0,t.jsxs)("div",{className:"jsx-1952bdba77817484",children:[(0,t.jsx)("span",{className:"jsx-1952bdba77817484 font-medium",children:"Year:"})," ",r.year]}),r.licensePlate&&(0,t.jsxs)("div",{className:"jsx-1952bdba77817484",children:[(0,t.jsx)("span",{className:"jsx-1952bdba77817484 font-medium",children:"Plate Number:"})," ",r.licensePlate]}),r.color&&(0,t.jsxs)("div",{className:"jsx-1952bdba77817484",children:[(0,t.jsx)("span",{className:"jsx-1952bdba77817484 font-medium",children:"Color:"})," ",r.color]}),(0,t.jsxs)("div",{className:"jsx-1952bdba77817484",children:[(0,t.jsx)("strong",{className:"jsx-1952bdba77817484",children:"Initial Odometer:"})," ",null!==r.initialOdometer?`${r.initialOdometer.toLocaleString()} miles`:"Not recorded"]}),(0,t.jsxs)("div",{className:"jsx-1952bdba77817484",children:[(0,t.jsx)("span",{className:"jsx-1952bdba77817484 font-medium",children:"Current Odometer:"})," ",a.length>0?`${Math.max(...a.map(e=>e.odometer)).toLocaleString()} miles`:null!==r.initialOdometer?`${r.initialOdometer.toLocaleString()} miles`:"No odometer data available"]}),(0,t.jsxs)("div",{className:"jsx-1952bdba77817484 col-span-2",children:[(0,t.jsx)("span",{className:"jsx-1952bdba77817484 font-medium",children:"Last Updated:"})," ",a.length>0?new Date(Math.max(...a.map(e=>new Date(e.date).getTime()))).toLocaleDateString():"No service records"]})]})]}),r.imageUrl&&(0,t.jsx)("div",{className:"jsx-1952bdba77817484 col-span-1 no-print",children:(0,t.jsx)("div",{className:"jsx-1952bdba77817484 relative aspect-[4/3] w-full overflow-hidden rounded",children:(0,t.jsx)(c.default,{src:r.imageUrl,alt:`${r.make} ${r.model}`,fill:!0,sizes:"(max-width: 768px) 100vw, 300px",style:{objectFit:"cover"}})})})]})})}),(0,t.jsxs)("header",{className:"jsx-1952bdba77817484 text-center mb-8 pb-4 border-b-2 border-gray-300 print-only",children:[(0,t.jsx)("h1",{className:"jsx-1952bdba77817484 text-3xl font-bold text-gray-800",children:"Vehicle Service History Report"}),(0,t.jsxs)("p",{className:"jsx-1952bdba77817484 text-md text-gray-600",children:[r.make," ",r.model," (",r.year,")",r.licensePlate&&` - ${r.licensePlate}`]})]}),(0,t.jsx)(u.R,{records:a,isLoading:!1,error:null,onRetry:$,showVehicleInfo:!1,vehicleSpecific:!0}),(0,t.jsxs)("footer",{className:"jsx-1952bdba77817484 mt-10 pt-4 border-t-2 border-gray-300 text-center text-xs text-gray-500",children:[(0,t.jsxs)("p",{className:"jsx-1952bdba77817484",children:["Report generated on: ",new Date().toLocaleDateString()]}),(0,t.jsx)("p",{className:"jsx-1952bdba77817484",children:"WorkHub - Vehicle Service Management"})]})]}),(0,t.jsx)(i(),{id:"1952bdba77817484",children:".print-only{display:none}@media print{.no-print{display:none!important}.print-only{display:block}.print-container{padding:1rem}.card-print{-webkit-box-shadow:none!important;-moz-box-shadow:none!important;box-shadow:none!important;border:none!important}.print-service-col{max-width:200px;white-space:normal!important}.print-notes-col{max-width:200px;white-space:normal!important}.print-text-wrap{word-break:break-word;white-space:normal!important}}@media(max-width:640px){.overflow-x-auto{overflow-x:auto}.summary-grid{grid-template-columns:1fr 1fr!important}}"})]})})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},61662:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(82614).A)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64273:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GlobalError:()=>n.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>m,tree:()=>d});var t=s(65239),a=s(48088),i=s(88170),n=s.n(i),o=s(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);s.d(r,l);let d={children:["",{children:["vehicles",{children:["[id]",{children:["report",{children:["service-history",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,93906)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\vehicles\\[id]\\report\\service-history\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,82196)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\vehicles\\[id]\\report\\layout.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\vehicles\\[id]\\report\\service-history\\page.tsx"],p={require:s,loadChunk:()=>Promise.resolve()},m=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/vehicles/[id]/report/service-history/page",pathname:"/vehicles/[id]/report/service-history",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},82196:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>i,metadata:()=>a});var t=s(37413);let a={title:"Vehicle Report"};function i({children:e}){return(0,t.jsx)(t.Fragment,{children:e})}},89743:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(82614).A)("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},91645:e=>{"use strict";e.exports=require("net")},93906:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>t});let t=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\projects\\\\CarServiceTrackingSystem\\\\TrackerSystem\\\\frontend\\\\src\\\\app\\\\vehicles\\\\[id]\\\\report\\\\service-history\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\vehicles\\[id]\\report\\service-history\\page.tsx","default")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var r=require("../../../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[4447,3622,1658,5880,474,6055,9584,8141,3983,4318,4550],()=>s(64273));module.exports=t})();