(()=>{var e={};e.id=9726,e.ids=[9726],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6211:(e,t,a)=>{"use strict";a.d(t,{A0:()=>n,BF:()=>o,Hj:()=>d,XI:()=>i,nA:()=>m,nd:()=>c});var s=a(60687),r=a(43210),l=a(4780);let i=r.forwardRef(({className:e,...t},a)=>(0,s.jsx)("div",{className:"relative w-full overflow-auto",children:(0,s.jsx)("table",{ref:a,className:(0,l.cn)("w-full caption-bottom text-sm",e),...t})}));i.displayName="Table";let n=r.forwardRef(({className:e,...t},a)=>(0,s.jsx)("thead",{ref:a,className:(0,l.cn)("[&_tr]:border-b",e),...t}));n.displayName="TableHeader";let o=r.forwardRef(({className:e,...t},a)=>(0,s.jsx)("tbody",{ref:a,className:(0,l.cn)("[&_tr:last-child]:border-0",e),...t}));o.displayName="TableBody",r.forwardRef(({className:e,...t},a)=>(0,s.jsx)("tfoot",{ref:a,className:(0,l.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",e),...t})).displayName="TableFooter";let d=r.forwardRef(({className:e,...t},a)=>(0,s.jsx)("tr",{ref:a,className:(0,l.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",e),...t}));d.displayName="TableRow";let c=r.forwardRef(({className:e,...t},a)=>(0,s.jsx)("th",{ref:a,className:(0,l.cn)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",e),...t}));c.displayName="TableHead";let m=r.forwardRef(({className:e,...t},a)=>(0,s.jsx)("td",{ref:a,className:(0,l.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",e),...t}));m.displayName="TableCell",r.forwardRef(({className:e,...t},a)=>(0,s.jsx)("caption",{ref:a,className:(0,l.cn)("mt-4 text-sm text-muted-foreground",e),...t})).displayName="TableCaption"},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},14583:(e,t,a)=>{"use strict";a.d(t,{$o:()=>g});var s=a(60687),r=a(43210),l=a(4780),i=a(43967),n=a(74158),o=a(69795),d=a(29523);let c=r.forwardRef(({className:e,...t},a)=>(0,s.jsx)("div",{ref:a,className:(0,l.cn)("flex justify-center",e),...t}));c.displayName="Pagination";let m=r.forwardRef(({className:e,...t},a)=>(0,s.jsx)("ul",{ref:a,className:(0,l.cn)("flex flex-row items-center gap-1",e),...t}));m.displayName="PaginationContent";let x=r.forwardRef(({className:e,...t},a)=>(0,s.jsx)("li",{ref:a,className:(0,l.cn)("",e),...t}));x.displayName="PaginationItem";let p=r.forwardRef(({className:e,isActive:t,...a},r)=>(0,s.jsx)(d.$,{ref:r,"aria-current":t?"page":void 0,variant:t?"outline":"ghost",size:"icon",className:(0,l.cn)("h-9 w-9",e),...a}));p.displayName="PaginationLink";let h=r.forwardRef(({className:e,...t},a)=>(0,s.jsxs)(d.$,{ref:a,variant:"ghost",size:"icon",className:(0,l.cn)("h-9 w-9 gap-1",e),...t,children:[(0,s.jsx)(i.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"sr-only",children:"Previous page"})]}));h.displayName="PaginationPrevious";let b=r.forwardRef(({className:e,...t},a)=>(0,s.jsxs)(d.$,{ref:a,variant:"ghost",size:"icon",className:(0,l.cn)("h-9 w-9 gap-1",e),...t,children:[(0,s.jsx)(n.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"sr-only",children:"Next page"})]}));b.displayName="PaginationNext";let u=r.forwardRef(({className:e,...t},a)=>(0,s.jsxs)("span",{ref:a,"aria-hidden":!0,className:(0,l.cn)("flex h-9 w-9 items-center justify-center",e),...t,children:[(0,s.jsx)(o.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"sr-only",children:"More pages"})]}));function g({currentPage:e,totalPages:t,onPageChange:a,className:r}){let l=(()=>{let a=[];a.push(1);let s=Math.max(2,e-1),r=Math.min(t-1,e+1);s>2&&a.push("ellipsis1");for(let e=s;e<=r;e++)a.push(e);return r<t-1&&a.push("ellipsis2"),t>1&&a.push(t),a})();return t<=1?null:(0,s.jsx)(c,{className:r,children:(0,s.jsxs)(m,{children:[(0,s.jsx)(x,{children:(0,s.jsx)(h,{onClick:()=>a(e-1),disabled:1===e,"aria-disabled":1===e?"true":void 0,"aria-label":"Go to previous page"})}),l.map((t,r)=>"ellipsis1"===t||"ellipsis2"===t?(0,s.jsx)(x,{children:(0,s.jsx)(u,{})},`ellipsis-${r}`):(0,s.jsx)(x,{children:(0,s.jsx)(p,{onClick:()=>a(t),isActive:e===t,"aria-label":`Go to page ${t}`,children:t})},`page-${t}`)),(0,s.jsx)(x,{children:(0,s.jsx)(b,{onClick:()=>a(e+1),disabled:e===t,"aria-disabled":e===t?"true":void 0,"aria-label":"Go to next page"})})]})})}u.displayName="PaginationEllipsis"},15079:(e,t,a)=>{"use strict";a.d(t,{bq:()=>x,eb:()=>u,gC:()=>b,l6:()=>c,yv:()=>m});var s=a(60687),r=a(43210),l=a(22670),i=a(61662),n=a(89743),o=a(58450),d=a(4780);let c=l.bL;l.YJ;let m=l.WT,x=r.forwardRef(({className:e,children:t,...a},r)=>(0,s.jsxs)(l.l9,{ref:r,className:(0,d.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...a,children:[t,(0,s.jsx)(l.In,{asChild:!0,children:(0,s.jsx)(i.A,{className:"h-4 w-4 opacity-50"})})]}));x.displayName=l.l9.displayName;let p=r.forwardRef(({className:e,...t},a)=>(0,s.jsx)(l.PP,{ref:a,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,s.jsx)(n.A,{className:"h-4 w-4"})}));p.displayName=l.PP.displayName;let h=r.forwardRef(({className:e,...t},a)=>(0,s.jsx)(l.wn,{ref:a,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,s.jsx)(i.A,{className:"h-4 w-4"})}));h.displayName=l.wn.displayName;let b=r.forwardRef(({className:e,children:t,position:a="popper",...r},i)=>(0,s.jsx)(l.ZL,{children:(0,s.jsxs)(l.UC,{ref:i,className:(0,d.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===a&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:a,...r,children:[(0,s.jsx)(p,{}),(0,s.jsx)(l.LM,{className:(0,d.cn)("p-1","popper"===a&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:t}),(0,s.jsx)(h,{})]})}));b.displayName=l.UC.displayName,r.forwardRef(({className:e,...t},a)=>(0,s.jsx)(l.JU,{ref:a,className:(0,d.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",e),...t})).displayName=l.JU.displayName;let u=r.forwardRef(({className:e,children:t,...a},r)=>(0,s.jsxs)(l.q7,{ref:r,className:(0,d.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...a,children:[(0,s.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,s.jsx)(l.VF,{children:(0,s.jsx)(o.A,{className:"h-4 w-4"})})}),(0,s.jsx)(l.p4,{children:t})]}));u.displayName=l.q7.displayName,r.forwardRef(({className:e,...t},a)=>(0,s.jsx)(l.wv,{ref:a,className:(0,d.cn)("-mx-1 my-1 h-px bg-muted",e),...t})).displayName=l.wv.displayName},15795:(e,t,a)=>{"use strict";function s(e){switch(e){case"In_Progress":return"In Progress";case"No_details":return"No Details";default:return e}}a.d(t,{fZ:()=>s})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26398:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(82614).A)("MapPin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},35165:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>x,tree:()=>d});var s=a(65239),r=a(48088),l=a(88170),i=a.n(l),n=a(30893),o={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);a.d(t,o);let d={children:["",{children:["delegations",{children:["report",{children:["list",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,48344)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\delegations\\report\\list\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(a.bind(a,78691)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\delegations\\report\\list\\layout.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,94431)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\delegations\\report\\list\\page.tsx"],m={require:a,loadChunk:()=>Promise.resolve()},x=new s.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/delegations/report/list/page",pathname:"/delegations/report/list",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},37859:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>L});var s=a(60687),r=a(76180),l=a.n(r),i=a(43210),n=a(16189);a(28840);var o=a(89743),d=a(61662),c=a(41936),m=a(78726),x=a(92876),p=a(26398),h=a(48206),b=a(18578),u=a(76869),g=a(58261),j=a(96834),f=a(4780),y=a(15795),N=a(6211),v=a(44493),w=a(52027),k=a(29523),C=a(89667),S=a(15079);let P={Planned:{bg:"bg-gradient-to-br from-blue-50 to-blue-100",text:"text-blue-700",border:"border-blue-200"},Confirmed:{bg:"bg-gradient-to-br from-green-50 to-green-100",text:"text-green-700",border:"border-green-200"},In_Progress:{bg:"bg-gradient-to-br from-yellow-50 to-yellow-100",text:"text-yellow-700",border:"border-yellow-200"},Completed:{bg:"bg-gradient-to-br from-purple-50 to-purple-100",text:"text-purple-700",border:"border-purple-200"},Cancelled:{bg:"bg-gradient-to-br from-red-50 to-red-100",text:"text-red-700",border:"border-red-200"},No_details:{bg:"bg-gradient-to-br from-gray-50 to-gray-100",text:"text-gray-700",border:"border-gray-200"}},A=e=>e.replace("_"," ");function D({delegations:e,className:t}){let a=e.length,r=e.reduce((e,t)=>{let a=t.status;return e[a]=(e[a]||0)+1,e},{}),l=e.reduce((e,t)=>e+t.delegates.length,0),i=Object.entries(r).sort(([,e],[,t])=>t-e).map(([e])=>e);return(0,s.jsxs)("div",{className:(0,f.cn)("mt-6 mb-8",t),children:[(0,s.jsxs)("div",{className:"grid grid-cols-2 lg:grid-cols-4 gap-4 mb-6",children:[(0,s.jsx)(R,{value:a,label:"Total Delegations",className:"bg-gradient-to-br from-slate-50 to-slate-100 border-slate-200 shadow-sm hover:shadow-md transition-shadow",textColor:"text-slate-700",valueColor:"text-slate-800"}),(0,s.jsx)(R,{value:l,label:"Total Delegates",className:"bg-gradient-to-br from-indigo-50 to-indigo-100 border-indigo-200 shadow-sm hover:shadow-md transition-shadow",textColor:"text-indigo-700",valueColor:"text-indigo-800"}),i.slice(0,2).map(e=>{let t=P[e];return(0,s.jsx)(R,{value:r[e],label:A(e),className:(0,f.cn)(t.bg,t.border,"shadow-sm hover:shadow-md transition-shadow"),textColor:t.text,valueColor:t.text},e)})]}),i.length>2&&(0,s.jsx)("div",{className:"grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-6 gap-3",children:i.slice(2).map(e=>{let t=P[e];return(0,s.jsx)(R,{value:r[e],label:A(e),className:(0,f.cn)(t.bg,t.border,"shadow-sm hover:shadow-md transition-shadow"),textColor:t.text,valueColor:t.text,compact:!0},e)})})]})}function R({value:e,label:t,className:a,textColor:r="text-gray-600",valueColor:l="text-gray-800",compact:i=!1}){return(0,s.jsx)(v.Zp,{className:(0,f.cn)("overflow-hidden border transition-all duration-200",a),children:(0,s.jsxs)(v.Wu,{className:(0,f.cn)("text-center",i?"p-3":"p-4"),children:[(0,s.jsx)("div",{className:(0,f.cn)("font-bold",i?"text-xl mb-1":"text-3xl mb-2",l),children:e.toLocaleString()}),(0,s.jsx)("div",{className:(0,f.cn)("font-medium",i?"text-xs":"text-sm",r),children:t})]})})}var T=a(14583);let F=["Planned","Confirmed","In_Progress","Completed","Cancelled","No_details"],M=e=>{switch(e){case"Planned":return"bg-blue-100 text-blue-800 border-blue-300";case"Confirmed":return"bg-green-100 text-green-800 border-green-300";case"In_Progress":return"bg-yellow-100 text-yellow-800 border-yellow-300";case"Completed":return"bg-purple-100 text-purple-800 border-purple-300";case"Cancelled":return"bg-red-100 text-red-800 border-red-300";default:return"bg-gray-100 text-gray-800 border-gray-300"}},q=e=>{if(!e)return"N/A";try{return(0,u.GP)((0,g.H)(e),"MMM d, yyyy")}catch(e){return"Invalid Date"}};function _(){(0,n.useSearchParams)();let[e,t]=(0,i.useState)([]),[a,r]=(0,i.useState)([]),[u,g]=(0,i.useState)(!0),[P,A]=(0,i.useState)(""),[R,_]=(0,i.useState)(""),[L,I]=(0,i.useState)("all"),[$,z]=(0,i.useState)({}),[E,G]=(0,i.useState)(1),[Z]=(0,i.useState)(10),[H,K]=(0,i.useState)("durationFrom"),[U,W]=(0,i.useState)("desc"),B=(0,i.useCallback)(e=>{let t=[...e];if(R){let e=R.toLowerCase();t=t.filter(t=>t.eventName.toLowerCase().includes(e)||t.location.toLowerCase().includes(e)||t.delegates.some(t=>t.name.toLowerCase().includes(e))||t.notes&&t.notes.toLowerCase().includes(e)||t.status.toLowerCase().includes(e))}"all"!==L&&(t=t.filter(e=>e.status===L)),$.from&&(t=t.filter(e=>new Date(e.durationFrom)>=$.from)),$.to&&(t=t.filter(e=>new Date(e.durationFrom)<=$.to)),r(t=J(t,H,U)),G(1)},[R,L,$,H,U]),J=(0,i.useCallback)((e,t,a)=>[...e].sort((e,s)=>{let r,l;switch(t){case"durationFrom":r=new Date(e.durationFrom).getTime(),l=new Date(s.durationFrom).getTime();break;case"status":r=e.status,l=s.status;break;case"eventName":r=e.eventName.toLowerCase(),l=s.eventName.toLowerCase();break;case"location":r=e.location.toLowerCase(),l=s.location.toLowerCase();break;case"delegates":r=e.delegates.length,l=s.delegates.length;break;default:r=e[t],l=s[t]}return null==r||null==l?0:r<l?"asc"===a?-1:1:r>l?"asc"===a?1:-1:0}),[]),O=(0,i.useCallback)(e=>{H===e?W("asc"===U?"desc":"asc"):(K(e),W("asc"))},[H,U]),X=(0,i.useCallback)(e=>H!==e?"none":"asc"===U?"ascending":"descending",[H,U]),V=(0,i.useCallback)(()=>{A(""),_(""),I("all"),z({}),G(1),B(e)},[e,B]),Y=E*Z,Q=Y-Z,ee=(0,i.useMemo)(()=>a.slice(Q,Y),[a,Q,Y]),et=(0,i.useMemo)(()=>Math.ceil(a.length/Z),[a.length,Z]),ea=(0,i.useCallback)(e=>{G(e)},[]),es=(0,i.useCallback)(e=>H!==e?null:"asc"===U?(0,s.jsx)(o.A,{className:"inline-block h-4 w-4 ml-1"}):(0,s.jsx)(d.A,{className:"inline-block h-4 w-4 ml-1"}),[H,U]);return u?(0,s.jsx)("div",{className:"max-w-5xl mx-auto p-4",children:(0,s.jsx)(w.jt,{variant:"table",count:5})}):(0,s.jsxs)("div",{className:"jsx-647059bd56a41866 max-w-7xl mx-auto bg-white p-4 sm:p-6 lg:p-8 text-gray-800 min-h-screen",children:[(0,s.jsx)("div",{className:"jsx-647059bd56a41866 text-right mb-6 no-print",children:(0,s.jsx)(b.k,{reportType:"delegations",reportContentId:"#delegations-list-report-content",tableId:"#delegations-table",fileName:`delegations-list-report-${new Date().toISOString().split("T")[0]}`,enableCsv:a.length>0})}),(0,s.jsxs)("div",{id:"delegations-list-report-content",className:"jsx-647059bd56a41866 report-content",children:[(0,s.jsxs)("header",{className:"jsx-647059bd56a41866 text-center mb-10 pb-6 border-b border-gray-200 report-header",children:[(0,s.jsxs)("div",{className:"jsx-647059bd56a41866 mb-6",children:[(0,s.jsx)("h1",{className:"jsx-647059bd56a41866 text-4xl font-bold text-gray-900 mb-3 report-header-title",children:"Delegation List Report"}),(0,s.jsx)("div",{className:"jsx-647059bd56a41866 w-24 h-1 bg-gradient-to-r from-blue-500 to-indigo-600 mx-auto rounded-full"})]}),(0,s.jsx)("p",{className:"jsx-647059bd56a41866 text-lg text-gray-600 mb-2 report-header-subtitle",children:P||"all"!==L?`Filtered by: ${"all"!==L?`Status - ${(0,y.fZ)(L)}`:""}${P?("all"!==L?" | ":"")+`Search - "${P}"`:""}`:"All Delegations"}),(0,s.jsxs)("p",{className:"jsx-647059bd56a41866 text-sm text-gray-500 report-header-date",children:["Generated: ",new Date().toLocaleDateString()," ",new Date().toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})]}),(0,s.jsx)("div",{className:"jsx-647059bd56a41866 no-print",children:(0,s.jsx)(D,{delegations:a})}),(0,s.jsxs)("div",{className:"jsx-647059bd56a41866 print-only print-summary-stats",children:[(0,s.jsxs)("div",{className:"jsx-647059bd56a41866 delegation-print-summary-item",children:[(0,s.jsx)("span",{className:"jsx-647059bd56a41866 delegation-print-summary-label",children:"Total Delegations:"})," ",(0,s.jsx)("span",{className:"jsx-647059bd56a41866 delegation-print-summary-value",children:a.length})]}),(0,s.jsxs)("div",{className:"jsx-647059bd56a41866 delegation-print-summary-item",children:[(0,s.jsx)("span",{className:"jsx-647059bd56a41866 delegation-print-summary-label",children:"Total Delegates:"})," ",(0,s.jsx)("span",{className:"jsx-647059bd56a41866 delegation-print-summary-value",children:a.reduce((e,t)=>e+t.delegates.length,0)})]}),F.map(e=>({status:e,count:a.filter(t=>t.status===e).length})).filter(e=>e.count>0).sort((e,t)=>t.count-e.count).slice(0,3).map(({status:e,count:t})=>(0,s.jsxs)("div",{className:"jsx-647059bd56a41866 delegation-print-summary-item",children:[(0,s.jsxs)("span",{className:"jsx-647059bd56a41866 delegation-print-summary-label",children:[(0,y.fZ)(e),":"]})," ",(0,s.jsx)("span",{className:"jsx-647059bd56a41866 delegation-print-summary-value",children:t})]},e)),"all"!==L&&(0,s.jsxs)("div",{className:"jsx-647059bd56a41866 delegation-print-summary-item",children:[(0,s.jsx)("span",{className:"jsx-647059bd56a41866 delegation-print-summary-label",children:"Filtered by Status:"})," ",(0,s.jsx)("span",{className:"jsx-647059bd56a41866 delegation-print-summary-value",children:(0,y.fZ)(L)})]}),P&&(0,s.jsxs)("div",{className:"jsx-647059bd56a41866 delegation-print-summary-item",children:[(0,s.jsx)("span",{className:"jsx-647059bd56a41866 delegation-print-summary-label",children:"Search Term:"})," ",(0,s.jsxs)("span",{className:"jsx-647059bd56a41866 delegation-print-summary-value",children:['"',P,'"']})]})]})]}),(0,s.jsx)("div",{className:"jsx-647059bd56a41866 mb-8 no-print",children:(0,s.jsx)(v.Zp,{className:"shadow-lg border-0 bg-gradient-to-r from-slate-50 to-gray-50",children:(0,s.jsxs)(v.Wu,{className:"p-6",children:[(0,s.jsxs)("div",{className:"jsx-647059bd56a41866 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 filter-grid",children:[(0,s.jsxs)("div",{className:"jsx-647059bd56a41866",children:[(0,s.jsx)("label",{htmlFor:"status-filter",className:"jsx-647059bd56a41866 block text-sm font-semibold text-gray-700 mb-2",children:"Filter by Status"}),(0,s.jsxs)(S.l6,{value:L,onValueChange:I,"aria-label":"Filter by status",children:[(0,s.jsx)(S.bq,{className:"w-full bg-white border-gray-300 focus:border-blue-500 focus:ring-blue-500",children:(0,s.jsx)(S.yv,{placeholder:"All Statuses"})}),(0,s.jsxs)(S.gC,{children:[(0,s.jsx)(S.eb,{value:"all",children:"All Statuses"}),F.map(e=>(0,s.jsx)(S.eb,{value:e,children:(0,y.fZ)(e)},e))]})]})]}),(0,s.jsxs)("div",{className:"jsx-647059bd56a41866 relative",children:[(0,s.jsx)("label",{htmlFor:"search-input",className:"jsx-647059bd56a41866 block text-sm font-semibold text-gray-700 mb-2",children:"Search Delegations"}),(0,s.jsxs)("div",{className:"jsx-647059bd56a41866 relative",children:[(0,s.jsx)(C.p,{id:"search-input",type:"text",placeholder:"Search by event, location, or delegate...",value:P,onChange:e=>A(e.target.value),className:"pl-10 pr-10 bg-white border-gray-300 focus:border-blue-500 focus:ring-blue-500","aria-label":"Search delegations"}),(0,s.jsx)(c.A,{className:"absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-gray-400","aria-hidden":"true"}),P&&(0,s.jsxs)(k.$,{variant:"ghost",size:"icon",className:"absolute right-1 top-1/2 -translate-y-1/2 h-7 w-7",onClick:()=>A(""),"aria-label":"Clear search",children:[(0,s.jsx)(m.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"jsx-647059bd56a41866 sr-only",children:"Clear search"})]})]})]}),(0,s.jsxs)("div",{className:"jsx-647059bd56a41866",children:[(0,s.jsx)("label",{htmlFor:"date-range",className:"jsx-647059bd56a41866 block text-sm font-semibold text-gray-700 mb-2",children:"Date Range"}),(0,s.jsx)(C.p,{id:"date-range",type:"text",placeholder:"Date range filter coming soon",disabled:!0,className:"opacity-50 bg-gray-100","aria-label":"Date range filter (coming soon)"})]})]}),(P||"all"!==L)&&(0,s.jsxs)("div",{className:"jsx-647059bd56a41866 mt-6 flex items-center justify-between bg-blue-50 p-4 rounded-lg border border-blue-200",children:[(0,s.jsxs)("div",{className:"jsx-647059bd56a41866 text-sm",children:[(0,s.jsx)("span",{className:"jsx-647059bd56a41866 font-semibold text-blue-800",children:"Active Filters:"}),P&&(0,s.jsxs)("span",{className:"jsx-647059bd56a41866 ml-2 px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs",children:['Search: "',P,'"']}),"all"!==L&&(0,s.jsxs)("span",{className:"jsx-647059bd56a41866 ml-2 px-2 py-1 bg-green-100 text-green-800 rounded text-xs",children:["Status:"," ",(0,y.fZ)(L)]})]}),(0,s.jsx)(k.$,{variant:"outline",size:"sm",onClick:V,className:"border-blue-300 text-blue-700 hover:bg-blue-100","aria-label":"Reset all filters",children:"Reset Filters"})]})]})})}),0===a.length?(0,s.jsx)("div",{className:"jsx-647059bd56a41866 text-center py-16 bg-gradient-to-br from-gray-50 to-slate-100 rounded-xl border border-gray-200",children:(0,s.jsxs)("div",{className:"jsx-647059bd56a41866 max-w-md mx-auto",children:[(0,s.jsx)("div",{className:"jsx-647059bd56a41866 mb-4 text-gray-400",children:(0,s.jsx)(x.A,{className:"h-16 w-16 mx-auto mb-4"})}),(0,s.jsx)("h3",{className:"jsx-647059bd56a41866 text-lg font-semibold text-gray-700 mb-2",children:"No delegations found"}),(0,s.jsx)("p",{className:"jsx-647059bd56a41866 text-gray-500 mb-4",children:"No delegations match the current filter criteria."}),(0,s.jsx)(k.$,{variant:"outline",size:"lg",onClick:V,className:"mt-2","aria-label":"Reset filters to show all delegations",children:"Reset Filters"})]})}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(v.Zp,{className:"card-print shadow-lg border-0 overflow-hidden",children:(0,s.jsx)(v.Wu,{className:"p-0",children:(0,s.jsx)("div",{className:"jsx-647059bd56a41866 delegations-table-container overflow-x-auto",children:(0,s.jsxs)(N.XI,{id:"delegations-table",children:[(0,s.jsx)(N.A0,{children:(0,s.jsxs)(N.Hj,{className:"bg-gradient-to-r from-slate-100 to-gray-100 border-b border-gray-200",children:[(0,s.jsxs)(N.nd,{onClick:()=>O("eventName"),className:"cursor-pointer hover:bg-slate-200 transition-colors font-semibold text-gray-700","aria-sort":X("eventName"),role:"columnheader",tabIndex:0,onKeyDown:e=>{("Enter"===e.key||" "===e.key)&&(e.preventDefault(),O("eventName"))},"aria-label":"Sort by event name",style:{width:"25%"},children:["Event Name ",es("eventName")]}),(0,s.jsxs)(N.nd,{onClick:()=>O("location"),className:"cursor-pointer hover:bg-slate-200 transition-colors font-semibold text-gray-700","aria-sort":X("location"),role:"columnheader",tabIndex:0,onKeyDown:e=>{("Enter"===e.key||" "===e.key)&&(e.preventDefault(),O("location"))},"aria-label":"Sort by location",style:{width:"20%"},children:["Location ",es("location")]}),(0,s.jsxs)(N.nd,{onClick:()=>O("durationFrom"),className:"cursor-pointer hover:bg-slate-200 transition-colors font-semibold text-gray-700","aria-sort":X("durationFrom"),role:"columnheader",tabIndex:0,onKeyDown:e=>{("Enter"===e.key||" "===e.key)&&(e.preventDefault(),O("durationFrom"))},"aria-label":"Sort by duration",style:{width:"15%"},children:["Duration ",es("durationFrom")]}),(0,s.jsxs)(N.nd,{onClick:()=>O("status"),className:"cursor-pointer hover:bg-slate-200 transition-colors font-semibold text-gray-700","aria-sort":X("status"),role:"columnheader",tabIndex:0,onKeyDown:e=>{("Enter"===e.key||" "===e.key)&&(e.preventDefault(),O("status"))},"aria-label":"Sort by status",style:{width:"10%"},children:["Status ",es("status")]}),(0,s.jsxs)(N.nd,{onClick:()=>O("delegates"),className:"cursor-pointer hover:bg-slate-200 transition-colors font-semibold text-gray-700","aria-sort":X("delegates"),role:"columnheader",tabIndex:0,onKeyDown:e=>{("Enter"===e.key||" "===e.key)&&(e.preventDefault(),O("delegates"))},"aria-label":"Sort by number of delegates",style:{width:"30%"},children:["Delegates ",es("delegates")]})]})}),(0,s.jsx)(N.BF,{children:ee.map((e,t)=>(0,s.jsxs)(N.Hj,{className:(0,f.cn)("page-break-inside-avoid hover:bg-slate-50 transition-colors border-b border-gray-100",t%2==0?"bg-white":"bg-slate-25"),children:[(0,s.jsx)(N.nA,{className:"font-medium print-text-wrap py-4 px-4",title:e.eventName,children:(0,s.jsx)("div",{className:"jsx-647059bd56a41866 font-semibold text-gray-800",children:e.eventName})}),(0,s.jsx)(N.nA,{className:"print-text-wrap print-location-col py-4 px-4",title:e.location,children:(0,s.jsxs)("div",{className:"jsx-647059bd56a41866 flex items-center text-gray-600",children:[(0,s.jsx)(p.A,{className:"h-4 w-4 mr-2 text-gray-400"}),e.location]})}),(0,s.jsx)(N.nA,{className:"whitespace-nowrap py-4 px-4",children:(0,s.jsxs)("div",{className:"jsx-647059bd56a41866 flex items-center text-gray-600",children:[(0,s.jsx)(x.A,{className:"h-4 w-4 mr-2 text-gray-400"}),(0,s.jsxs)("div",{className:"jsx-647059bd56a41866 text-sm",children:[(0,s.jsx)("div",{className:"jsx-647059bd56a41866",children:q(e.durationFrom)}),(0,s.jsxs)("div",{className:"jsx-647059bd56a41866 text-xs text-gray-500",children:["to ",q(e.durationTo)]})]})]})}),(0,s.jsx)(N.nA,{className:"py-4 px-4",children:(0,s.jsx)(j.E,{className:(0,f.cn)("text-xs py-1 px-2 font-medium",M(e.status)),children:(0,y.fZ)(e.status)})}),(0,s.jsx)(N.nA,{className:"max-w-xs print-text-wrap py-4 px-4",title:e.delegates.map(e=>e.name).join(", "),children:e.delegates.length>0?(0,s.jsxs)("div",{className:"jsx-647059bd56a41866 flex items-start",children:[(0,s.jsx)(h.A,{className:"h-4 w-4 mr-2 text-gray-400 mt-0.5 flex-shrink-0"}),(0,s.jsxs)("div",{className:"jsx-647059bd56a41866",children:[(0,s.jsx)("span",{className:"jsx-647059bd56a41866 no-print",children:e.delegates.length<=3?(0,s.jsx)("div",{className:"jsx-647059bd56a41866 space-y-1",children:e.delegates.map((e,t)=>(0,s.jsx)("div",{className:"jsx-647059bd56a41866 text-sm text-gray-700",children:e.name},t))}):(0,s.jsxs)("div",{className:"jsx-647059bd56a41866",children:[(0,s.jsx)("div",{className:"jsx-647059bd56a41866 space-y-1 mb-1",children:e.delegates.slice(0,2).map((e,t)=>(0,s.jsx)("div",{className:"jsx-647059bd56a41866 text-sm text-gray-700",children:e.name},t))}),(0,s.jsxs)("span",{className:"jsx-647059bd56a41866 text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded",children:["+",e.delegates.length-2," ","more"]})]})}),(0,s.jsx)("span",{className:"jsx-647059bd56a41866 print-only",children:(0,s.jsx)("div",{className:"jsx-647059bd56a41866 space-y-1",children:e.delegates.map((e,t)=>(0,s.jsx)("div",{className:"jsx-647059bd56a41866 text-sm",children:e.name},t))})})]})]}):(0,s.jsx)("span",{className:"jsx-647059bd56a41866 text-gray-400 text-sm",children:"No delegates"})})]},e.id))})]})})})}),a.length>Z&&(0,s.jsx)("div",{className:"jsx-647059bd56a41866 flex justify-center mt-8 no-print",children:(0,s.jsx)(T.$o,{currentPage:E,totalPages:et,onPageChange:ea})})]}),(0,s.jsx)("footer",{className:"jsx-647059bd56a41866 mt-12 pt-6 border-t border-gray-200 text-center text-sm text-gray-500 report-footer",children:(0,s.jsxs)("div",{className:"jsx-647059bd56a41866 space-y-2",children:[(0,s.jsxs)("p",{className:"jsx-647059bd56a41866 font-medium",children:["Report generated on: ",new Date().toLocaleDateString()," ",new Date().toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})]}),(0,s.jsx)("p",{className:"jsx-647059bd56a41866 text-gray-400",children:"WorkHub - Delegation Management"}),(0,s.jsx)("p",{className:"jsx-647059bd56a41866 report-page-number",children:"Page "}),(0,s.jsx)("p",{className:"jsx-647059bd56a41866 print-only text-xs",children:"Confidential - For internal use only"})]})})]}),(0,s.jsx)(l(),{id:"647059bd56a41866",children:".print-text-wrap{word-break:break-word;white-space:normal!important}@media(max-width:640px){.delegations-table-container{overflow-x:auto}.filter-grid{grid-template-columns:1fr}.summary-grid{grid-template-columns:1fr 1fr}}@media print{#delegations-list-report-content{width:100%!important;max-width:100%!important}#delegations-table tr:nth-child(even){background-color:#f9f9f9!important}#delegations-table .print-location-col{max-width:50mm!important;word-break:break-word!important}#delegations-table th:first-child,#delegations-table td:first-child{width:25%!important}#delegations-table th:nth-child(2),#delegations-table td:nth-child(2){width:20%!important}#delegations-table th:nth-child(3),#delegations-table td:nth-child(3){width:20%!important;white-space:nowrap!important}#delegations-table th:nth-child(4),#delegations-table td:nth-child(4){width:10%!important}#delegations-table th:nth-child(5),#delegations-table td:nth-child(5){width:25%!important}}"})]})}function L(){return(0,s.jsx)(i.Suspense,{fallback:(0,s.jsx)("div",{className:"text-center py-10",children:"Loading report..."}),children:(0,s.jsx)(_,{})})}},41936:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(82614).A)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},48206:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(82614).A)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},48344:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>s});let s=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\projects\\\\CarServiceTrackingSystem\\\\TrackerSystem\\\\frontend\\\\src\\\\app\\\\delegations\\\\report\\\\list\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\delegations\\report\\list\\page.tsx","default")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},57822:(e,t,a)=>{Promise.resolve().then(a.bind(a,37859))},62550:(e,t,a)=>{Promise.resolve().then(a.bind(a,48344))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},78691:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>l,metadata:()=>r});var s=a(37413);let r={title:"Delegation List Report"};function l({children:e}){return(0,s.jsx)(s.Fragment,{children:e})}},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},92876:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(82614).A)("CalendarDays",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}],["path",{d:"M8 14h.01",key:"6423bh"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 18h.01",key:"lrp35t"}],["path",{d:"M12 18h.01",key:"mhygvu"}],["path",{d:"M16 18h.01",key:"kzsmim"}]])},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),s=t.X(0,[4447,3622,1658,5880,2729,6055,9584,8141,3983,4318],()=>a(35165));module.exports=s})();