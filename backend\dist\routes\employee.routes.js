import { Router } from 'express';
import * as employeeController from '../controllers/employee.controller.js';
import { validate } from '../middleware/validation.js';
import { authenticateSupabaseUser, requireRole, } from '../middleware/supabaseAuth.js';
import { employeeCreateSchema, employeeUpdateSchema, employeeIdSchema, } from '../schemas/employee.schema.js';
const router = Router();
// 🚨 EMERGENCY SECURITY: All employee routes require authentication
// POST /api/employees - Create a new employee (ADMIN only)
router.post('/', authenticateSupabaseUser, requireRole(['ADMIN', 'SUPER_ADMIN']), validate(employeeCreateSchema), employeeController.createEmployee);
// GET /api/employees - Get all employees (MANAGER+ can see all, <PERSON><PERSON> sees limited)
router.get('/', authenticateSupabaseUser, employeeController.getAllEmployees);
// GET /api/employees/enriched - Get all employees enriched with vehicle information (MANAGER+)
router.get('/enriched', authenticateSupabaseUser, requireRole(['MANAGER', 'ADMIN', 'SUPER_ADMIN']), employeeController.getEnrichedEmployees);
// GET /api/employees/:id - Get a specific employee by ID (authenticated users)
router.get('/:id', authenticateSupabaseUser, validate(employeeIdSchema, 'params'), employeeController.getEmployeeById);
// PUT /api/employees/:id - Update a specific employee by ID (MANAGER+ or own record)
router.put('/:id', authenticateSupabaseUser, validate(employeeIdSchema, 'params'), validate(employeeUpdateSchema), employeeController.updateEmployee);
// DELETE /api/employees/:id - Delete a specific employee by ID (ADMIN only)
router.delete('/:id', authenticateSupabaseUser, requireRole(['ADMIN', 'SUPER_ADMIN']), validate(employeeIdSchema, 'params'), employeeController.deleteEmployee);
export default router;
//# sourceMappingURL=employee.routes.js.map