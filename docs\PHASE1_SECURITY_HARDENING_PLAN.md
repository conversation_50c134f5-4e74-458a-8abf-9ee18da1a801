# Phase 1: Immediate Security Hardening Implementation Plan

## 📋 Overview

With **Phase 0: Emergency Security Foundation** now **100% complete**, this document outlines the implementation plan for **Phase 1: Immediate Security Hardening (Days 3-5)** as defined in the Security Enhancement Plan V3.

## 🎯 Phase 1 Objectives

- **Harden Docker containers** with non-root users and security best practices
- **Implement proper secrets management** with strong generated secrets
- **Add security headers** using Helmet.js for comprehensive protection
- **Enhance input validation** with sanitization for XSS prevention
- **Implement rate limiting** to prevent abuse and DoS attacks

## 📊 Current Security Status

### **✅ Phase 0 Achievements**
- **Hybrid RBAC System**: 100% functional with JWT custom claims
- **Database Security**: RLS policies active, anonymous access revoked
- **API Protection**: All endpoints secured with authentication
- **Frontend Security**: Complete authentication flow implemented
- **Testing**: Comprehensive security validation completed

### **🎯 Phase 1 Targets**
- **Docker Security**: Non-root containers with hardened configurations
- **Secrets Management**: Strong secrets with proper environment handling
- **Security Headers**: Comprehensive HTTP security headers
- **Input Validation**: Enhanced sanitization and validation
- **Rate Limiting**: API protection against abuse

## 🐳 Task 1.1: Docker Security Fixes

### **Current Docker Issues**
- Containers running as root user (security risk)
- No security updates in base images
- Missing proper signal handling
- Exposed credentials in logs

### **Implementation Steps**

#### **Step 1: Harden Backend Dockerfile**

```dockerfile
# backend/Dockerfile - SECURITY HARDENED VERSION
FROM node:18-alpine AS builder

# Security: Install security updates
RUN apk update && apk upgrade && \
    apk add --no-cache dumb-init && \
    rm -rf /var/cache/apk/*

# Security: Create non-root user
RUN addgroup -g 1001 -S nodejs && \
    adduser -S workhub -u 1001

WORKDIR /app

# Copy package files with proper ownership
COPY --chown=workhub:nodejs package*.json ./
RUN npm ci --only=production && npm cache clean --force

# Production stage
FROM node:18-alpine AS runtime

# Security: Install security updates and dumb-init
RUN apk update && apk upgrade && \
    apk add --no-cache dumb-init && \
    rm -rf /var/cache/apk/*

# Security: Create non-root user
RUN addgroup -g 1001 -S nodejs && \
    adduser -S workhub -u 1001

WORKDIR /app

# Security: Copy files with proper ownership
COPY --from=builder --chown=workhub:nodejs /app/node_modules ./node_modules
COPY --chown=workhub:nodejs . .

# Security: Build application
RUN npm run build

# Security: Remove development dependencies and package manager
RUN npm prune --production && \
    apk del apk-tools

# Security: Switch to non-root user
USER workhub

# Security: Use dumb-init for proper signal handling
ENTRYPOINT ["dumb-init", "--"]

# Security: Don't expose sensitive information in logs
CMD ["node", "dist/server.js"]

# Security: Add labels for scanning
LABEL security.scan="enabled"
LABEL security.last-updated="2025-01-24"
LABEL security.non-root="true"
```

#### **Step 2: Harden Frontend Dockerfile**

```dockerfile
# frontend/Dockerfile - SECURITY HARDENED VERSION
FROM node:18-alpine AS builder

# Security: Install security updates
RUN apk update && apk upgrade && rm -rf /var/cache/apk/*

# Security: Create non-root user
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nextjs -u 1001

WORKDIR /app

# Copy package files
COPY package*.json ./
RUN npm ci --only=production && npm cache clean --force

# Copy source and build
COPY . .
RUN npm run build

# Production stage
FROM node:18-alpine AS runtime

# Security: Install security updates and dumb-init
RUN apk update && apk upgrade && \
    apk add --no-cache dumb-init && \
    rm -rf /var/cache/apk/*

# Security: Create non-root user
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nextjs -u 1001

WORKDIR /app

# Security: Copy built application with proper ownership
COPY --from=builder --chown=nextjs:nodejs /app/.next ./.next
COPY --from=builder --chown=nextjs:nodejs /app/node_modules ./node_modules
COPY --from=builder --chown=nextjs:nodejs /app/package.json ./package.json
COPY --from=builder --chown=nextjs:nodejs /app/public ./public

# Security: Remove package manager
RUN apk del apk-tools

# Security: Switch to non-root user
USER nextjs

# Security: Use dumb-init for proper signal handling
ENTRYPOINT ["dumb-init", "--"]

# Expose port
EXPOSE 3000

# Security: Start application
CMD ["npm", "start"]

# Security: Add labels
LABEL security.scan="enabled"
LABEL security.last-updated="2025-01-24"
LABEL security.non-root="true"
```

## 🔐 Task 1.2: Secrets Management

### **Current Issues**
- Weak or default secrets
- Hardcoded credentials in configuration
- Insecure environment variable handling

### **Implementation Steps**

#### **Step 1: Generate Strong Secrets**

```bash
# Generate strong secrets
openssl rand -base64 32  # For JWT_SECRET
openssl rand -base64 32  # For API_SECRET
openssl rand -base64 32  # For SESSION_SECRET
```

#### **Step 2: Update Environment Configuration**

```bash
# backend/.env.example - SECURE VERSION
# Database (Supabase)
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-anon-key-here
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key-here

# Application
NODE_ENV=production
PORT=3001

# Security: Strong generated secrets (REQUIRED)
JWT_SECRET=GENERATE_32_CHAR_SECRET_IMMEDIATELY
API_SECRET=GENERATE_32_CHAR_SECRET_IMMEDIATELY
SESSION_SECRET=GENERATE_32_CHAR_SECRET_IMMEDIATELY

# Security: Remove any hardcoded credentials
# DATABASE_URL=REMOVED_FOR_SECURITY

# Security: Rate limiting
RATE_LIMIT_WINDOW_MS=900000  # 15 minutes
RATE_LIMIT_MAX_REQUESTS=100  # requests per window

# Security: CORS origins (production)
ALLOWED_ORIGINS=https://your-production-domain.com,https://your-staging-domain.com
```

#### **Step 3: Implement Secrets Validation**

```javascript
// backend/src/config/secrets.js
import crypto from 'crypto';

const requiredSecrets = [
  'SUPABASE_URL',
  'SUPABASE_ANON_KEY', 
  'SUPABASE_SERVICE_ROLE_KEY',
  'JWT_SECRET',
  'API_SECRET'
];

export const validateSecrets = () => {
  const missing = [];
  const weak = [];

  for (const secret of requiredSecrets) {
    const value = process.env[secret];
    
    if (!value) {
      missing.push(secret);
    } else if (secret.includes('SECRET') && value.length < 32) {
      weak.push(secret);
    }
  }

  if (missing.length > 0) {
    throw new Error(`Missing required secrets: ${missing.join(', ')}`);
  }

  if (weak.length > 0) {
    throw new Error(`Weak secrets detected (must be 32+ chars): ${weak.join(', ')}`);
  }

  console.log('✅ All secrets validated successfully');
};

// Validate on startup
validateSecrets();
```

## 🛡️ Task 1.3: Security Headers Implementation

### **Implementation Steps**

#### **Step 1: Install Helmet.js**

```bash
cd backend
npm install helmet
```

#### **Step 2: Configure Security Headers**

```javascript
// backend/src/middleware/security.js
import helmet from 'helmet';

export const securityHeaders = helmet({
  // Content Security Policy
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      scriptSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"], // For CSS frameworks
      imgSrc: ["'self'", 'data:', 'https:'],
      connectSrc: ["'self'", process.env.SUPABASE_URL],
      fontSrc: ["'self'"],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'"],
      frameSrc: ["'none'"],
      baseUri: ["'self'"],
      formAction: ["'self'"],
    },
  },
  
  // HTTP Strict Transport Security
  hsts: {
    maxAge: 31536000, // 1 year
    includeSubDomains: true,
    preload: true,
  },
  
  // X-Frame-Options
  frameguard: { action: 'deny' },
  
  // X-Content-Type-Options
  noSniff: true,
  
  // X-XSS-Protection
  xssFilter: true,
  
  // Referrer Policy
  referrerPolicy: { policy: 'same-origin' },
  
  // Cross-Origin Embedder Policy
  crossOriginEmbedderPolicy: false, // Disable if causing issues
  
  // Cross-Origin Resource Policy
  crossOriginResourcePolicy: { policy: 'cross-origin' },
  
  // Cross-Origin Opener Policy
  crossOriginOpenerPolicy: { policy: 'same-origin-allow-popups' },
});

// Additional security middleware
export const additionalSecurity = (req, res, next) => {
  // Remove X-Powered-By header
  res.removeHeader('X-Powered-By');
  
  // Add custom security headers
  res.setHeader('X-API-Version', '1.0');
  res.setHeader('X-Security-Level', 'HIGH');
  
  next();
};
```

#### **Step 3: Apply Security Headers**

```javascript
// backend/src/app.ts - Add security headers
import { securityHeaders, additionalSecurity } from './middleware/security.js';

// Apply security headers early in middleware chain
app.use(securityHeaders);
app.use(additionalSecurity);
```

## 🔍 Task 1.4: Enhanced Input Validation

### **Implementation Steps**

#### **Step 1: Install Validation Dependencies**

```bash
cd backend
npm install dompurify jsdom
```

#### **Step 2: Create Enhanced Validation Middleware**

```javascript
// backend/src/middleware/validation.js
import { z } from 'zod';
import DOMPurify from 'dompurify';
import { JSDOM } from 'jsdom';

// Create DOMPurify instance
const window = new JSDOM('').window;
const purify = DOMPurify(window);

// Fields that may contain HTML and need sanitization
const HTML_FIELDS = ['description', 'notes', 'comments', 'name', 'details'];

const sanitizeInput = (value, fieldName) => {
  if (typeof value !== 'string') return value;

  // Basic sanitization for all strings
  let sanitized = value.trim().slice(0, 10000); // Length limit

  // HTML sanitization for specific fields
  if (HTML_FIELDS.includes(fieldName.toLowerCase())) {
    sanitized = purify.sanitize(sanitized, {
      ALLOWED_TAGS: [], // No HTML tags allowed
      ALLOWED_ATTR: [],
      KEEP_CONTENT: true,
    });
  }

  // Remove potential script injections
  sanitized = sanitized.replace(
    /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
    ''
  );

  return sanitized;
};

export const validateRequest = (schema) => {
  return (req, res, next) => {
    try {
      // Validate with Zod first
      const validatedData = schema.parse({
        body: req.body,
        params: req.params,
        query: req.query,
      });

      // Apply sanitization
      req.validatedData = sanitizeRequestData(validatedData);
      next();
    } catch (error) {
      res.status(400).json({
        error: 'Validation failed',
        details: error.errors,
        code: 'VALIDATION_ERROR',
      });
    }
  };
};

const sanitizeRequestData = (data) => {
  if (typeof data === 'string') {
    return sanitizeInput(data, 'generic');
  }
  if (Array.isArray(data)) {
    return data.slice(0, 1000).map(sanitizeRequestData); // Limit array size
  }
  if (data && typeof data === 'object') {
    const sanitized = {};
    for (const [key, value] of Object.entries(data)) {
      sanitized[key] =
        typeof value === 'string'
          ? sanitizeInput(value, key)
          : sanitizeRequestData(value);
    }
    return sanitized;
  }
  return data;
};
```

## ⚡ Task 1.5: Rate Limiting Implementation

### **Implementation Steps**

#### **Step 1: Install Rate Limiting**

```bash
cd backend
npm install express-rate-limit express-slow-down
```

#### **Step 2: Configure Rate Limiting**

```javascript
// backend/src/middleware/rateLimiting.js
import rateLimit from 'express-rate-limit';
import slowDown from 'express-slow-down';

// General API rate limiting
export const generalRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // Limit each IP to 100 requests per windowMs
  message: {
    error: 'Too many requests',
    code: 'RATE_LIMIT_EXCEEDED',
    retryAfter: '15 minutes',
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Strict rate limiting for authentication endpoints
export const authRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // Limit each IP to 5 auth requests per windowMs
  message: {
    error: 'Too many authentication attempts',
    code: 'AUTH_RATE_LIMIT_EXCEEDED',
    retryAfter: '15 minutes',
  },
  skipSuccessfulRequests: true,
});

// Admin endpoints rate limiting
export const adminRateLimit = rateLimit({
  windowMs: 5 * 60 * 1000, // 5 minutes
  max: 20, // Limit admin operations
  message: {
    error: 'Too many admin requests',
    code: 'ADMIN_RATE_LIMIT_EXCEEDED',
    retryAfter: '5 minutes',
  },
});

// Speed limiting (slow down responses)
export const speedLimiter = slowDown({
  windowMs: 15 * 60 * 1000, // 15 minutes
  delayAfter: 50, // Allow 50 requests per windowMs without delay
  delayMs: 500, // Add 500ms delay per request after delayAfter
  maxDelayMs: 20000, // Maximum delay of 20 seconds
});
```

#### **Step 3: Apply Rate Limiting**

```javascript
// backend/src/app.ts - Apply rate limiting
import { 
  generalRateLimit, 
  authRateLimit, 
  adminRateLimit, 
  speedLimiter 
} from './middleware/rateLimiting.js';

// Apply general rate limiting and speed limiting
app.use(generalRateLimit);
app.use(speedLimiter);

// Apply specific rate limiting to auth routes
app.use('/api/auth', authRateLimit);

// Apply admin rate limiting
app.use('/api/admin', adminRateLimit);
```

## 📊 Phase 1 Success Criteria

### **Completion Checklist**
- [ ] **Docker Security**: Containers running as non-root user
- [ ] **Secrets Management**: Strong secrets generated and validated
- [ ] **Security Headers**: Helmet.js implemented with comprehensive headers
- [ ] **Input Validation**: Enhanced sanitization with DOMPurify
- [ ] **Rate Limiting**: API protection against abuse implemented

### **Testing Requirements**
- [ ] **Docker**: Verify containers run as non-root user
- [ ] **Secrets**: Validate all secrets meet strength requirements
- [ ] **Headers**: Verify security headers in HTTP responses
- [ ] **Validation**: Test XSS prevention with malicious inputs
- [ ] **Rate Limiting**: Verify rate limits trigger correctly

## 🎯 Next Steps

After completing Phase 1:
1. **Deploy hardened system** to staging
2. **Conduct security testing** with hardened configuration
3. **Prepare for Phase 2**: Advanced Security (Week 2)
4. **Plan production deployment** with full security stack

---

**Document Version**: 1.0  
**Created**: January 24, 2025  
**Phase**: 1 - Immediate Security Hardening  
**Timeline**: Days 3-5 (3 days implementation)
